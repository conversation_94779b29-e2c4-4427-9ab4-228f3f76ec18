import { Model, Optional } from 'sequelize';
interface AssetTypeAttributes {
    id: string;
    name: string;
    status: boolean;
    createdAt: Date;
    updatedAt: Date;
}
interface AssetTypeCreationAttributes extends Optional<AssetTypeAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
declare class AssetType extends Model<AssetTypeAttributes, AssetTypeCreationAttributes> implements AssetTypeAttributes {
    id: string;
    name: string;
    status: boolean;
    readonly createdAt: Date;
    readonly updatedAt: Date;
}
export default AssetType;
