"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const uuid_1 = require("uuid");
const AssetType_1 = __importDefault(require("../models/AssetType"));
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// Get all asset types
router.get('/', auth_1.authenticateToken, async (req, res) => {
    try {
        const assetTypes = await AssetType_1.default.findAll({
            order: [['name', 'ASC']]
        });
        res.json({
            success: true,
            data: {
                assetTypes: assetTypes,
                count: assetTypes.length
            }
        });
    }
    catch (error) {
        console.error('Get asset types error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get asset type by ID
router.get('/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        const assetType = await AssetType_1.default.findByPk(req.params.id);
        if (!assetType) {
            res.status(404).json({
                success: false,
                message: 'Asset type not found'
            });
            return;
        }
        res.json({
            success: true,
            data: { assetType }
        });
    }
    catch (error) {
        console.error('Get asset type error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Create new asset type
router.post('/', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const { name, status = true } = req.body;
        // Validate input
        if (!name || !name.trim()) {
            res.status(400).json({
                success: false,
                message: 'Asset type name is required'
            });
            return;
        }
        // Check if asset type already exists
        const existingAssetType = await AssetType_1.default.findOne({
            where: { name: name.trim() }
        });
        if (existingAssetType) {
            res.status(409).json({
                success: false,
                message: 'Asset type with this name already exists'
            });
            return;
        }
        // Create new asset type
        const assetType = await AssetType_1.default.create({
            id: (0, uuid_1.v4)(),
            name: name.trim(),
            status: Boolean(status)
        });
        res.status(201).json({
            success: true,
            message: 'Asset type created successfully',
            data: { assetType }
        });
    }
    catch (error) {
        console.error('Create asset type error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Update asset type
router.put('/:id', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const { name, status } = req.body;
        const assetType = await AssetType_1.default.findByPk(req.params.id);
        if (!assetType) {
            res.status(404).json({
                success: false,
                message: 'Asset type not found'
            });
            return;
        }
        // Validate input
        if (!name || !name.trim()) {
            res.status(400).json({
                success: false,
                message: 'Asset type name is required'
            });
            return;
        }
        // Check if another asset type with the same name exists (excluding current one)
        const existingAssetType = await AssetType_1.default.findOne({
            where: {
                name: name.trim(),
                id: { [require('sequelize').Op.ne]: req.params.id }
            }
        });
        if (existingAssetType) {
            res.status(409).json({
                success: false,
                message: 'Asset type with this name already exists'
            });
            return;
        }
        // Update asset type
        await assetType.update({
            name: name.trim(),
            status: status !== undefined ? Boolean(status) : assetType.status
        });
        res.json({
            success: true,
            message: 'Asset type updated successfully',
            data: { assetType }
        });
    }
    catch (error) {
        console.error('Update asset type error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Delete asset type
router.delete('/:id', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const assetType = await AssetType_1.default.findByPk(req.params.id);
        if (!assetType) {
            res.status(404).json({
                success: false,
                message: 'Asset type not found'
            });
            return;
        }
        await assetType.destroy();
        res.json({
            success: true,
            message: 'Asset type deleted successfully'
        });
    }
    catch (error) {
        console.error('Delete asset type error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
