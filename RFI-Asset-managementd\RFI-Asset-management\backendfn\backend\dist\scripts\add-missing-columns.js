"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addMissingColumns = addMissingColumns;
// src/scripts/add-missing-columns.ts
const database_1 = require("../config/database");
const sequelize_1 = require("sequelize");
async function addMissingColumns() {
    try {
        console.log('🔄 Starting to add missing columns...');
        const queryInterface = database_1.sequelize.getQueryInterface();
        // Check database connection
        await database_1.sequelize.authenticate();
        console.log('✅ Database connection established.');
        // Check if the assets table exists
        const tables = await queryInterface.showAllTables();
        if (!tables.includes('assets')) {
            console.log('❌ Assets table does not exist. Please run create-tables.ts first.');
            return;
        }
        // Check current columns in assets table
        const columns = await queryInterface.describeTable('assets');
        console.log('📋 Current columns in assets table:', Object.keys(columns));
        // Add missing 'type' column if it doesn't exist
        if (!columns.type) {
            console.log('📝 Adding missing "type" column to assets table...');
            await queryInterface.addColumn('assets', 'type', {
                type: sequelize_1.DataTypes.STRING,
                allowNull: false,
                defaultValue: 'Electronic Voting Machine' // Temporary default
            });
            console.log('✅ "type" column added successfully.');
        }
        else {
            console.log('ℹ️ "type" column already exists.');
        }
        // Add other missing columns if needed
        if (!columns.model) {
            console.log('📝 Adding missing "model" column to assets table...');
            await queryInterface.addColumn('assets', 'model', {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true
            });
            console.log('✅ "model" column added successfully.');
        }
        else {
            console.log('ℹ️ "model" column already exists.');
        }
        if (!columns.county) {
            console.log('📝 Adding missing "county" column to assets table...');
            await queryInterface.addColumn('assets', 'county', {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            });
            console.log('✅ "county" column added successfully.');
        }
        else {
            console.log('ℹ️ "county" column already exists.');
        }
        if (!columns.precinct) {
            console.log('📝 Adding missing "precinct" column to assets table...');
            await queryInterface.addColumn('assets', 'precinct', {
                type: sequelize_1.DataTypes.STRING,
                allowNull: true,
            });
            console.log('✅ "precinct" column added successfully.');
        }
        else {
            console.log('ℹ️ "precinct" column already exists.');
        }
        if (!columns.warranty_expiry) {
            console.log('📝 Adding missing "warranty_expiry" column to assets table...');
            await queryInterface.addColumn('assets', 'warranty_expiry', {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            });
            console.log('✅ "warranty_expiry" column added successfully.');
        }
        else {
            console.log('ℹ️ "warranty_expiry" column already exists.');
        }
        if (!columns.last_checked) {
            console.log('📝 Adding missing "last_checked" column to assets table...');
            await queryInterface.addColumn('assets', 'last_checked', {
                type: sequelize_1.DataTypes.DATE,
                allowNull: true,
            });
            console.log('✅ "last_checked" column added successfully.');
        }
        else {
            console.log('ℹ️ "last_checked" column already exists.');
        }
        if (!columns.specifications) {
            console.log('📝 Adding missing "specifications" column to assets table...');
            await queryInterface.addColumn('assets', 'specifications', {
                type: sequelize_1.DataTypes.TEXT,
                allowNull: true,
            });
            console.log('✅ "specifications" column added successfully.');
        }
        else {
            console.log('ℹ️ "specifications" column already exists.');
        }
        // Update the status and condition columns to match the model enums
        console.log('📝 Updating status and condition columns...');
        // Check if status column needs updating
        if (columns.status && columns.status.type !== "ENUM('Available','In Transit','Deployed','Damaged','Under Repair','Retired')") {
            console.log('📝 Updating status column enum values...');
            await queryInterface.changeColumn('assets', 'status', {
                type: sequelize_1.DataTypes.ENUM('Available', 'In Transit', 'Deployed', 'Damaged', 'Under Repair', 'Retired'),
                allowNull: false,
                defaultValue: 'Available'
            });
            console.log('✅ Status column updated successfully.');
        }
        // Check if condition column needs updating
        if (columns.condition && columns.condition.type !== "ENUM('Excellent','Good','Fair','Poor','Damaged')") {
            console.log('📝 Updating condition column enum values...');
            await queryInterface.changeColumn('assets', 'condition', {
                type: sequelize_1.DataTypes.ENUM('Excellent', 'Good', 'Fair', 'Poor', 'Damaged'),
                allowNull: false,
                defaultValue: 'Good'
            });
            console.log('✅ Condition column updated successfully.');
        }
        // Show final table structure
        const finalColumns = await queryInterface.describeTable('assets');
        console.log('📋 Final columns in assets table:', Object.keys(finalColumns));
        console.log('🎉 All missing columns added successfully!');
    }
    catch (error) {
        console.error('❌ Error adding missing columns:', error);
    }
    finally {
        await database_1.sequelize.close();
    }
}
// Run the script
if (require.main === module) {
    addMissingColumns();
}
