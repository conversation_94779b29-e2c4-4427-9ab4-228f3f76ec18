import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PackingLocation, PackingLocationCreationData } from "@/services/packingLocationService";

// States list for dropdown
const states = [
  { value: "AL", label: "Alabama" },
  { value: "AK", label: "Alaska" },
  { value: "AZ", label: "Arizona" },
  { value: "AR", label: "Arkansas" },
  { value: "CA", label: "California" },
  { value: "CO", label: "Colorado" },
  { value: "CT", label: "Connecticut" },
  { value: "DE", label: "Delaware" },
  { value: "FL", label: "Florida" },
  { value: "GA", label: "Georgia" },
  { value: "HI", label: "Hawaii" },
  { value: "ID", label: "Idaho" },
  { value: "IL", label: "Illinois" },
  { value: "IN", label: "Indiana" },
  { value: "IA", label: "Iowa" },
  { value: "KS", label: "Kansas" },
  { value: "KY", label: "Kentucky" },
  { value: "LA", label: "Louisiana" },
  { value: "ME", label: "Maine" },
  { value: "MD", label: "Maryland" },
  { value: "MA", label: "Massachusetts" },
  { value: "MI", label: "Michigan" },
  { value: "MN", label: "Minnesota" },
  { value: "MS", label: "Mississippi" },
  { value: "MO", label: "Missouri" },
  { value: "MT", label: "Montana" },
  { value: "NE", label: "Nebraska" },
  { value: "NV", label: "Nevada" },
  { value: "NH", label: "New Hampshire" },
  { value: "NJ", label: "New Jersey" },
  { value: "NM", label: "New Mexico" },
  { value: "NY", label: "New York" },
  { value: "NC", label: "North Carolina" },
  { value: "ND", label: "North Dakota" },
  { value: "OH", label: "Ohio" },
  { value: "OK", label: "Oklahoma" },
  { value: "OR", label: "Oregon" },
  { value: "PA", label: "Pennsylvania" },
  { value: "RI", label: "Rhode Island" },
  { value: "SC", label: "South Carolina" },
  { value: "SD", label: "South Dakota" },
  { value: "TN", label: "Tennessee" },
  { value: "TX", label: "Texas" },
  { value: "UT", label: "Utah" },
  { value: "VT", label: "Vermont" },
  { value: "VA", label: "Virginia" },
  { value: "WA", label: "Washington" },
  { value: "WV", label: "West Virginia" },
  { value: "WI", label: "Wisconsin" },
  { value: "WY", label: "Wyoming" }
];

interface PackingLocationEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  packingLocation: PackingLocation | null;
  onSave: (packingLocation: PackingLocationCreationData) => void;
  isEditMode: boolean;
}

export function PackingLocationEditDialog({
  open,
  onOpenChange,
  packingLocation,
  onSave,
  isEditMode
}: PackingLocationEditDialogProps) {
  const [formData, setFormData] = useState<PackingLocationCreationData>({
    name: "",
    locationType: "",
    area: "",
    address: "",
    addressLine2: "",
    city: "",
    state: "",
    zip: "",
    status: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or data changes
  useEffect(() => {
    if (open) {
      if (packingLocation) {
        setFormData({
          name: packingLocation.name,
          locationType: packingLocation.locationType || "",
          area: packingLocation.area || "",
          address: packingLocation.address || "",
          addressLine2: packingLocation.addressLine2 || "",
          city: packingLocation.city || "",
          state: packingLocation.state || "",
          zip: packingLocation.zip || "",
          status: packingLocation.status
        });
      } else {
        // Reset form for new entry with default values for hidden fields
        setFormData({
          name: "",
          locationType: "",
          area: "",
          address: "",
          addressLine2: "",
          city: "",
          state: "",
          zip: "",
          status: true
        });
      }
      setErrors({});
    }
  }, [packingLocation, open]);

  // Handle input change
  const handleInputChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: ""
      });
    }
  };

  // Handle status change
  const handleStatusChange = (checked: boolean) => {
    setFormData({
      ...formData,
      status: checked
    });
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.locationType?.trim()) {
      newErrors.locationType = "Location Type is required";
    } else if (formData.locationType.trim().length < 2) {
      newErrors.locationType = "Location Type must be at least 2 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      // Generate a default name based on location type if no name is provided
      const defaultName = formData.name.trim() || formData.locationType.trim();

      onSave({
        name: defaultName,
        locationType: formData.locationType,
        area: formData.area?.trim() || "",
        address: formData.address?.trim() || "",
        addressLine2: formData.addressLine2?.trim() || "",
        city: formData.city?.trim() || "",
        state: formData.state || "",
        zip: formData.zip?.trim() || "",
        status: formData.status
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-xl">
            {isEditMode ? "Edit Location" : "Add Location"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 gap-4 py-4">
            {/* Location Type */}
            <div className="space-y-2">
              <Label htmlFor="type" className="text-sm font-medium flex items-center">
                Location Type <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="type"
                value={formData.locationType || ""}
                onChange={(e) => handleInputChange("locationType", e.target.value)}
                placeholder="Enter location type (e.g., Warehouse, Office)"
                className={errors.locationType ? "border-red-500" : ""}
              />
              {errors.locationType && <p className="text-xs text-red-500">{errors.locationType}</p>}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="status" className="text-sm font-medium flex items-center">
                Status <span className="text-red-500 ml-1">*</span>
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="status"
                  checked={formData.status}
                  onCheckedChange={handleStatusChange}
                />
                <span className={`text-sm ${formData.status ? "text-green-600" : "text-red-600"}`}>
                  {formData.status ? "Active" : "Inactive"}
                </span>
              </div>
            </div>
          </div>

          <DialogFooter className="flex justify-end space-x-3 mt-6 pt-2 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              {isEditMode ? "Save Changes" : "Add Location"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}