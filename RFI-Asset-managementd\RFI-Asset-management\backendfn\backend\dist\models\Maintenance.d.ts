import { Model, Optional } from 'sequelize';
export interface MaintenanceAttributes {
    assetTag: string;
    category: 'AV computers' | 'AV printers' | 'tabs' | 'others' | 'scanners' | 'pollpads' | 'monitors';
    name: string;
    requestType: 'preventive' | 'corrective' | 'emergency';
    priority: 'high' | 'low' | 'medium' | 'critical';
    assignedTechnician?: string;
    description?: string;
    scheduledDate?: Date;
    scheduledFrequency?: 'once' | 'weekly' | 'monthly' | 'quarterly' | 'half-yearly' | 'yearly';
    estimatedHours?: number;
    estimatedCost?: number;
    notes?: string;
    status: 'REQUESTED' | 'SCHEDULED' | 'IN_PROGRESS' | 'TESTING' | 'COMPLETED';
    createdAt?: Date;
    updatedAt?: Date;
}
export interface MaintenanceCreationAttributes extends Optional<MaintenanceAttributes, 'assignedTechnician' | 'description' | 'scheduledDate' | 'scheduledFrequency' | 'estimatedHours' | 'estimatedCost' | 'notes' | 'createdAt' | 'updatedAt'> {
}
declare class Maintenance extends Model<MaintenanceAttributes, MaintenanceCreationAttributes> implements MaintenanceAttributes {
    assetTag: string;
    category: 'AV computers' | 'AV printers' | 'tabs' | 'others' | 'scanners' | 'pollpads' | 'monitors';
    name: string;
    requestType: 'preventive' | 'corrective' | 'emergency';
    priority: 'high' | 'low' | 'medium' | 'critical';
    assignedTechnician?: string;
    description?: string;
    scheduledDate?: Date;
    scheduledFrequency?: 'once' | 'weekly' | 'monthly' | 'quarterly' | 'half-yearly' | 'yearly';
    estimatedHours?: number;
    estimatedCost?: number;
    notes?: string;
    status: 'REQUESTED' | 'SCHEDULED' | 'IN_PROGRESS' | 'TESTING' | 'COMPLETED';
    readonly createdAt: Date;
    readonly updatedAt: Date;
    isOverdue(): boolean;
    getPriorityWeight(): number;
    getDaysUntilScheduled(): number | null;
}
export default Maintenance;
