"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// src/scripts/test-connection.ts
const database_1 = require("../config/database");
async function testConnection() {
    try {
        console.log('🔄 Testing database connection...');
        console.log('');
        console.log('📊 Connection Details:');
        console.log(`  Host: ${process.env.DB_HOST || '**************'}`);
        console.log(`  Port: ${process.env.DB_PORT || '3306'}`);
        console.log(`  Database: ${process.env.DB_NAME || 'assetmanagementool'}`);
        console.log(`  Username: ${process.env.DB_USERNAME || 'ram'}`);
        console.log('');
        // Test authentication
        await database_1.sequelize.authenticate();
        console.log('✅ Database connection successful!');
        // Test query execution
        const [results] = await database_1.sequelize.query('SELECT 1 as test');
        console.log('✅ Query execution successful!');
        // Check if tables exist
        console.log('');
        console.log('🔍 Checking for existing tables...');
        const [tables] = await database_1.sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'assetmanagementool'}'
    `);
        if (Array.isArray(tables) && tables.length > 0) {
            console.log('📋 Found tables:');
            tables.forEach((table) => {
                console.log(`  - ${table.TABLE_NAME}`);
            });
        }
        else {
            console.log('⚠️ No tables found in database.');
            console.log('💡 Run "npm run create-tables" to create the required tables.');
        }
        console.log('');
        console.log('🎉 Database connection test completed successfully!');
    }
    catch (error) {
        console.error('❌ Database connection test failed!');
        console.error('');
        console.error('Error details:', error.message);
        if (error.original) {
            console.error('Original error:', error.original.message);
        }
        console.log('');
        console.log('🔧 Common solutions:');
        console.log('1. Check if MySQL server is running');
        console.log('2. Verify database credentials in .env file');
        console.log('3. Ensure the database exists');
        console.log('4. Check firewall/network connectivity');
        console.log('5. Verify user permissions');
        throw error;
    }
    finally {
        await database_1.sequelize.close();
    }
}
// Script execution
if (require.main === module) {
    testConnection()
        .then(() => {
        process.exit(0);
    })
        .catch((error) => {
        process.exit(1);
    });
}
exports.default = testConnection;
