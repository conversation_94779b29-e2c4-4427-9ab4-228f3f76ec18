import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Plus, Search, Eye, Edit, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AppLayout } from '@/components/layout/AppLayout';
import { OrderAddDialog } from '@/components/orders/OrderAddDialog';
import { OrderEditDialog } from '@/components/orders/OrderEditDialog';
import { OrderViewDialog } from '@/components/orders/OrderViewDialog';

// API service
const API_BASE_URL = 'http://localhost:5000/api';

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Order interface
interface Order {
  id: number;
  refNo: string;
  date: string;
  vendor: string;
  consumable: string;
  qty: number;
  unitCost: number;
  total: number;
  status: string;
  locationName?: string;
  Location?: string;
  location?: string;
  items?: Array<{
    id: number;
    name: string;
    quantity: number;
    unitCost: number;
    amount: number;
  }>;
}

const mapOrderForDialog = (order: any) => {
  try {
    console.log("🔄 mapOrderForDialog input:", order);
    const mapped = {
      ...order,
      id: typeof order.id === "string" ? parseInt(order.id, 10) : order.id,
      // Do NOT add otherCosts, subtotal, createdBy here for edit dialog
    };
    console.log("🔄 mapOrderForDialog output:", mapped);
    return mapped;
  } catch (error) {
    console.error("❌ Error in mapOrderForDialog:", error);
    return order; // Return original order if mapping fails
  }
};

export default function OrdersPage() {
  // State management
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Dialog states
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  console.log("🔍 OrdersPage rendering, loading:", loading, "orders:", orders.length);
  console.log("🔍 Dialog states - View:", showViewDialog, "Edit:", showEditDialog, "Add:", showAddDialog);
  console.log("🔍 Selected order:", selectedOrder?.id);

  // Calculate order total
  const calculateOrderTotal = (order: Order): string => {
    try {
      // If order has a total field, use it
      if (typeof order.total === 'number' && order.total > 0) {
        return order.total.toFixed(2);
      }

      // Calculate from items if available
      if (order.items && Array.isArray(order.items) && order.items.length > 0) {
        const total = order.items.reduce((sum, item) => {
          const amount = typeof item.amount === 'number' ? item.amount : 0;
          return sum + amount;
        }, 0);
        return total.toFixed(2);
      }

      // Fallback: calculate from qty and unit cost if available
      const qty = typeof order.qty === 'number' ? order.qty : 0;
      const unitCost = typeof order.unitCost === 'number' ? order.unitCost : 10;
      return (qty * unitCost).toFixed(2);
    } catch (error) {
      console.warn("Error calculating total for order:", order.refNo, error);
      return "0.00";
    }
  };

  // Load orders from backend
  const loadOrders = async () => {
    try {
      console.log("🚀 Starting API call...");
      setLoading(true);
      setError(null);
      
      const response = await apiCall('/transaction-orders');
      console.log("📦 Raw API response:", response);

      // Check if response has the expected structure: { success: true, data: { orders: [...] } }
      if (response && response.success && response.data && Array.isArray(response.data.orders)) {
        console.log("✅ Valid orders array received, count:", response.data.orders.length);

        // Debug: Log all unique status values
        const uniqueStatuses = [...new Set(response.data.orders.map(order => order.status))];
        console.log("📊 Unique status values in data:", uniqueStatuses);

        // Debug: Log each order's status for detailed analysis
        response.data.orders.forEach((order, index) => {
          console.log(`Order ${index + 1}: status = "${order.status}" (type: ${typeof order.status})`);
        });

        setOrders(response.data.orders);
        setFilteredOrders(response.data.orders);
        setTotalPages(Math.ceil(response.data.orders.length / itemsPerPage));
      } else {
        console.warn("⚠️ Invalid response format:", response);
        setOrders([]);
        setFilteredOrders([]);
        setError("Invalid data format received from server");
      }
    } catch (err) {
      console.error("❌ Error loading orders:", err);
      setError(err instanceof Error ? err.message : 'Failed to load orders');
      setOrders([]);
      setFilteredOrders([]);
    } finally {
      setLoading(false);
      console.log("🏁 Loading complete");
    }
  };

  // Load orders on component mount
  useEffect(() => {
    loadOrders();
  }, []);

  // Apply filters whenever dependencies change
  useEffect(() => {
    console.log("🔄 Applying filters...", { searchQuery, statusFilter, dateFilter });
    
    try {
      let result = [...orders];

      // Search filter
      if (searchQuery && searchQuery.trim()) {
        result = result.filter(order => {
          if (!order) return false;
          const refNo = order.refNo || '';
          const vendor = order.vendor || '';
          const location = order.locationName || order.Location || order.location || '';
          const consumable = order.consumable || '';
          const query = searchQuery.toLowerCase();

          return refNo.toString().toLowerCase().includes(query) ||
                 vendor.toLowerCase().includes(query) ||
                 location.toLowerCase().includes(query) ||
                 consumable.toLowerCase().includes(query);
        });
      }

      // Status filter
      if (statusFilter !== "all") {
        result = result.filter(order => {
          if (!order || !order.status) return false;
          return order.status.toLowerCase() === statusFilter.toLowerCase();
        });
      }

      // Date filter
      if (dateFilter !== "all") {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        result = result.filter(order => {
          if (!order || !order.date) return false;

          try {
            const orderDate = new Date(order.date);
            if (isNaN(orderDate.getTime())) return false;

            switch (dateFilter) {
              case "today":
                return orderDate >= today;
              case "this-week":
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                return orderDate >= weekStart;
              case "this-month":
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return orderDate >= monthStart;
              case "last-month":
                const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return orderDate >= lastMonthStart && orderDate < thisMonthStart;
              default:
                return true;
            }
          } catch (dateError) {
            console.warn("Date parsing error for order:", order.refNo, dateError);
            return false;
          }
        });
      }

      console.log("✅ Filters applied, result count:", result.length);
      setFilteredOrders(result);
      setTotalPages(Math.ceil(result.length / itemsPerPage));
      setCurrentPage(1);
    } catch (error) {
      console.error("❌ Error in filter useEffect:", error);
      setFilteredOrders(orders);
      setTotalPages(Math.ceil(orders.length / itemsPerPage));
      setCurrentPage(1);
    }
  }, [orders, searchQuery, statusFilter, dateFilter, itemsPerPage]);

  // Handle search
  const handleSearch = () => {
    // Search is handled in useEffect, this is just for the search button
    console.log("🔍 Search triggered for:", searchQuery);
  };

  // Handle add order
  const handleAddOrder = () => {
    console.log("➕ Add button clicked");
    setSelectedOrder(null);
    setShowAddDialog(true);
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    const mappedOrder = mapOrderForDialog(order);
    setSelectedOrder(mappedOrder);
    setShowViewDialog(true);
  };

  // Handle edit order
  const handleEditOrder = (order: Order) => {
    try {
      console.log("✏️ Edit button clicked for order:", order.refNo);
      console.log("✏️ Raw order data:", JSON.stringify(order, null, 2));

      if (!order) {
        console.error("❌ No order data provided to handleEditOrder");
        return;
      }

      const mappedOrder = mapOrderForDialog(order);
      console.log("✏️ Mapped order data:", JSON.stringify(mappedOrder, null, 2));
      setSelectedOrder(mappedOrder);
      console.log("✏️ Setting showEditDialog to true");
      setShowEditDialog(true);
    } catch (error) {
      console.error("❌ Error in handleEditOrder:", error);
    }
  };

  // Handle edit from view dialog
  const handleEditFromView = () => {
    setShowViewDialog(false);
    setShowEditDialog(true);
  };

  // Wrapper for add
  const handleAddOrderSave = async (orderData: Order) => {
    try {
      const addResponse = await apiCall('/transaction-orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
      });
      
      console.log("✅ Order added successfully:", addResponse);
      console.log('Order added successfully!');
      setShowAddDialog(false);
      await loadOrders(); // Reload orders
    } catch (err) {
      console.error("❌ Error adding order:", err);
      console.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Wrapper for edit
  const handleEditOrderSave = async (orderData: Order) => {
    try {
      if (!selectedOrder) {
        console.error("❌ No selected order for update");
        return;
      }

      console.log("🔄 Updating order ID:", selectedOrder.id);
      console.log("📤 Order data being sent:", JSON.stringify(orderData, null, 2));
      console.log("🌐 API endpoint:", `/transaction-orders/${selectedOrder.id}`);

      const editResponse = await apiCall(`/transaction-orders/${selectedOrder.id}`, {
        method: 'PUT',
        body: JSON.stringify(orderData),
      });

      console.log("✅ Order updated successfully:", JSON.stringify(editResponse, null, 2));
      console.log('Order updated successfully!');
      setShowEditDialog(false);

      // Reload orders to see the changes
      console.log("🔄 Reloading orders to reflect changes...");
      await loadOrders();
    } catch (err) {
      console.error("❌ Error updating order:", err);
      console.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      // Don't close dialog on error so user can try again
    }
  };

  // Handle delete order
  const handleDeleteOrder = async (order: Order) => {
    if (confirm(`Are you sure you want to delete order "${order.refNo}"?`)) {
      try {
        const response = await apiCall(`/transaction-orders/${order.id}`, {
          method: 'DELETE',
        });
        
        console.log("✅ Order deleted successfully:", response);
        console.log('Order deleted successfully!');
        await loadOrders(); // Reload orders
      } catch (err) {
        console.error("❌ Error deleting order:", err);
        console.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending': return 'secondary';
      case 'approved': return 'default';
      case 'delivered': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  // Helper function to count orders by status category
  const countOrdersByStatus = (statusCategory: string) => {
    return orders.filter(order => {
      if (!order.status) return false;
      const status = order.status.toLowerCase().trim();

      switch (statusCategory) {
        case 'pending':
          return status === 'pending' ||
                 status === 'awaiting' ||
                 status === 'submitted' ||
                 status === 'new' ||
                 status === 'open';
        case 'transit':
          return status === 'in transit' ||
                 status === 'shipped' ||
                 status === 'in-transit' ||
                 status === 'transit' ||
                 status === 'shipping' ||
                 status === 'on the way';
        case 'delivered':
          return status === 'delivered' ||
                 status === 'completed' ||
                 status === 'received' ||
                 status === 'fulfilled';
        case 'approved':
          return status === 'approved' ||
                 status === 'confirmed' ||
                 status === 'accepted';
        default:
          return false;
      }
    }).length;
  };

  // Get paginated orders
  const getPaginatedOrders = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredOrders.slice(startIndex, endIndex);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading orders...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full p-4">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>

          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Orders</h2>
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={loadOrders}>
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-full p-4">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Link to="/transactions/consumables">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back to Consumable Transactions
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
                <p className="text-gray-600">Manage consumable orders</p>
              </div>
            </div>
            <Button onClick={handleAddOrder}>
              <Plus className="h-4 w-4 mr-2" />
              Add Order
            </Button>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
              <CardDescription>Filter orders by search, status, and date</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 flex-wrap">
                <div className="flex-1 min-w-[200px]">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Search orders..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="flex-1"
                    />
                    <Button onClick={handleSearch} variant="outline">
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Dates</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="this-week">This Week</SelectItem>
                    <SelectItem value="this-month">This Month</SelectItem>
                    <SelectItem value="last-month">Last Month</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Orders Table */}
        <Card className="flex-1">
          <CardHeader>
            <CardTitle>Orders ({filteredOrders.length})</CardTitle>
            <CardDescription>
              {filteredOrders.length === 0 ? 'No orders found' : `Showing ${getPaginatedOrders().length} of ${filteredOrders.length} orders`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredOrders.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No orders found</p>
                <Button onClick={handleAddOrder}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Order
                </Button>
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Ref No</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Vendor</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Consumable</TableHead>
                      <TableHead>Qty</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getPaginatedOrders().map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.refNo}</TableCell>
                        <TableCell>{formatDate(order.date)}</TableCell>
                        <TableCell>{order.vendor}</TableCell>
                        <TableCell>{order.locationName || order.Location || order.location || 'N/A'}</TableCell>
                        <TableCell>{order.consumable}</TableCell>
                        <TableCell>{order.qty}</TableCell>
                        <TableCell>₹{calculateOrderTotal(order)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(order.status)}>
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex gap-2 justify-end">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log("🔍 View button clicked for order ID:", order.id);
                                handleViewOrder(order);
                              }}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log("✏️ Edit button clicked for order ID:", order.id);
                                handleEditOrder(order);
                              }}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeleteOrder(order)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-gray-700">
                      Page {currentPage} of {totalPages}
                    </p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Order Summary Statistics */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Total Orders */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-800 mb-1">
                  {orders.length}
                </div>
                <div className="text-sm text-blue-600 font-medium">Total Orders</div>
                <div className="text-xs text-blue-500 mt-1">All Time</div>
              </div>

              {/* Pending Orders */}
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-orange-800 mb-1">
                  {countOrdersByStatus('pending')}
                </div>
                <div className="text-sm text-orange-600 font-medium">Pending Orders</div>
                <div className="text-xs text-orange-500 mt-1">Awaiting Processing</div>
              </div>

              {/* In Transit Orders */}
              <div className="bg-cyan-50 border border-cyan-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-cyan-800 mb-1">
                  {countOrdersByStatus('transit')}
                </div>
                <div className="text-sm text-cyan-600 font-medium">In Transit</div>
                <div className="text-xs text-cyan-500 mt-1">Being Delivered</div>
              </div>

              {/* Orders This Month */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-800 mb-1">
                  {orders.filter(order => {
                    const orderDate = new Date(order.date);
                    const currentDate = new Date();
                    return orderDate.getMonth() === currentDate.getMonth() &&
                           orderDate.getFullYear() === currentDate.getFullYear();
                  }).length}
                </div>
                <div className="text-sm text-green-600 font-medium">Orders This Month</div>
                <div className="text-xs text-green-500 mt-1">Current Month</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dialogs */}
        <OrderAddDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          onSave={(order) => {
            const orderWithDefaults = {
              ...order,
              id: 0,
              unitCost: (order as any).unitCost ?? 0, // type cast to avoid TS error
              consumable: (order as any).consumable ?? "",
            };
            handleAddOrderSave(orderWithDefaults as any); // as any to satisfy TS
          }}
        />

        {/* Edit Order Dialog */}
        <OrderEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          order={selectedOrder || undefined}
          onSave={(order) => {
            // Ensure unitCost is present for type compatibility
            const orderWithUnitCost = {
              ...order,
              unitCost: (order as any).unitCost ?? 0,
            };
            handleEditOrderSave(orderWithUnitCost as any);
          }}
        />

        {/* View Order Dialog */}
        <OrderViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          order={{
            ...selectedOrder,
            id: String(selectedOrder?.id ?? ""),
            otherCosts: [],
            subtotal: 0,
            createdBy: "",
            location: selectedOrder?.location ?? selectedOrder?.locationName ?? selectedOrder?.Location ?? "",
            items: (selectedOrder?.items || []).map(item => ({
              ...item,
              id: String(item.id),
              itemId: String(item.id),
              itemName: item.name,
            })),
            status: (
              selectedOrder?.status === "Pending" ||
              selectedOrder?.status === "Processing" ||
              selectedOrder?.status === "In Transit" ||
              selectedOrder?.status === "Delivered" ||
              selectedOrder?.status === "Cancelled" ||
              selectedOrder?.status === "pending" ||
              selectedOrder?.status === "approved" ||
              selectedOrder?.status === "completed" ||
              selectedOrder?.status === "cancelled"
            )
              ? selectedOrder.status
              : "pending",
          }}
          onEdit={handleEditFromView}
        />
      </div>
    </AppLayout>
  );
}
