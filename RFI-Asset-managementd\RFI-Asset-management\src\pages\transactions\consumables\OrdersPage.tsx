import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, FileDown, Plus, Search, Edit, Eye, Trash } from "lucide-react";
import { PageTitle } from "@/components/layout/PageTitle";
import { Link } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { OrderAddDialog } from "@/components/orders/OrderAddDialog";
import { OrderEditDialog } from "@/components/orders/OrderEditDialog";
import { OrderViewDialog } from "@/components/orders/OrderViewDialog";
import { exportData } from "@/utils/exportUtils";
import type { Order, OrderItem, OtherCost } from "@/utils/OrdersLocalStorage";

// Toast notification system
interface ToastData {
  id: string;
  type: 'success' | 'error';
  message: string;
}

let toastContainer: React.Dispatch<React.SetStateAction<ToastData[]>> | null = null;

const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  useEffect(() => {
    toastContainer = setToasts;
    return () => {
      toastContainer = null;
    };
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  useEffect(() => {
    toasts.forEach(toast => {
      const timer = setTimeout(() => {
        removeToast(toast.id);
      }, 5000);
      return () => clearTimeout(timer);
    });
  }, [toasts]);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map(toast => (
        <div
          key={toast.id}
          className={`flex items-center p-4 mb-3 rounded-lg shadow-lg border-l-4 transform transition-all duration-300 ease-in-out ${
            toast.type === 'success' 
              ? 'bg-green-50 border-green-500 text-green-800' 
              : 'bg-red-50 border-red-500 text-red-800'
          }`}
        >
          <span className="flex-1 text-sm font-medium">{toast.message}</span>
          <button
            onClick={() => removeToast(toast.id)}
            className={`ml-3 p-1 rounded-full text-lg font-bold ${
              toast.type === 'success' ? 'hover:bg-green-200' : 'hover:bg-red-200'
            }`}
          >
            ×
          </button>
        </div>
      ))}
    </div>
  );
};

const toast = {
  success: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'success', message }]);
    }
  },
  error: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'error', message }]);
    }
  }
};

// API service
const API_BASE_URL = 'http://localhost:5000/api';

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Add this interface for stats
interface OrderStats {
  total: number;
  thisMonth: number;
  pending: number;
  processing: number;
  inTransit: number;
  delivered: number;
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("this-month");
  const [orderStats, setOrderStats] = useState<OrderStats>({
    total: 0,
    thisMonth: 0,
    pending: 0,
    processing: 0,
    inTransit: 0,
    delivered: 0
  });

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Dialogs
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  console.log("🔍 OrdersPage rendering, loading:", loading, "orders:", orders.length);

  // Load orders from backend
  const loadOrders = async () => {
    try {
      console.log("🚀 Starting API call...");
      setLoading(true);
      setError(null);

      const response = await apiCall('/transaction-orders');
      console.log("📡 API Response:", response);

      if (response.success) {
        const ordersData = response.data?.orders || [];
        console.log("📦 Orders data received:", ordersData);
        setOrders(ordersData);
        setFilteredOrders(ordersData);

        // Calculate stats
        calculateOrderStats(ordersData);
      } else {
        console.warn("⚠️ API response not successful:", response);
        setError(response.message || 'Failed to load orders');
        // Set empty arrays to prevent crashes
        setOrders([]);
        setFilteredOrders([]);
      }
    } catch (err) {
      console.error('❌ Error loading orders:', err);
      setError(err instanceof Error ? err.message : 'Failed to load orders');
      // Set empty arrays to prevent crashes
      setOrders([]);
      setFilteredOrders([]);
    } finally {
      setLoading(false);
      console.log("🏁 Loading finished");
    }
  };

  // Calculate order statistics - simplified version
  const calculateOrderStats = (ordersData: Order[]) => {
    console.log("📊 Calculating stats for orders:", ordersData.length);

    const stats = {
      total: ordersData.length,
      thisMonth: 0, // Simplified - will calculate later
      pending: ordersData.filter(order => order?.status === 'Pending').length,
      processing: ordersData.filter(order => order?.status === 'Processing').length,
      inTransit: ordersData.filter(order => order?.status === 'In Transit').length,
      delivered: ordersData.filter(order => order?.status === 'Delivered').length,
    };

    console.log("📈 Calculated stats:", stats);
    setOrderStats(stats);
  };

  useEffect(() => {
    console.log("🎯 useEffect triggered");
    loadOrders();
  }, []);

  // Apply filters - simplified version
  useEffect(() => {
    console.log("🔍 Applying simple filters to orders:", orders.length);
    let result = [...orders]; // Create a copy

    // Simple search filter
    if (searchQuery && searchQuery.trim()) {
      result = result.filter(order =>
        order?.refNo?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order?.vendor?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order?.location?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Simple status filter
    if (statusFilter !== "all") {
      result = result.filter(order => order?.status?.toLowerCase() === statusFilter.toLowerCase());
    }

    console.log("✅ Simple filters applied, result count:", result.length);
    setFilteredOrders(result);
    setTotalPages(Math.ceil(result.length / itemsPerPage));
    setCurrentPage(1);
  }, [orders, searchQuery, statusFilter, itemsPerPage]);

  // Handle search
  const handleSearch = () => {
    // Search is handled in useEffect, this is just for the search button
    console.log("🔍 Search triggered for:", searchQuery);
  };

  // Handle add new order
  const handleAddOrder = () => {
    setSelectedOrder(null);
    setShowAddDialog(true);
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowViewDialog(true);
  };

  // Handle edit order
  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowEditDialog(true);
  };

  // Handle edit from view dialog
  const handleEditFromView = () => {
    setShowViewDialog(false);
    setShowEditDialog(true);
  };

  // Wrapper for add
  const handleAddOrderSave = async (orderData: Omit<Order, 'id'>) => {
    try {
      const addResponse = await apiCall('/transaction-orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
      });
      if (addResponse.success) {
        toast.success('Order created successfully!');
        setShowAddDialog(false);
        loadOrders();
      } else {
        toast.error(`Error: ${addResponse.message}`);
      }
    } catch (err) {
      console.error('Error saving order:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Wrapper for edit
  const handleEditOrderSave = async (orderData: Order) => {
    try {
      if (!selectedOrder) return;
      const editResponse = await apiCall(`/transaction-orders/${selectedOrder.id}`, {
        method: 'PUT',
        body: JSON.stringify(orderData),
      });
      if (editResponse.success) {
        toast.success('Order updated successfully!');
        setShowEditDialog(false);
        loadOrders();
      } else {
        toast.error(`Error: ${editResponse.message}`);
      }
    } catch (err) {
      console.error('Error saving order:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Handle delete order
  const handleDeleteOrder = async (order: Order) => {
    if (confirm(`Are you sure you want to delete order "${order.refNo}"?`)) {
      try {
        const response = await apiCall(`/transaction-orders/${order.id}`, {
          method: 'DELETE',
        });

        if (response.success) {
          toast.success('Order deleted successfully!');
          loadOrders();
        } else {
          toast.error(`Error: ${response.message}`);
        }
      } catch (err) {
        console.error('Error deleting order:', err);
        toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = filteredOrders.map(order => ({
      'Ref No': order.refNo,
      'Date': order.date,
      'Vendor': order.vendor,
      'Location': order.location,
      'Items Count': order.items.length,
      'Total': `$${order.total.toFixed(2)}`,
      'Status': order.status,
      'Notes': order.note || '',
    }));

    const headers = ['Ref No', 'Date', 'Vendor', 'Location', 'Items Count', 'Total', 'Status', 'Notes'];
    exportData(exportableData, format, 'orders', 'Orders List', headers);
  };

  // Get current orders for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstItem, indexOfLastItem);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'in transit':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  console.log("🎨 About to render with state:", { loading, error, ordersCount: orders.length });

  if (loading) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>
          
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading Orders...
              </div>
              <p className="mt-2 text-gray-600">Please wait while we fetch the data.</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>
          
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                <div className="flex items-center mb-4">
                  <svg className="h-6 w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-lg font-semibold text-red-800">Error Loading Orders</h3>
                </div>
                <p className="text-red-700 mb-4">{error}</p>
                <Button onClick={loadOrders} className="bg-blue-500 hover:bg-blue-600">
                  <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Retry
                </Button>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="mb-4">
          <Link to="/transactions/consumables">
            <Button variant="ghost" size="sm" className="mr-2">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Consumable Transactions
            </Button>
          </Link>
        </div>

        <PageTitle
          title="Orders"
          description="Track and manage consumable purchase orders"
          actions={
            <>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <FileDown className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleExport('pdf')}>
                    Export as PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('csv')}>
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('excel')}>
                    Export as Excel
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('clipboard')}>
                    Copy to Clipboard
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button size="sm" onClick={handleAddOrder}>
                <Plus className="h-4 w-4 mr-2" />
                New Order
              </Button>
            </>
          }
        />

        <Card>
          <CardHeader>
            <CardTitle>Order Search & Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div>
                <Label htmlFor="order-search">Order ID or Vendor</Label>
                <div className="flex mt-1">
                  <Input
                    id="order-search"
                    placeholder="Enter ID or vendor name"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <Button className="ml-2" size="icon" onClick={handleSearch}>
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={statusFilter}
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger id="status" className="mt-1">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="in transit">In Transit</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="date-range">Date Range</Label>
                <Select
                  value={dateFilter}
                  onValueChange={setDateFilter}
                >
                  <SelectTrigger id="date-range" className="mt-1">
                    <SelectValue placeholder="Select date range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Time</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="this-week">This Week</SelectItem>
                    <SelectItem value="this-month">This Month</SelectItem>
                    <SelectItem value="last-month">Last Month</SelectItem>
                    <SelectItem value="last-3-months">Last 3 Months</SelectItem>
                    <SelectItem value="last-6-months">Last 6 Months</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Purchase Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ref No</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.refNo}</TableCell>
                    <TableCell>{new Date(order.date).toLocaleDateString()}</TableCell>
                    <TableCell>{order.vendor}</TableCell>
                    <TableCell>{order.location}</TableCell>
                    <TableCell>{order.items.length}</TableCell>
                    <TableCell>${order.total.toFixed(2)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusBadgeColor(order.status)}>
                        {order.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex gap-2 justify-end">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewOrder(order)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditOrder(order)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteOrder(order)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {currentOrders.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      {searchQuery || statusFilter !== 'all' || dateFilter !== 'all' 
                        ? 'No matching orders found' 
                        : 'No orders found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            
            {filteredOrders.length > 0 && (
              <div className="mt-4 flex justify-between items-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <div className="text-sm text-gray-500">
                  Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredOrders.length)} of {filteredOrders.length} orders
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Total Orders</p>
                <p className="text-2xl font-bold">{orderStats.total}</p>
                <p className="text-xs text-gray-400">All Time</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Pending Orders</p>
                <p className="text-2xl font-bold text-yellow-600">{orderStats.pending}</p>
                <p className="text-xs text-gray-400">Awaiting Processing</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">In Transit</p>
                <p className="text-2xl font-bold text-blue-600">{orderStats.inTransit}</p>
                <p className="text-xs text-gray-400">Being Delivered</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Orders This Month</p>
                <p className="text-2xl font-bold text-green-600">{orderStats.thisMonth}</p>
                <p className="text-xs text-gray-400">Current Month</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div style={{ 
          marginTop: '20px', 
          padding: '10px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '4px',
          fontSize: '12px',
          color: '#666'
        }}>
          🛠️ Debug Info: {orders.length} orders loaded from database! Filtered: {filteredOrders.length}
        </div>

        {/* Add Order Dialog */}
        <OrderAddDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          onSave={handleAddOrderSave}
        />

        {/* Edit Order Dialog */}
        <OrderEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          order={selectedOrder || undefined}
          onSave={handleEditOrderSave}
        />

        {/* View Order Dialog */}
        <OrderViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          order={selectedOrder || undefined}
        />

        {/* Toast Container */}
        <ToastContainer />
      </div>
    </AppLayout>
  );
}