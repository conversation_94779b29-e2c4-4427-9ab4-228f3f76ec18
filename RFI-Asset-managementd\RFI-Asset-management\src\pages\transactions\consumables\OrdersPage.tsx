import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Plus, Search, Eye, Edit, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AppLayout } from '@/components/layout/AppLayout';
import { OrderAddDialog } from '@/components/dialogs/OrderAddDialog';
import { OrderEditDialog } from '@/components/dialogs/OrderEditDialog';
import { OrderViewDialog } from '@/components/dialogs/OrderViewDialog';
import { apiCall } from '@/lib/api';
import { toast } from 'react-hot-toast';
import { ToastContainer } from 'react-hot-toast';

// Order interface
interface Order {
  id: number;
  refNo: string;
  date: string;
  vendor: string;
  consumable: string;
  qty: number;
  unitCost: number;
  total: number;
  status: string;
  locationName?: string;
  Location?: string;
  location?: string;
  items?: Array<{
    id: number;
    name: string;
    quantity: number;
    unitCost: number;
    amount: number;
  }>;
}

export default function OrdersPage() {
  // State management
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Dialog states
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  console.log("🔍 OrdersPage rendering, loading:", loading, "orders:", orders.length);

  // Calculate order total
  const calculateOrderTotal = (order: Order): string => {
    try {
      // If order has a total field, use it
      if (typeof order.total === 'number' && order.total > 0) {
        return order.total.toFixed(2);
      }

      // Calculate from items if available
      if (order.items && Array.isArray(order.items) && order.items.length > 0) {
        const total = order.items.reduce((sum, item) => {
          const amount = typeof item.amount === 'number' ? item.amount : 0;
          return sum + amount;
        }, 0);
        return total.toFixed(2);
      }

      // Fallback: calculate from qty and unit cost if available
      const qty = typeof order.qty === 'number' ? order.qty : 0;
      const unitCost = typeof order.unitCost === 'number' ? order.unitCost : 10;
      return (qty * unitCost).toFixed(2);
    } catch (error) {
      console.warn("Error calculating total for order:", order.refNo, error);
      return "0.00";
    }
  };

  // Load orders from backend
  const loadOrders = async () => {
    try {
      console.log("🚀 Starting API call...");
      setLoading(true);
      setError(null);
      
      const response = await apiCall('/transaction-orders');
      console.log("📦 Raw API response:", response);
      
      if (response && Array.isArray(response)) {
        console.log("✅ Valid orders array received, count:", response.length);
        setOrders(response);
        setFilteredOrders(response);
        setTotalPages(Math.ceil(response.length / itemsPerPage));
      } else {
        console.warn("⚠️ Invalid response format:", response);
        setOrders([]);
        setFilteredOrders([]);
        setError("Invalid data format received from server");
      }
    } catch (err) {
      console.error("❌ Error loading orders:", err);
      setError(err instanceof Error ? err.message : 'Failed to load orders');
      setOrders([]);
      setFilteredOrders([]);
    } finally {
      setLoading(false);
      console.log("🏁 Loading complete");
    }
  };

  // Load orders on component mount
  useEffect(() => {
    loadOrders();
  }, []);

  // Apply filters whenever dependencies change
  useEffect(() => {
    console.log("🔄 Applying filters...", { searchQuery, statusFilter, dateFilter });
    
    try {
      let result = [...orders];

      // Search filter
      if (searchQuery && searchQuery.trim()) {
        result = result.filter(order => {
          if (!order) return false;
          const refNo = order.refNo || '';
          const vendor = order.vendor || '';
          const location = order.locationName || order.Location || order.location || '';
          const consumable = order.consumable || '';
          const query = searchQuery.toLowerCase();

          return refNo.toString().toLowerCase().includes(query) ||
                 vendor.toLowerCase().includes(query) ||
                 location.toLowerCase().includes(query) ||
                 consumable.toLowerCase().includes(query);
        });
      }

      // Status filter
      if (statusFilter !== "all") {
        result = result.filter(order => {
          if (!order || !order.status) return false;
          return order.status.toLowerCase() === statusFilter.toLowerCase();
        });
      }

      // Date filter
      if (dateFilter !== "all") {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        result = result.filter(order => {
          if (!order || !order.date) return false;

          try {
            const orderDate = new Date(order.date);
            if (isNaN(orderDate.getTime())) return false;

            switch (dateFilter) {
              case "today":
                return orderDate >= today;
              case "this-week":
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                return orderDate >= weekStart;
              case "this-month":
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return orderDate >= monthStart;
              case "last-month":
                const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return orderDate >= lastMonthStart && orderDate < thisMonthStart;
              default:
                return true;
            }
          } catch (dateError) {
            console.warn("Date parsing error for order:", order.refNo, dateError);
            return false;
          }
        });
      }

      console.log("✅ Filters applied, result count:", result.length);
      setFilteredOrders(result);
      setTotalPages(Math.ceil(result.length / itemsPerPage));
      setCurrentPage(1);
    } catch (error) {
      console.error("❌ Error in filter useEffect:", error);
      setFilteredOrders(orders);
      setTotalPages(Math.ceil(orders.length / itemsPerPage));
      setCurrentPage(1);
    }
  }, [orders, searchQuery, statusFilter, dateFilter, itemsPerPage]);

  // Handle search
  const handleSearch = () => {
    // Search is handled in useEffect, this is just for the search button
    console.log("🔍 Search triggered for:", searchQuery);
  };

  // Handle add order
  const handleAddOrder = () => {
    console.log("➕ Add button clicked");
    setSelectedOrder(null);
    setShowAddDialog(true);
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    console.log("🔍 View button clicked for order:", order.refNo);
    setSelectedOrder(order);
    setShowViewDialog(true);
  };

  // Handle edit order
  const handleEditOrder = (order: Order) => {
    console.log("✏️ Edit button clicked for order:", order.refNo);
    setSelectedOrder(order);
    setShowEditDialog(true);
  };

  // Handle edit from view dialog
  const handleEditFromView = () => {
    setShowViewDialog(false);
    setShowEditDialog(true);
  };

  // Wrapper for add
  const handleAddOrderSave = async (orderData: Order) => {
    try {
      const addResponse = await apiCall('/transaction-orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
      });
      
      console.log("✅ Order added successfully:", addResponse);
      toast.success('Order added successfully!');
      setShowAddDialog(false);
      await loadOrders(); // Reload orders
    } catch (err) {
      console.error("❌ Error adding order:", err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Wrapper for edit
  const handleEditOrderSave = async (orderData: Order) => {
    try {
      if (!selectedOrder) return;
      const editResponse = await apiCall(`/transaction-orders/${selectedOrder.id}`, {
        method: 'PUT',
        body: JSON.stringify(orderData),
      });
      
      console.log("✅ Order updated successfully:", editResponse);
      toast.success('Order updated successfully!');
      setShowEditDialog(false);
      await loadOrders(); // Reload orders
    } catch (err) {
      console.error("❌ Error updating order:", err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Handle delete order
  const handleDeleteOrder = async (order: Order) => {
    if (confirm(`Are you sure you want to delete order "${order.refNo}"?`)) {
      try {
        const response = await apiCall(`/transaction-orders/${order.id}`, {
          method: 'DELETE',
        });
        
        console.log("✅ Order deleted successfully:", response);
        toast.success('Order deleted successfully!');
        await loadOrders(); // Reload orders
      } catch (err) {
        console.error("❌ Error deleting order:", err);
        toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending': return 'secondary';
      case 'approved': return 'default';
      case 'delivered': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  // Get paginated orders
  const getPaginatedOrders = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredOrders.slice(startIndex, endIndex);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading orders...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full p-4">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>

          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Orders</h2>
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={loadOrders}>
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-full p-4">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Link to="/transactions/consumables">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back to Consumable Transactions
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
                <p className="text-gray-600">Manage consumable orders</p>
              </div>
            </div>
            <Button onClick={handleAddOrder}>
              <Plus className="h-4 w-4 mr-2" />
              Add Order
            </Button>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
              <CardDescription>Filter orders by search, status, and date</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 flex-wrap">
                <div className="flex-1 min-w-[200px]">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Search orders..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="flex-1"
                    />
                    <Button onClick={handleSearch} variant="outline">
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Dates</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="this-week">This Week</SelectItem>
                    <SelectItem value="this-month">This Month</SelectItem>
                    <SelectItem value="last-month">Last Month</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Orders Table */}
        <Card className="flex-1">
          <CardHeader>
            <CardTitle>Orders ({filteredOrders.length})</CardTitle>
            <CardDescription>
              {filteredOrders.length === 0 ? 'No orders found' : `Showing ${getPaginatedOrders().length} of ${filteredOrders.length} orders`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredOrders.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No orders found</p>
                <Button onClick={handleAddOrder}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Order
                </Button>
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Ref No</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Vendor</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Consumable</TableHead>
                      <TableHead>Qty</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getPaginatedOrders().map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.refNo}</TableCell>
                        <TableCell>{formatDate(order.date)}</TableCell>
                        <TableCell>{order.vendor}</TableCell>
                        <TableCell>{order.locationName || order.Location || order.location || 'N/A'}</TableCell>
                        <TableCell>{order.consumable}</TableCell>
                        <TableCell>{order.qty}</TableCell>
                        <TableCell>₹{calculateOrderTotal(order)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(order.status)}>
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex gap-2 justify-end">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewOrder(order)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditOrder(order)}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeleteOrder(order)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-gray-700">
                      Page {currentPage} of {totalPages}
                    </p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Dialogs */}
        <OrderAddDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          onSave={handleAddOrderSave}
        />

        {/* Edit Order Dialog */}
        <OrderEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          order={selectedOrder || undefined}
          onSave={handleEditOrderSave}
        />

        {/* View Order Dialog */}
        <OrderViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          order={selectedOrder || undefined}
          onEdit={handleEditFromView}
        />

        <ToastContainer />
      </div>
    </AppLayout>
  );
}
