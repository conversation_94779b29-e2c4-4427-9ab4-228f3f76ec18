import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, FileDown, Plus, Search, Edit, Eye, Trash } from "lucide-react";
import { PageTitle } from "@/components/layout/PageTitle";
import { Link } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { OrderAddDialog } from "@/components/orders/OrderAddDialog";
import { OrderEditDialog } from "@/components/orders/OrderEditDialog";
import { OrderViewDialog } from "@/components/orders/OrderViewDialog";
import { exportData } from "@/utils/exportUtils";
import type { Order, OrderItem, OtherCost } from "@/utils/OrdersLocalStorage";

// Toast notification system
interface ToastData {
  id: string;
  type: 'success' | 'error';
  message: string;
}

let toastContainer: React.Dispatch<React.SetStateAction<ToastData[]>> | null = null;

const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  useEffect(() => {
    toastContainer = setToasts;
    return () => {
      toastContainer = null;
    };
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  useEffect(() => {
    toasts.forEach(toast => {
      const timer = setTimeout(() => {
        removeToast(toast.id);
      }, 5000);
      return () => clearTimeout(timer);
    });
  }, [toasts]);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map(toast => (
        <div
          key={toast.id}
          className={`p-4 rounded-lg shadow-lg ${
            toast.type === 'success' 
              ? 'bg-green-500 text-white' 
              : 'bg-red-500 text-white'
          }`}
        >
          <div className="flex justify-between items-center">
            <span>{toast.message}</span>
            <button
              onClick={() => removeToast(toast.id)}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

const toast = {
  success: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'success', message }]);
    }
  },
  error: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'error', message }]);
    }
  }
};

// API service
const API_BASE_URL = 'http://localhost:5000/api';

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Order statistics interface
interface OrderStats {
  total: number;
  thisMonth: number;
  pending: number;
  processing: number;
  inTransit: number;
  delivered: number;
}

export default function OrdersPage() {
  // State management
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("this-month");
  const [orderStats, setOrderStats] = useState<OrderStats>({
    total: 0,
    thisMonth: 0,
    pending: 0,
    processing: 0,
    inTransit: 0,
    delivered: 0
  });

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Dialogs
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  console.log("🔍 OrdersPage rendering, loading:", loading, "orders:", orders.length);

  // Load orders from backend
  const loadOrders = async () => {
    try {
      console.log("🚀 Starting API call...");
      setLoading(true);
      setError(null);
      
      const response = await apiCall('/transaction-orders');
      console.log("📡 API Response:", response);
      
      if (response.success && response.data?.orders) {
        const ordersData = response.data.orders;
        console.log("📦 Orders data received:", ordersData);
        setOrders(ordersData);
        setFilteredOrders(ordersData);
        
        // Calculate stats
        calculateOrderStats(ordersData);
      } else {
        console.warn("⚠️ API response not successful:", response);
        setError(response.message || 'Failed to load orders');
        setOrders([]);
        setFilteredOrders([]);
      }
    } catch (err) {
      console.error('❌ Error loading orders:', err);
      setError(err instanceof Error ? err.message : 'Failed to load orders');
      setOrders([]);
      setFilteredOrders([]);
    } finally {
      setLoading(false);
      console.log("🏁 Loading finished");
    }
  };

  // Calculate order statistics
  const calculateOrderStats = (ordersData: Order[]) => {
    console.log("📊 Calculating stats for orders:", ordersData.length);
    
    const stats = {
      total: ordersData.length,
      thisMonth: 0, // Will calculate safely
      pending: ordersData.filter(order => order?.status === 'Pending').length,
      processing: ordersData.filter(order => order?.status === 'Processing').length,
      inTransit: ordersData.filter(order => order?.status === 'In Transit').length,
      delivered: ordersData.filter(order => order?.status === 'Delivered').length,
    };
    
    console.log("📈 Calculated stats:", stats);
    setOrderStats(stats);
  };

  useEffect(() => {
    console.log("🎯 useEffect triggered");
    loadOrders();
  }, []);

  // Apply filters
  useEffect(() => {
    console.log("🔍 Applying filters to orders:", orders.length);
    
    let result = [...orders];

    // Search filter
    if (searchQuery && searchQuery.trim()) {
      result = result.filter(order => {
        if (!order) return false;
        const refNo = order.refNo || '';
        const vendor = order.vendor || '';
        const location = order.location || '';
        const query = searchQuery.toLowerCase();
        
        return refNo.toLowerCase().includes(query) ||
               vendor.toLowerCase().includes(query) ||
               location.toLowerCase().includes(query);
      });
    }

    // Status filter
    if (statusFilter !== "all") {
      result = result.filter(order => {
        if (!order || !order.status) return false;
        return order.status.toLowerCase() === statusFilter.toLowerCase();
      });
    }

    console.log("✅ Filters applied, result count:", result.length);
    setFilteredOrders(result);
    setTotalPages(Math.ceil(result.length / itemsPerPage));
    setCurrentPage(1);
  }, [orders, searchQuery, statusFilter, itemsPerPage]);

  // Handle search
  const handleSearch = () => {
    console.log("🔍 Search triggered for:", searchQuery);
  };

  // Handle add order save
  const handleAddOrderSave = async (orderData: any) => {
    try {
      console.log("💾 Saving new order:", orderData);
      const response = await apiCall('/transaction-orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
      });

      if (response.success) {
        toast.success('Order created successfully!');
        setShowAddDialog(false);
        loadOrders();
      } else {
        toast.error(`Error: ${response.message}`);
      }
    } catch (err) {
      console.error('Error creating order:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Handle edit order save
  const handleEditOrderSave = async (orderData: any) => {
    try {
      console.log("💾 Updating order:", orderData);
      const response = await apiCall(`/transaction-orders/${orderData.id}`, {
        method: 'PUT',
        body: JSON.stringify(orderData),
      });

      if (response.success) {
        toast.success('Order updated successfully!');
        setShowEditDialog(false);
        setSelectedOrder(null);
        loadOrders();
      } else {
        toast.error(`Error: ${response.message}`);
      }
    } catch (err) {
      console.error('Error updating order:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowViewDialog(true);
  };

  // Handle edit order
  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowEditDialog(true);
  };

  // Handle delete order
  const handleDeleteOrder = async (order: Order) => {
    if (confirm(`Are you sure you want to delete order "${order.refNo}"?`)) {
      try {
        const response = await apiCall(`/transaction-orders/${order.id}`, {
          method: 'DELETE',
        });

        if (response.success) {
          toast.success('Order deleted successfully!');
          loadOrders();
        } else {
          toast.error(`Error: ${response.message}`);
        }
      } catch (err) {
        console.error('Error deleting order:', err);
        toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = filteredOrders.map(order => ({
      'Ref No': order.refNo,
      'Date': order.date,
      'Vendor': order.vendor,
      'Location': order.location,
      'Items Count': order.items?.length || 0,
      'Total': `$${order.total?.toFixed(2) || '0.00'}`,
      'Status': order.status,
      'Notes': order.note || '',
    }));

    const headers = ['Ref No', 'Date', 'Vendor', 'Location', 'Items Count', 'Total', 'Status', 'Notes'];
    exportData(exportableData, format, 'orders', 'Orders List', headers);
  };

  // Get current orders for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstItem, indexOfLastItem);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'in transit':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  console.log("🎨 About to render with state:", { loading, error, ordersCount: orders.length });

  if (loading) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>

          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading Orders...
              </div>
              <p className="mt-2 text-gray-600">Please wait while we fetch the data.</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>

          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                <div className="flex items-center mb-4">
                  <svg className="h-6 w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h2 className="text-lg font-semibold text-red-800">Error Loading Orders</h2>
                </div>
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={loadOrders} className="bg-red-600 hover:bg-red-700">
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="mb-4">
          <Link to="/transactions/consumables">
            <Button variant="ghost" size="sm" className="mr-2">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Consumable Transactions
            </Button>
          </Link>
        </div>

        <PageTitle
          title="Orders"
          description="Track and manage consumable purchase orders"
          actions={
            <>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <FileDown className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleExport('csv')}>
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('excel')}>
                    Export as Excel
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button onClick={() => setShowAddDialog(true)} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Order
              </Button>
            </>
          }
        />

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">Search Orders</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by ref no, vendor, location..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="in transit">In Transit</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="date-filter">Date Range</Label>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Dates" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Dates</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="this-week">This Week</SelectItem>
                    <SelectItem value="this-month">This Month</SelectItem>
                    <SelectItem value="last-month">Last Month</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button onClick={handleSearch} className="w-full">
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Purchase Orders
              <span className="text-sm font-normal text-gray-500">
                {filteredOrders.length} of {orders.length} orders
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {currentOrders.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No matching orders found</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Ref No</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Vendor</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Items</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.refNo}</TableCell>
                        <TableCell>{order.date}</TableCell>
                        <TableCell>{order.vendor}</TableCell>
                        <TableCell>{order.location}</TableCell>
                        <TableCell>{order.items?.length || 0}</TableCell>
                        <TableCell>${order.total?.toFixed(2) || '0.00'}</TableCell>
                        <TableCell>
                          <Badge className={getStatusBadgeColor(order.status)}>
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                Actions
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onClick={() => handleViewOrder(order)}>
                                <Eye className="h-4 w-4 mr-2" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditOrder(order)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteOrder(order)}
                                className="text-red-600"
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredOrders.length)} of {filteredOrders.length} orders
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => paginate(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => paginate(page)}
                    >
                      {page}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => paginate(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Total Orders</p>
                <p className="text-2xl font-bold">{orderStats.total}</p>
                <p className="text-xs text-gray-400">All Time</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Pending Orders</p>
                <p className="text-2xl font-bold text-yellow-600">{orderStats.pending}</p>
                <p className="text-xs text-gray-400">Awaiting Processing</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">In Transit</p>
                <p className="text-2xl font-bold text-blue-600">{orderStats.inTransit}</p>
                <p className="text-xs text-gray-400">Being Delivered</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Orders This Month</p>
                <p className="text-2xl font-bold text-green-600">{orderStats.thisMonth}</p>
                <p className="text-xs text-gray-400">Current Month</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div style={{
          marginTop: '20px',
          padding: '10px',
          backgroundColor: '#f0f0f0',
          borderRadius: '4px',
          fontSize: '12px',
          color: '#666'
        }}>
          🛠️ Debug Info: {orders.length} orders loaded from database! Filtered: {filteredOrders.length}
        </div>

        {/* Add Order Dialog */}
        <OrderAddDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          onSave={handleAddOrderSave}
        />

        {/* Edit Order Dialog */}
        <OrderEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          order={selectedOrder || undefined}
          onSave={handleEditOrderSave}
        />

        {/* View Order Dialog */}
        <OrderViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          order={selectedOrder || undefined}
        />

        {/* Toast Container */}
        <ToastContainer />
      </div>
    </AppLayout>
  );
}
