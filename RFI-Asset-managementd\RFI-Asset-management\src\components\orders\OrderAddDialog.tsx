import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar as CalendarIcon, Plus, X } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Order, OrderItem, OtherCost, getOtherCostTypes } from "@/utils/OrdersLocalStorage";
import { getVendors } from "@/utils/vendorStorage";
// import { getConsumables } from "@/utils/ConsumablesLocalStorage";

interface OrderAddDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (order: Omit<Order, 'id'>) => void;
}

export function OrderAddDialog({
  open,
  onOpenChange,
  onSave
}: OrderAddDialogProps) {
  // Vendor type fallback
  const defaultVendors = [
    { id: 'v1', name: 'ABC Supplies' },
    { id: 'v2', name: 'Global Consumables' }
  ];
  const [vendors, setVendors] = useState<any[]>(defaultVendors);
  const [consumables, setConsumables] = useState<any[]>([]);
  const [locations, setLocations] = useState<any[]>([]);
  const [otherCostTypes, setOtherCostTypes] = useState<string[]>([]);
  const [date, setDate] = useState<Date | undefined>(new Date());

  const [formData, setFormData] = useState<Omit<Order, 'id'>>({
    refNo: "",
    date: format(new Date(), "yyyy-MM-dd"),
    vendor: "",
    location: "",
    note: "",
    items: [],
    otherCosts: [],
    subtotal: 0,
    total: 0,
    status: "Pending",
    createdBy: "Super Admin"
  });

  const [newConsumable, setNewConsumable] = useState("");

  // Load data
  useEffect(() => {
    if (open) {
      try {
        const vendorData = getVendors();
        // Merge by id only, and only if name exists
        let mergedVendors = defaultVendors.slice();
        if (Array.isArray(vendorData)) {
          vendorData.forEach(v => {
            // Try to get a display name from possible fields, using type assertion to silence TS errors
            const displayName = (v as any).vendorName || (v as any).companyName || (v as any).name || null;
            if (v && (v as any).id && displayName && !mergedVendors.some(d => d.id === (v as any).id)) {
              mergedVendors.push({ id: (v as any).id, name: displayName });
            }
          });
        }
        setVendors(mergedVendors);
        const costTypes = getOtherCostTypes();
        setOtherCostTypes(costTypes);
        // Generate a new reference number
        const newRefNo = `${Math.floor(Math.random() * 900) + 100}`;
        // Safe date formatting
        let formattedDate: string;
        try {
          formattedDate = format(new Date(), "yyyy-MM-dd");
        } catch (dateError) {
          console.error("Date formatting error:", dateError);
          formattedDate = new Date().toISOString().split('T')[0]; // Fallback
        }

        setFormData({
          refNo: newRefNo,
          date: formattedDate,
          vendor: "",
          location: "",
          note: "",
          items: [],
          otherCosts: [],
          subtotal: 0,
          total: 0,
          status: "Pending",
          createdBy: "Super Admin"
        });
        // Fetch consumable categories from backend
        fetch("http://localhost:5000/api/transaction-orders/config/consumable-categories")
          .then(res => res.json())
          .then(data => {
            if (data.success && Array.isArray(data.data)) {
              setConsumables(data.data);
            } else {
              setConsumables([]);
            }
          })
          .catch(err => {
            setConsumables([]);
            console.error("Error loading consumable categories", err);
          });
        // Fetch packing locations from backend
        fetch("http://localhost:5000/api/transaction-orders/config/packing-locations")
          .then(res => res.json())
          .then(data => {
            if (data.success && Array.isArray(data.data)) {
              setLocations(data.data);
            } else {
              setLocations([]);
            }
          })
          .catch(err => {
            setLocations([]);
            console.error("Error loading packing locations", err);
          });
      } catch (error) {
        console.error("Error loading data:", error);
      }
    }
  }, [open]);

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setDate(date);
      try {
        handleInputChange("date", format(date, "yyyy-MM-dd"));
      } catch (error) {
        console.error("Date formatting error:", error);
        handleInputChange("date", date.toISOString().split('T')[0]);
      }
    }
  };

  // Handle adding an item
  const handleAddItem = () => {
    const newItem: OrderItem = {
      id: `item${formData.items.length + 1}`,
      itemId: "",
      itemName: "",
      quantity: 0,
      unitCost: 0,
      amount: 0
    };
    
    const updatedItems = [...formData.items, newItem];
    setFormData({
      ...formData,
      items: updatedItems
    });
  };

  // Handle removing an item
  const handleRemoveItem = (itemId: string) => {
    const updatedItems = formData.items.filter(item => item.id !== itemId);
    
    // Recalculate subtotal
    const subtotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
    const otherCostsTotal = formData.otherCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      items: updatedItems,
      subtotal: subtotal,
      total: subtotal + otherCostsTotal
    });
  };

  // Handle item change
  const handleItemChange = (itemId: string, field: string, value: any) => {
    const updatedItems = formData.items.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };
        
        // If quantity or unitCost changed, recalculate amount
        if (field === "quantity" || field === "unitCost") {
          updatedItem.quantity = field === "quantity" ? Number(value) : updatedItem.quantity;
          updatedItem.unitCost = field === "unitCost" ? Number(value) : updatedItem.unitCost;
          updatedItem.amount = updatedItem.quantity * updatedItem.unitCost;
        }
        
        // If item selection changed, update item details
        if (field === "itemId") {
          const selectedItem = consumables.find(c => c.id === value);
          if (selectedItem) {
            updatedItem.itemName = selectedItem.name;
          }
        }
        
        return updatedItem;
      }
      return item;
    });
    
    // Recalculate subtotal
    const subtotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
    const otherCostsTotal = formData.otherCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      items: updatedItems,
      subtotal: subtotal,
      total: subtotal + otherCostsTotal
    });
  };

  // Handle adding other cost
  const handleAddOtherCost = () => {
    const newCost: OtherCost = {
      id: `cost${formData.otherCosts.length + 1}`,
      type: "",
      amount: 0
    };
    
    const updatedCosts = [...formData.otherCosts, newCost];
    setFormData({
      ...formData,
      otherCosts: updatedCosts
    });
  };

  // Handle removing other cost
  const handleRemoveOtherCost = (costId: string) => {
    const updatedCosts = formData.otherCosts.filter(cost => cost.id !== costId);
    
    // Recalculate total
    const otherCostsTotal = updatedCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      otherCosts: updatedCosts,
      total: formData.subtotal + otherCostsTotal
    });
  };

  // Handle other cost change
  const handleOtherCostChange = (costId: string, field: string, value: any) => {
    const updatedCosts = formData.otherCosts.map(cost => {
      if (cost.id === costId) {
        const updatedCost = { ...cost, [field]: value };
        
        // If amount changed, convert to number
        if (field === "amount") {
          updatedCost.amount = Number(value);
        }
        
        return updatedCost;
      }
      return cost;
    });
    
    // Recalculate total
    const otherCostsTotal = updatedCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      otherCosts: updatedCosts,
      total: formData.subtotal + otherCostsTotal
    });
  };

  // Handle consumable addition
  const handleAddConsumable = () => {
    if (newConsumable.trim()) {
      const newConsumableItem = {
        id: `consumable${consumables.length + 1}`,
        name: newConsumable,
        itemNo: `ItemNo${consumables.length + 1}`
      };
      
      setConsumables([...consumables, newConsumableItem]);
      setNewConsumable("");
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    // Validate form
    if (!formData.vendor || !formData.date || !formData.location) {
      alert("Please fill in all required fields (vendor, date, stock location)");
      return;
    }
    if (!Array.isArray(formData.items) || formData.items.length === 0) {
      alert("Please add at least one item");
      return;
    }
    // Set consumable to the first item's name or id
    const consumable = formData.items.length > 0 ? (formData.items[0].itemName || formData.items[0].itemId) : "";
    if (!consumable) {
      alert("Please select a consumable item");
      return;
    }
    // Calculate total qty from items
    const totalQty = formData.items.reduce((sum, item) => sum + Number(item.quantity || 0), 0);

    // Map items to backend structure
    const itemsWithBackendFields = formData.items.map(item => ({
      id: item.id,
      item: item.itemId || item.itemName,
      consumable: item.itemName || item.itemId,
      qty: Number(item.quantity),
      unitCost: Number(item.unitCost),
      amount: Number(item.amount)
    }));

    // Convert status to lowercase and map to allowed values
    let status = (formData.status || '').toLowerCase();
    if (!["pending", "approved", "completed", "cancelled"].includes(status)) {
      status = "pending";
    }

    // Build payload for backend (Location with capital L)
    const orderPayload = {
      vendor: formData.vendor,
      date: formData.date,
      Location: formData.location, // Capital L for backend
      consumable,
      qty: totalQty,
      items: itemsWithBackendFields,
      note: formData.note,
      subtotal: Number(formData.subtotal),
      total: Number(formData.total),
      status
    };
    onSave(orderPayload as any);
    onOpenChange(false);
  };

  console.log("Selected location:", formData.location);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Order</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="refNo">Ref No</Label>
              <Input
                id="refNo"
                value={formData.refNo}
                onChange={(e) => handleInputChange("refNo", e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="date">Date *</Label>
              <div className="flex mt-1">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendor">vendor *</Label>
              <Select 
                value={formData.vendor} 
                onValueChange={(value) => handleInputChange("vendor", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.name}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="location">location *</Label>
              <Select
                value={formData.location}
                onValueChange={(value) => handleInputChange("location", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="note">note</Label>
            <Textarea
              id="note"
              value={formData.note}
              onChange={(e) => handleInputChange("note", e.target.value)}
              className="mt-1"
              rows={3}
            />
          </div>
          
          <div className="mt-4">
            {/* Add Consumable Field */}
            {/* <div className="flex items-end gap-2 mb-2">
              <div className="flex-1">
                <Label htmlFor="add-consumable">Add Consumable</Label>
                <Input
                  id="add-consumable"
                  placeholder="Enter new consumable"
                  value={newConsumable}
                  onChange={e => setNewConsumable(e.target.value)}
                  className="mt-1"
                />
              </div> */}
          </div>
          {/* Item List */}
          <div className="flex justify-between items-center mb-2">
            <Label className="w-56">items</Label>
            <Label className="w-56">consumable</Label>
            <Label className="w-20 text-center">qty</Label>
            <Label className="w-24 text-center">unit cost</Label>
            <Label className="w-24 text-right">Amount</Label>
            <div className="w-8"></div>
          </div>
          
          {formData.items.map((item, idx) => (
            <div key={item.id} className="flex items-center mb-2 gap-2">
              {/* Item selector */}
              <Select 
                value={item.itemId} 
                onValueChange={(value) => handleItemChange(item.id, "itemId", value)}
              >
                <SelectTrigger className="w-56">
                  <SelectValue placeholder="Select an item" />
                </SelectTrigger>
                <SelectContent>
                  {consumables.map((consumable) => (
                    <SelectItem key={consumable.id} value={consumable.id}>
                      {consumable.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {/* Consumable input for custom entry */}
              <Input
                placeholder="Consumable"
                value={item.itemName}
                onChange={e => handleItemChange(item.id, "itemName", e.target.value)}
                className="w-56"
              />
              {/* Quantity */}
              <Input
                type="number"
                value={item.quantity}
                onChange={e => handleItemChange(item.id, "quantity", e.target.value)}
                className="w-20 text-center"
                min="0"
              />
              {/* Unit Cost */}
              <Input
                type="number"
                value={item.unitCost}
                onChange={e => handleItemChange(item.id, "unitCost", e.target.value)}
                className="w-24 text-center"
                min="0"
                step="0.01"
              />
              {/* Amount */}
              <div className="w-24 text-right">
                {item.amount.toFixed(1)}
              </div>
              {/* Remove button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveItem(item.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddItem}
            className="mt-2"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Item
          </Button>
        </div>
        
        <div className="flex justify-end mt-4">
          <div className="w-64">
            <div className="flex justify-between py-2">
              <span>SUBTOTAL</span>
              <span>{formData.subtotal.toFixed(1)}</span>
            </div>
            
            {formData.otherCosts.map((cost) => (
              <div key={cost.id} className="flex items-center py-2">
                <Select 
                  value={cost.type} 
                  onValueChange={(value) => handleOtherCostChange(cost.id, "type", value)}
                >
                  <SelectTrigger className="w-36">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {otherCostTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <X 
                  className="h-4 w-4 mx-1 cursor-pointer" 
                  onClick={() => handleRemoveOtherCost(cost.id)}
                />
                
                <Input
                  type="number"
                  value={cost.amount}
                  onChange={(e) => handleOtherCostChange(cost.id, "amount", e.target.value)}
                  className="w-20 ml-2"
                  min="0"
                  step="0.01"
                />
              </div>
            ))}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAddOtherCost}
              className="mt-1"
            >
              <Plus className="h-4 w-4 mr-1" />
              Other Cost Type
            </Button>
            
            <div className="flex justify-between py-2 mt-2 font-bold border-t">
              <span>TOTAL</span>
              <span>{formData.total.toFixed(1)}</span>
            </div>
          </div>
        </div>
      
      
      <DialogFooter>
        <Button variant="outline" onClick={() => onOpenChange(false)}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>Save Order</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
);
}
