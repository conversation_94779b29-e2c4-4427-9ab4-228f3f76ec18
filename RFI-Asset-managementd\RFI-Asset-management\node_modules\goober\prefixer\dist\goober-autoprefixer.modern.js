var i=new Map([["align-self","-ms-grid-row-align"],["color-adjust","-webkit-print-color-adjust"],["column-gap","grid-column-gap"],["gap","grid-gap"],["grid-template-columns","-ms-grid-columns"],["grid-template-rows","-ms-grid-rows"],["justify-self","-ms-grid-column-align"],["margin-inline-end","-webkit-margin-end"],["margin-inline-start","-webkit-margin-start"],["overflow-wrap","word-wrap"],["padding-inline-end","-webkit-padding-end"],["padding-inline-start","-webkit-padding-start"],["row-gap","grid-row-gap"],["scroll-margin-bottom","scroll-snap-margin-bottom"],["scroll-margin-left","scroll-snap-margin-left"],["scroll-margin-right","scroll-snap-margin-right"],["scroll-margin-top","scroll-snap-margin-top"],["scroll-margin","scroll-snap-margin"],["text-combine-upright","-ms-text-combine-horizontal"]]);function n(n,r){let t="";const a=i.get(n);a&&(t+=`${a}:${r};`);const o=function(i){var n=/^(?:(text-(?:decoration$|e|or|si)|back(?:ground-cl|d|f)|box-d|(?:mask(?:$|-[ispro]|-cl)))|(tab-|column(?!-s)|text-align-l)|(ap)|(u|hy))/i.exec(i);return n?n[1]?1:n[2]?2:n[3]?3:5:0}(n);1&o&&(t+=`-webkit-${n}:${r};`),2&o&&(t+=`-moz-${n}:${r};`),4&o&&(t+=`-ms-${n}:${r};`);const l=function(i,n){var r=/^(?:(pos)|(background-i)|((?:max-|min-)?(?:block-s|inl|he|widt))|(dis))/i.exec(i);return r?r[1]?/^sti/i.test(n)?1:0:r[2]?/^image-/i.test(n)?1:0:r[3]?"-"===n[3]?2:0:/^(inline-)?grid$/i.test(n)?4:0:0}(n,r);return 1&l?t+=`${n}:-webkit-${r};`:2&l?t+=`${n}:-moz-${r};`:4&l&&(t+=`${n}:-ms-${r};`),t+=`${n}:${r};`,t}export{n as prefix};
