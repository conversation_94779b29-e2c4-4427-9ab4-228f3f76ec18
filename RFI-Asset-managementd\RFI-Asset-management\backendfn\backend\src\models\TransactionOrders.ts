import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';

interface TransactionOrderItemAttributes {
  id: string;
  item: string;
  consumable: string;
  qty: number;
  unitCost: number;
  amount: number;
}

interface TransactionOrderAttributes {
  id: string;
  refNo: number;
  vendor: string;
  date: Date;
  Location: string;
  note?: string;
  items: TransactionOrderItemAttributes[];
  consumable: string;
  qty: number;
  unitCost: number;
  subtotal: number;
  total: number;
  status: 'pending' | 'approved' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

interface TransactionOrderCreationAttributes extends Optional<TransactionOrderAttributes, 'id' | 'refNo' | 'createdAt' | 'updatedAt' | 'note'> {}

class TransactionOrder extends Model<TransactionOrderAttributes, TransactionOrderCreationAttributes> implements TransactionOrderAttributes {
  public id!: string;
  public refNo!: number;
  public vendor!: string;
  public date!: Date;
  public Location!: string;
  public note?: string;
  public items!: TransactionOrderItemAttributes[];
  public consumable!: string;
  public qty!: number;
  public unitCost!: number;
  public subtotal!: number;
  public total!: number;
  public status!: 'pending' | 'approved' | 'completed' | 'cancelled';
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

TransactionOrder.init(
  {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
    },
    refNo: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      autoIncrement: true,
      field: 'ref_no'
    },
    vendor: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      },
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    Location: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'location',
      validate: {
        notEmpty: true,
        len: [1, 255]
      },
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    items: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: [],
      validate: {
        isValidItems(value: TransactionOrderItemAttributes[]) {
          if (!Array.isArray(value)) {
            throw new Error('Items must be an array');
          }
          if (value.length === 0) {
            throw new Error('At least one item is required');
          }
          value.forEach((item, index) => {
            if (!item.item || !item.consumable) {
              throw new Error(`Item ${index + 1}: item and consumable are required`);
            }
            if (typeof item.qty !== 'number' || item.qty <= 0) {
              throw new Error(`Item ${index + 1}: quantity must be a positive number`);
            }
            if (typeof item.unitCost !== 'number' || item.unitCost < 0) {
              throw new Error(`Item ${index + 1}: unit cost must be a non-negative number`);
            }
            if (typeof item.amount !== 'number' || item.amount < 0) {
              throw new Error(`Item ${index + 1}: amount must be a non-negative number`);
            }
          });
        }
      }
    },
    consumable: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      },
    },
    qty: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 1
      },
    },
    unitCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      field: 'unit_cost',
      validate: {
        min: 0
      },
    },
    subtotal: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      },
    },
    total: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      },
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'completed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    },
  },
  {
    sequelize,
    tableName: 'transaction_orders',
    underscored: true,
    timestamps: true,
    hooks: {
      beforeValidate: (order: TransactionOrder) => {
        // Auto-calculate subtotal and total from items
        if (order.items && order.items.length > 0) {
          const subtotal = order.items.reduce((sum, item) => sum + item.amount, 0);
          order.subtotal = subtotal;
          order.total = subtotal; // You can add other costs here if needed
        }
      },
      beforeCreate: (order: TransactionOrder) => {
        // Generate UUID for id if not provided
        if (!order.id) {
          order.id = require('crypto').randomUUID();
        }
      }
    }
  }
);

export default TransactionOrder;