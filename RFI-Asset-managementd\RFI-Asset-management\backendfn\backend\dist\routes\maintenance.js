"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Maintenance_1 = __importDefault(require("../models/Maintenance"));
const sequelize_1 = require("sequelize");
const router = (0, express_1.Router)();
// GET /api/maintenance - Get all maintenance records with optional filtering
router.get('/', async (req, res) => {
    try {
        const { page = 1, limit = 50, search, category, status, priority, requestType, assignedTechnician, startDate, endDate } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const whereClause = {};
        // Add search filters
        if (search) {
            whereClause[sequelize_1.Op.or] = [
                { assetTag: { [sequelize_1.Op.iLike]: `%${search}%` } },
                { name: { [sequelize_1.Op.iLike]: `%${search}%` } },
                { description: { [sequelize_1.Op.iLike]: `%${search}%` } },
                { assignedTechnician: { [sequelize_1.Op.iLike]: `%${search}%` } }
            ];
        }
        // Add specific filters
        if (category)
            whereClause.category = category;
        if (status)
            whereClause.status = status;
        if (priority)
            whereClause.priority = priority;
        if (requestType)
            whereClause.requestType = requestType;
        if (assignedTechnician)
            whereClause.assignedTechnician = { [sequelize_1.Op.iLike]: `%${assignedTechnician}%` };
        // Date range filtering
        if (startDate || endDate) {
            whereClause.createdAt = {};
            if (startDate)
                whereClause.createdAt[sequelize_1.Op.gte] = new Date(startDate);
            if (endDate)
                whereClause.createdAt[sequelize_1.Op.lte] = new Date(endDate);
        }
        const { count, rows } = await Maintenance_1.default.findAndCountAll({
            where: whereClause,
            limit: Number(limit),
            offset,
            order: [['createdAt', 'DESC']]
        });
        res.json({
            maintenance: rows,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: count,
                pages: Math.ceil(count / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Error fetching maintenance records:', error);
        res.status(500).json({ error: 'Failed to fetch maintenance records' });
    }
});
// GET /api/maintenance/stats - Get maintenance dashboard statistics
router.get('/stats', async (req, res) => {
    try {
        // Basic counts
        const totalRequests = await Maintenance_1.default.count();
        const pendingTasks = await Maintenance_1.default.count({
            where: {
                status: {
                    [sequelize_1.Op.in]: ['REQUESTED', 'SCHEDULED', 'IN_PROGRESS']
                }
            }
        });
        // Completed in last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const completedLast30Days = await Maintenance_1.default.count({
            where: {
                status: 'COMPLETED',
                updatedAt: {
                    [sequelize_1.Op.gte]: thirtyDaysAgo
                }
            }
        });
        // High priority tasks (critical & high)
        const highPriorityTasks = await Maintenance_1.default.count({
            where: {
                priority: {
                    [sequelize_1.Op.in]: ['critical', 'high']
                },
                status: {
                    [sequelize_1.Op.not]: 'COMPLETED'
                }
            }
        });
        // Status breakdown
        const statusBreakdown = await Maintenance_1.default.findAll({
            attributes: [
                'status',
                [Maintenance_1.default.sequelize.fn('COUNT', Maintenance_1.default.sequelize.col('asset_tag')), 'count']
            ],
            group: ['status'],
            raw: true
        });
        // Priority breakdown
        const priorityBreakdown = await Maintenance_1.default.findAll({
            attributes: [
                'priority',
                [Maintenance_1.default.sequelize.fn('COUNT', Maintenance_1.default.sequelize.col('asset_tag')), 'count']
            ],
            group: ['priority'],
            raw: true
        });
        // Category breakdown
        const categoryBreakdown = await Maintenance_1.default.findAll({
            attributes: [
                'category',
                [Maintenance_1.default.sequelize.fn('COUNT', Maintenance_1.default.sequelize.col('asset_tag')), 'count']
            ],
            group: ['category'],
            raw: true
        });
        // Request type breakdown
        const requestTypeBreakdown = await Maintenance_1.default.findAll({
            attributes: [
                'requestType',
                [Maintenance_1.default.sequelize.fn('COUNT', Maintenance_1.default.sequelize.col('asset_tag')), 'count']
            ],
            group: ['requestType'],
            raw: true
        });
        // Overdue maintenance (scheduled date passed but not completed)
        const overdueTasks = await Maintenance_1.default.count({
            where: {
                scheduledDate: {
                    [sequelize_1.Op.lt]: new Date()
                },
                status: {
                    [sequelize_1.Op.not]: 'COMPLETED'
                }
            }
        });
        // Due this week
        const nextWeek = new Date();
        nextWeek.setDate(nextWeek.getDate() + 7);
        const dueThisWeek = await Maintenance_1.default.count({
            where: {
                scheduledDate: {
                    [sequelize_1.Op.between]: [new Date(), nextWeek]
                },
                status: {
                    [sequelize_1.Op.not]: 'COMPLETED'
                }
            }
        });
        // Assets under maintenance count (assuming 1 asset per maintenance record)
        const underMaintenanceCount = await Maintenance_1.default.count({
            where: {
                status: {
                    [sequelize_1.Op.in]: ['IN_PROGRESS', 'TESTING']
                }
            }
        });
        // Calculate percentage of total assets under maintenance
        // Note: You might need to adjust this based on your total asset count
        const underMaintenancePercentage = totalRequests > 0 ?
            Math.round((underMaintenanceCount / totalRequests) * 100) : 0;
        res.json({
            // Main dashboard metrics
            totalRequests,
            pendingTasks,
            completedLast30Days,
            highPriorityTasks,
            // Additional metrics
            overdueTasks,
            dueThisWeek,
            underMaintenanceCount,
            underMaintenancePercentage,
            // Breakdowns
            statusBreakdown,
            priorityBreakdown,
            categoryBreakdown,
            requestTypeBreakdown
        });
    }
    catch (error) {
        console.error('Error fetching maintenance stats:', error);
        res.status(500).json({ error: 'Failed to fetch maintenance statistics' });
    }
});
// GET /api/maintenance/recent-activity - Get recent maintenance activity (last 10 days)
router.get('/recent-activity', async (req, res) => {
    try {
        const tenDaysAgo = new Date();
        tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);
        const recentActivity = await Maintenance_1.default.findAll({
            where: {
                createdAt: {
                    [sequelize_1.Op.gte]: tenDaysAgo
                }
            },
            order: [['createdAt', 'DESC']],
            limit: 20,
            attributes: [
                'assetTag',
                'name',
                'category',
                'requestType',
                'priority',
                'status',
                'description',
                'assignedTechnician',
                'scheduledDate',
                'createdAt',
                'updatedAt'
            ]
        });
        // Group by activity type
        const requested = recentActivity.filter(item => item.createdAt >= tenDaysAgo && item.status === 'REQUESTED');
        const scheduled = recentActivity.filter(item => item.status === 'SCHEDULED' && item.updatedAt >= tenDaysAgo);
        const completed = recentActivity.filter(item => item.status === 'COMPLETED' && item.updatedAt >= tenDaysAgo);
        res.json({
            recentActivity,
            summary: {
                requested: requested.length,
                scheduled: scheduled.length,
                completed: completed.length,
                total: recentActivity.length
            },
            activities: {
                requested,
                scheduled,
                completed
            }
        });
    }
    catch (error) {
        console.error('Error fetching recent maintenance activity:', error);
        res.status(500).json({ error: 'Failed to fetch recent maintenance activity' });
    }
});
// GET /api/maintenance/:assetTag - Get specific maintenance record by asset tag
router.get('/:assetTag', async (req, res) => {
    try {
        const maintenance = await Maintenance_1.default.findByPk(req.params.assetTag);
        if (!maintenance) {
            res.status(404).json({ error: 'Maintenance record not found' });
            return;
        }
        res.json(maintenance);
    }
    catch (error) {
        console.error('Error fetching maintenance record:', error);
        res.status(500).json({ error: 'Failed to fetch maintenance record' });
    }
});
// POST /api/maintenance - Create new maintenance request
router.post('/', async (req, res) => {
    try {
        const maintenanceData = req.body;
        // Check if maintenance record already exists for this asset
        const existingMaintenance = await Maintenance_1.default.findOne({
            where: { assetTag: maintenanceData.assetTag }
        });
        if (existingMaintenance) {
            res.status(400).json({ error: 'Maintenance record already exists for this asset' });
            return;
        }
        const maintenance = await Maintenance_1.default.create(maintenanceData);
        res.status(201).json(maintenance);
    }
    catch (error) {
        console.error('Error creating maintenance record:', error);
        res.status(500).json({ error: 'Failed to create maintenance record' });
    }
});
// PUT /api/maintenance/:assetTag - Update maintenance record
router.put('/:assetTag', async (req, res) => {
    try {
        const maintenance = await Maintenance_1.default.findByPk(req.params.assetTag);
        if (!maintenance) {
            res.status(404).json({ error: 'Maintenance record not found' });
            return;
        }
        await maintenance.update(req.body);
        res.json(maintenance);
    }
    catch (error) {
        console.error('Error updating maintenance record:', error);
        res.status(500).json({ error: 'Failed to update maintenance record' });
    }
});
// PUT /api/maintenance/:assetTag/status - Update maintenance status
router.put('/:assetTag/status', async (req, res) => {
    try {
        const { status, notes, assignedTechnician } = req.body;
        const maintenance = await Maintenance_1.default.findByPk(req.params.assetTag);
        if (!maintenance) {
            res.status(404).json({ error: 'Maintenance record not found' });
            return;
        }
        const updateData = { status };
        if (notes !== undefined)
            updateData.notes = notes;
        if (assignedTechnician !== undefined)
            updateData.assignedTechnician = assignedTechnician;
        await maintenance.update(updateData);
        res.json(maintenance);
    }
    catch (error) {
        console.error('Error updating maintenance status:', error);
        res.status(500).json({ error: 'Failed to update maintenance status' });
    }
});
// PUT /api/maintenance/:assetTag/schedule - Schedule maintenance
router.put('/:assetTag/schedule', async (req, res) => {
    try {
        const { scheduledDate, assignedTechnician, estimatedHours, estimatedCost, notes } = req.body;
        const maintenance = await Maintenance_1.default.findByPk(req.params.assetTag);
        if (!maintenance) {
            res.status(404).json({ error: 'Maintenance record not found' });
            return;
        }
        const updateData = {
            status: 'SCHEDULED',
            scheduledDate: new Date(scheduledDate)
        };
        if (assignedTechnician)
            updateData.assignedTechnician = assignedTechnician;
        if (estimatedHours !== undefined)
            updateData.estimatedHours = estimatedHours;
        if (estimatedCost !== undefined)
            updateData.estimatedCost = estimatedCost;
        if (notes)
            updateData.notes = notes;
        await maintenance.update(updateData);
        res.json(maintenance);
    }
    catch (error) {
        console.error('Error scheduling maintenance:', error);
        res.status(500).json({ error: 'Failed to schedule maintenance' });
    }
});
// DELETE /api/maintenance/:assetTag - Delete maintenance record
router.delete('/:assetTag', async (req, res) => {
    try {
        const maintenance = await Maintenance_1.default.findByPk(req.params.assetTag);
        if (!maintenance) {
            res.status(404).json({ error: 'Maintenance record not found' });
            return;
        }
        await maintenance.destroy();
        res.json({ message: 'Maintenance record deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting maintenance record:', error);
        res.status(500).json({ error: 'Failed to delete maintenance record' });
    }
});
// GET /api/maintenance/overdue/list - Get overdue maintenance tasks
router.get('/overdue/list', async (req, res) => {
    try {
        const overdueTasks = await Maintenance_1.default.findAll({
            where: {
                scheduledDate: {
                    [sequelize_1.Op.lt]: new Date()
                },
                status: {
                    [sequelize_1.Op.not]: 'COMPLETED'
                }
            },
            order: [['scheduledDate', 'ASC']]
        });
        res.json(overdueTasks);
    }
    catch (error) {
        console.error('Error fetching overdue tasks:', error);
        res.status(500).json({ error: 'Failed to fetch overdue tasks' });
    }
});
// GET /api/maintenance/categories/list - Get unique categories
router.get('/categories/list', async (req, res) => {
    try {
        const categories = await Maintenance_1.default.findAll({
            attributes: ['category'],
            group: ['category'],
            order: [['category', 'ASC']],
            raw: true
        });
        res.json(categories.map(c => c.category));
    }
    catch (error) {
        console.error('Error fetching categories:', error);
        res.status(500).json({ error: 'Failed to fetch categories' });
    }
});
// GET /api/maintenance/technicians/list - Get unique technicians
router.get('/technicians/list', async (req, res) => {
    try {
        const technicians = await Maintenance_1.default.findAll({
            attributes: ['assignedTechnician'],
            where: {
                assignedTechnician: {
                    [sequelize_1.Op.notIn]: ['']
                }
            },
            group: ['assignedTechnician'],
            order: [['assignedTechnician', 'ASC']],
            raw: true
        });
        res.json(technicians.map(t => t.assignedTechnician));
    }
    catch (error) {
        console.error('Error fetching technicians:', error);
        res.status(500).json({ error: 'Failed to fetch technicians' });
    }
});
// Helper function to safely extract count from Sequelize result
const extractCount = (countResult) => {
    if (typeof countResult === 'number') {
        return countResult;
    }
    if (Array.isArray(countResult) && countResult.length > 0) {
        return countResult[0]?.count || 0;
    }
    if (countResult && typeof countResult === 'object' && 'count' in countResult) {
        return countResult.count || 0;
    }
    return 0;
};
// GET /api/maintenance/asset-status/overview - Get detailed asset status overview
router.get('/asset-status/overview', async (req, res) => {
    try {
        // Working Assets: Assets currently in progress with maintenance
        const workingAssetsResult = await Maintenance_1.default.count({
            distinct: true,
            col: 'assetTag',
            where: {
                status: 'IN_PROGRESS'
            }
        });
        // Get all unique asset tags that are either requested or in progress
        const busyAssets = await Maintenance_1.default.findAll({
            attributes: ['assetTag'],
            where: {
                status: {
                    [sequelize_1.Op.in]: ['REQUESTED', 'IN_PROGRESS']
                }
            },
            group: ['assetTag'],
            raw: true
        });
        const busyAssetTags = busyAssets.map(asset => asset.assetTag);
        // Available Assets: Assets not currently in requested or in-progress state
        const totalUniqueAssetsResult = await Maintenance_1.default.count({
            distinct: true,
            col: 'assetTag'
        });
        const workingAssetsCount = extractCount(workingAssetsResult);
        const totalUniqueAssets = extractCount(totalUniqueAssetsResult);
        const availableAssetsCount = totalUniqueAssets - busyAssetTags.length;
        // Assigned Assets: Assets with requested status and assigned technician
        const assignedAssetsResult = await Maintenance_1.default.count({
            distinct: true,
            col: 'assetTag',
            where: {
                status: 'REQUESTED',
                assignedTechnician: {
                    [sequelize_1.Op.notIn]: ['']
                }
            }
        });
        const assignedAssetsCount = extractCount(assignedAssetsResult);
        // Additional status breakdown
        const statusBreakdown = await Maintenance_1.default.findAll({
            attributes: [
                'status',
                [Maintenance_1.default.sequelize.fn('COUNT', Maintenance_1.default.sequelize.fn('DISTINCT', Maintenance_1.default.sequelize.col('asset_tag'))), 'count']
            ],
            group: ['status'],
            order: [['status', 'ASC']],
            raw: true
        });
        // Assets by priority level
        const priorityBreakdown = await Maintenance_1.default.findAll({
            attributes: [
                'priority',
                [Maintenance_1.default.sequelize.fn('COUNT', Maintenance_1.default.sequelize.fn('DISTINCT', Maintenance_1.default.sequelize.col('asset_tag'))), 'count']
            ],
            where: {
                status: {
                    [sequelize_1.Op.not]: 'COMPLETED'
                }
            },
            group: ['priority'],
            order: [['priority', 'ASC']],
            raw: true
        });
        // Overdue assets
        const overdueAssetsResult = await Maintenance_1.default.count({
            distinct: true,
            col: 'assetTag',
            where: {
                scheduledDate: {
                    [sequelize_1.Op.lt]: new Date()
                },
                status: {
                    [sequelize_1.Op.not]: 'COMPLETED'
                }
            }
        });
        const overdueAssetsCount = extractCount(overdueAssetsResult);
        res.json({
            assetStatusOverview: [
                {
                    status: 'Working',
                    count: workingAssetsCount,
                    description: 'Assets currently under maintenance'
                },
                {
                    status: 'Available',
                    count: Math.max(0, availableAssetsCount),
                    description: 'Assets available for use (not in maintenance queue)'
                },
                {
                    status: 'Assigned',
                    count: assignedAssetsCount,
                    description: 'Assets with assigned technicians'
                }
            ],
            detailedBreakdown: {
                totalUniqueAssets,
                busyAssets: busyAssetTags.length,
                overdueAssets: overdueAssetsCount,
                statusBreakdown,
                priorityBreakdown
            },
            summary: {
                totalAssets: totalUniqueAssets,
                inMaintenance: busyAssetTags.length,
                available: Math.max(0, availableAssetsCount),
                maintenanceRate: totalUniqueAssets > 0 ?
                    Math.round((busyAssetTags.length / totalUniqueAssets) * 100) : 0
            }
        });
    }
    catch (error) {
        console.error('Error fetching asset status overview:', error);
        res.status(500).json({ error: 'Failed to fetch asset status overview' });
    }
});
// GET /api/maintenance/dashboard/summary - Enhanced dashboard summary with asset status
router.get('/dashboard/summary', async (req, res) => {
    try {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        // Main metrics
        const totalRequests = await Maintenance_1.default.count();
        const pendingTasks = await Maintenance_1.default.count({
            where: { status: { [sequelize_1.Op.in]: ['REQUESTED', 'SCHEDULED', 'IN_PROGRESS'] } }
        });
        const completedLast30Days = await Maintenance_1.default.count({
            where: {
                status: 'COMPLETED',
                updatedAt: { [sequelize_1.Op.gte]: thirtyDaysAgo }
            }
        });
        const highPriorityTasks = await Maintenance_1.default.count({
            where: {
                priority: { [sequelize_1.Op.in]: ['critical', 'high'] },
                status: { [sequelize_1.Op.not]: 'COMPLETED' }
            }
        });
        // Additional insights
        const underMaintenanceCount = await Maintenance_1.default.count({
            where: { status: { [sequelize_1.Op.in]: ['IN_PROGRESS', 'TESTING'] } }
        });
        const dueThisWeek = await Maintenance_1.default.count({
            where: {
                scheduledDate: { [sequelize_1.Op.between]: [now, sevenDaysFromNow] },
                status: { [sequelize_1.Op.not]: 'COMPLETED' }
            }
        });
        // Enhanced Asset Status Overview based on maintenance table
        const workingAssetsResult = await Maintenance_1.default.count({
            distinct: true,
            col: 'assetTag',
            where: { status: 'IN_PROGRESS' }
        });
        const busyAssets = await Maintenance_1.default.findAll({
            attributes: ['assetTag'],
            where: { status: { [sequelize_1.Op.in]: ['REQUESTED', 'IN_PROGRESS'] } },
            group: ['assetTag'],
            raw: true
        });
        const totalUniqueAssetsResult = await Maintenance_1.default.count({
            distinct: true,
            col: 'assetTag'
        });
        const assignedAssetsResult = await Maintenance_1.default.count({
            distinct: true,
            col: 'assetTag',
            where: {
                status: 'REQUESTED',
                assignedTechnician: {
                    [sequelize_1.Op.notIn]: ['']
                }
            }
        });
        const workingAssetsCount = extractCount(workingAssetsResult);
        const totalUniqueAssets = extractCount(totalUniqueAssetsResult);
        const assignedAssetsCount = extractCount(assignedAssetsResult);
        const availableAssetsCount = totalUniqueAssets - busyAssets.length;
        const assetStatusOverview = [
            {
                status: 'Working',
                count: workingAssetsCount
            },
            {
                status: 'Available',
                count: Math.max(0, availableAssetsCount)
            },
            {
                status: 'Assigned',
                count: assignedAssetsCount
            }
        ];
        res.json({
            mainMetrics: {
                totalRequests,
                pendingTasks,
                completedLast30Days,
                highPriorityTasks
            },
            additionalMetrics: {
                underMaintenanceCount,
                underMaintenancePercentage: totalRequests > 0 ?
                    Math.round((underMaintenanceCount / totalRequests) * 100) : 0,
                dueThisWeek
            },
            assetStatusOverview,
            assetSummary: {
                totalAssets: totalUniqueAssets,
                inMaintenance: busyAssets.length,
                available: Math.max(0, availableAssetsCount),
                maintenanceRate: totalUniqueAssets > 0 ?
                    Math.round((busyAssets.length / totalUniqueAssets) * 100) : 0
            }
        });
    }
    catch (error) {
        console.error('Error fetching dashboard summary:', error);
        res.status(500).json({ error: 'Failed to fetch dashboard summary' });
    }
});
// GET /api/maintenance/recent - Get most recent maintenance records (for dashboard)
router.get('/recent', async (req, res) => {
    try {
        const limit = req.query.limit ? parseInt(req.query.limit, 10) : 10;
        const recentRecords = await Maintenance_1.default.findAll({
            order: [['createdAt', 'DESC']],
            limit,
            attributes: [
                'assetTag',
                'category',
                'name',
                'priority',
                'status',
                'description',
                'assignedTechnician',
                'scheduledDate',
                'scheduledFrequency',
                'estimatedHours',
                'estimatedCost',
                'notes',
                'createdAt',
                'updatedAt'
            ]
        });
        res.json({
            success: true,
            data: { maintenanceRecords: recentRecords }
        });
    }
    catch (error) {
        console.error('Error fetching recent maintenance records:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch recent maintenance records' });
    }
});
exports.default = router;
