{"version": 3, "file": "photoswipe-lightbox.esm.js", "sources": ["../../../src/js/util/util.js", "../../../src/js/core/eventable.js", "../../../src/js/slide/placeholder.js", "../../../src/js/slide/content.js", "../../../src/js/util/viewport-size.js", "../../../src/js/slide/zoom-level.js", "../../../src/js/slide/loader.js", "../../../src/js/core/base.js", "../../../src/js/lightbox/lightbox.js"], "sourcesContent": ["/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n/**\r\n * @template {keyof HTMLElementTagNameMap} T\r\n * @param {string} className\r\n * @param {T} tagName\r\n * @param {Node} [appendToEl]\r\n * @returns {HTMLElementTagNameMap[T]}\r\n */\r\nexport function createElement(className, tagName, appendToEl) {\r\n  const el = document.createElement(tagName);\r\n  if (className) {\r\n    el.className = className;\r\n  }\r\n  if (appendToEl) {\r\n    appendToEl.appendChild(el);\r\n  }\r\n  return el;\r\n}\r\n\r\n/**\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {Point}\r\n */\r\nexport function equalizePoints(p1, p2) {\r\n  p1.x = p2.x;\r\n  p1.y = p2.y;\r\n  if (p2.id !== undefined) {\r\n    p1.id = p2.id;\r\n  }\r\n  return p1;\r\n}\r\n\r\n/**\r\n * @param {Point} p\r\n */\r\nexport function roundPoint(p) {\r\n  p.x = Math.round(p.x);\r\n  p.y = Math.round(p.y);\r\n}\r\n\r\n/**\r\n * Returns distance between two points.\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {number}\r\n */\r\nexport function getDistanceBetween(p1, p2) {\r\n  const x = Math.abs(p1.x - p2.x);\r\n  const y = Math.abs(p1.y - p2.y);\r\n  return Math.sqrt((x * x) + (y * y));\r\n}\r\n\r\n/**\r\n * Whether X and Y positions of points are equal\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {boolean}\r\n */\r\nexport function pointsEqual(p1, p2) {\r\n  return p1.x === p2.x && p1.y === p2.y;\r\n}\r\n\r\n/**\r\n * The float result between the min and max values.\r\n *\r\n * @param {number} val\r\n * @param {number} min\r\n * @param {number} max\r\n * @returns {number}\r\n */\r\nexport function clamp(val, min, max) {\r\n  return Math.min(Math.max(val, min), max);\r\n}\r\n\r\n/**\r\n * Get transform string\r\n *\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n * @returns {string}\r\n */\r\nexport function toTransformString(x, y, scale) {\r\n  let propValue = `translate3d(${x}px,${y || 0}px,0)`;\r\n\r\n  if (scale !== undefined) {\r\n    propValue += ` scale3d(${scale},${scale},1)`;\r\n  }\r\n\r\n  return propValue;\r\n}\r\n\r\n/**\r\n * Apply transform:translate(x, y) scale(scale) to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n */\r\nexport function setTransform(el, x, y, scale) {\r\n  el.style.transform = toTransformString(x, y, scale);\r\n}\r\n\r\nconst defaultCSSEasing = 'cubic-bezier(.4,0,.22,1)';\r\n\r\n/**\r\n * Apply CSS transition to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string} [prop] CSS property to animate\r\n * @param {number} [duration] in ms\r\n * @param {string} [ease] CSS easing function\r\n */\r\nexport function setTransitionStyle(el, prop, duration, ease) {\r\n  // inOut: 'cubic-bezier(.4, 0, .22, 1)', // for \"toggle state\" transitions\r\n  // out: 'cubic-bezier(0, 0, .22, 1)', // for \"show\" transitions\r\n  // in: 'cubic-bezier(.4, 0, 1, 1)'// for \"hide\" transitions\r\n  el.style.transition = prop\r\n    ? `${prop} ${duration}ms ${ease || defaultCSSEasing}`\r\n    : 'none';\r\n}\r\n\r\n/**\r\n * Apply width and height CSS properties to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string | number} w\r\n * @param {string | number} h\r\n */\r\nexport function setWidthHeight(el, w, h) {\r\n  el.style.width = (typeof w === 'number') ? `${w}px` : w;\r\n  el.style.height = (typeof h === 'number') ? `${h}px` : h;\r\n}\r\n\r\n/**\r\n * @param {HTMLElement} el\r\n */\r\nexport function removeTransitionStyle(el) {\r\n  setTransitionStyle(el);\r\n}\r\n\r\n/**\r\n * @param {HTMLImageElement} img\r\n * @returns {Promise<HTMLImageElement | void>}\r\n */\r\nexport function decodeImage(img) {\r\n  if ('decode' in img) {\r\n    return img.decode().catch(() => {});\r\n  }\r\n\r\n  if (img.complete) {\r\n    return Promise.resolve(img);\r\n  }\r\n\r\n  return new Promise((resolve, reject) => {\r\n    img.onload = () => resolve(img);\r\n    img.onerror = reject;\r\n  });\r\n}\r\n\r\n/** @typedef {LOAD_STATE[keyof LOAD_STATE]} LoadState */\r\n/** @type {{ IDLE: 'idle'; LOADING: 'loading'; LOADED: 'loaded'; ERROR: 'error' }} */\r\nexport const LOAD_STATE = {\r\n  IDLE: 'idle',\r\n  LOADING: 'loading',\r\n  LOADED: 'loaded',\r\n  ERROR: 'error',\r\n};\r\n\r\n\r\n/**\r\n * Check if click or keydown event was dispatched\r\n * with a special key or via mouse wheel.\r\n *\r\n * @param {MouseEvent | KeyboardEvent} e\r\n * @returns {boolean}\r\n */\r\nexport function specialKeyUsed(e) {\r\n  return ('button' in e && e.button === 1) || e.ctrlKey || e.metaKey || e.altKey || e.shiftKey;\r\n}\r\n\r\n/**\r\n * Parse `gallery` or `children` options.\r\n *\r\n * @param {import('../photoswipe.js').ElementProvider} [option]\r\n * @param {string} [legacySelector]\r\n * @param {HTMLElement | Document} [parent]\r\n * @returns HTMLElement[]\r\n */\r\nexport function getElementsFromOption(option, legacySelector, parent = document) {\r\n  /** @type {HTMLElement[]} */\r\n  let elements = [];\r\n\r\n  if (option instanceof Element) {\r\n    elements = [option];\r\n  } else if (option instanceof NodeList || Array.isArray(option)) {\r\n    elements = Array.from(option);\r\n  } else {\r\n    const selector = typeof option === 'string' ? option : legacySelector;\r\n    if (selector) {\r\n      elements = Array.from(parent.querySelectorAll(selector));\r\n    }\r\n  }\r\n\r\n  return elements;\r\n}\r\n\r\n/**\r\n * Check if variable is PhotoSwipe class\r\n *\r\n * @param {any} fn\r\n * @returns {boolean}\r\n */\r\nexport function isPswpClass(fn) {\r\n  return typeof fn === 'function'\r\n    && fn.prototype\r\n    && fn.prototype.goTo;\r\n}\r\n\r\n/**\r\n * Check if browser is Safari\r\n *\r\n * @returns {boolean}\r\n */\r\nexport function isSafari() {\r\n  return !!(navigator.vendor && navigator.vendor.match(/apple/i));\r\n}\r\n\r\n", "/** @typedef {import('../lightbox/lightbox.js').default} PhotoSwipeLightbox */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../photoswipe.js').DataSource} DataSource */\r\n/** @typedef {import('../ui/ui-element.js').UIElementData} UIElementData */\r\n/** @typedef {import('../slide/content.js').default} ContentDefault */\r\n/** @typedef {import('../slide/slide.js').default} Slide */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n/** @typedef {import('../slide/zoom-level.js').default} ZoomLevel */\r\n/** @typedef {import('../slide/get-thumb-bounds.js').Bounds} Bounds */\r\n\r\n/**\r\n * Allow adding an arbitrary props to the Content\r\n * https://photoswipe.com/custom-content/#using-webp-image-format\r\n * @typedef {ContentDefault & Record<string, any>} Content\r\n */\r\n/** @typedef {{ x?: number; y?: number }} Point */\r\n\r\n/**\r\n * @typedef {Object} PhotoSwipeEventsMap https://photoswipe.com/events/\r\n *\r\n *\r\n * https://photoswipe.com/adding-ui-elements/\r\n *\r\n * @prop {undefined} uiRegister\r\n * @prop {{ data: UIElementData }} uiElementCreate\r\n *\r\n *\r\n * https://photoswipe.com/events/#initialization-events\r\n *\r\n * @prop {undefined} beforeOpen\r\n * @prop {undefined} firstUpdate\r\n * @prop {undefined} initialLayout\r\n * @prop {undefined} change\r\n * @prop {undefined} afterInit\r\n * @prop {undefined} bindEvents\r\n *\r\n *\r\n * https://photoswipe.com/events/#opening-or-closing-transition-events\r\n *\r\n * @prop {undefined} openingAnimationStart\r\n * @prop {undefined} openingAnimationEnd\r\n * @prop {undefined} closingAnimationStart\r\n * @prop {undefined} closingAnimationEnd\r\n *\r\n *\r\n * https://photoswipe.com/events/#closing-events\r\n *\r\n * @prop {undefined} close\r\n * @prop {undefined} destroy\r\n *\r\n *\r\n * https://photoswipe.com/events/#pointer-and-gesture-events\r\n *\r\n * @prop {{ originalEvent: PointerEvent }} pointerDown\r\n * @prop {{ originalEvent: PointerEvent }} pointerMove\r\n * @prop {{ originalEvent: PointerEvent }} pointerUp\r\n * @prop {{ bgOpacity: number }} pinchClose can be default prevented\r\n * @prop {{ panY: number }} verticalDrag can be default prevented\r\n *\r\n *\r\n * https://photoswipe.com/events/#slide-content-events\r\n *\r\n * @prop {{ content: Content }} contentInit\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoad can be default prevented\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoadImage can be default prevented\r\n * @prop {{ content: Content; slide: Slide; isError?: boolean }} loadComplete\r\n * @prop {{ content: Content; slide: Slide }} loadError\r\n * @prop {{ content: Content; width: number; height: number }} contentResize can be default prevented\r\n * @prop {{ content: Content; width: number; height: number; slide: Slide }} imageSizeChange\r\n * @prop {{ content: Content }} contentLazyLoad can be default prevented\r\n * @prop {{ content: Content }} contentAppend can be default prevented\r\n * @prop {{ content: Content }} contentActivate can be default prevented\r\n * @prop {{ content: Content }} contentDeactivate can be default prevented\r\n * @prop {{ content: Content }} contentRemove can be default prevented\r\n * @prop {{ content: Content }} contentDestroy can be default prevented\r\n *\r\n *\r\n * undocumented\r\n *\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} imageClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} bgClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} tapAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} doubleTapAction can be default prevented\r\n *\r\n * @prop {{ originalEvent: KeyboardEvent }} keydown can be default prevented\r\n * @prop {{ x: number; dragging: boolean }} moveMainScroll\r\n * @prop {{ slide: Slide }} firstZoomPan\r\n * @prop {{ slide: Slide | undefined, data: SlideData, index: number }} gettingData\r\n * @prop {undefined} beforeResize\r\n * @prop {undefined} resize\r\n * @prop {undefined} viewportSize\r\n * @prop {undefined} updateScrollOffset\r\n * @prop {{ slide: Slide }} slideInit\r\n * @prop {{ slide: Slide }} afterSetContent\r\n * @prop {{ slide: Slide }} slideLoad\r\n * @prop {{ slide: Slide }} appendHeavy can be default prevented\r\n * @prop {{ slide: Slide }} appendHeavyContent\r\n * @prop {{ slide: Slide }} slideActivate\r\n * @prop {{ slide: Slide }} slideDeactivate\r\n * @prop {{ slide: Slide }} slideDestroy\r\n * @prop {{ destZoomLevel: number, centerPoint: Point | undefined, transitionDuration: number | false | undefined }} beforeZoomTo\r\n * @prop {{ slide: Slide }} zoomPanUpdate\r\n * @prop {{ slide: Slide }} initialZoomPan\r\n * @prop {{ slide: Slide }} calcSlideSize\r\n * @prop {undefined} resolutionChanged\r\n * @prop {{ originalEvent: WheelEvent }} wheel can be default prevented\r\n * @prop {{ content: Content }} contentAppendImage can be default prevented\r\n * @prop {{ index: number; itemData: SlideData }} lazyLoadSlide can be default prevented\r\n * @prop {undefined} lazyLoad\r\n * @prop {{ slide: Slide }} calcBounds\r\n * @prop {{ zoomLevels: ZoomLevel, slideData: SlideData }} zoomLevelsUpdate\r\n *\r\n *\r\n * legacy\r\n *\r\n * @prop {undefined} init\r\n * @prop {undefined} initialZoomIn\r\n * @prop {undefined} initialZoomOut\r\n * @prop {undefined} initialZoomInEnd\r\n * @prop {undefined} initialZoomOutEnd\r\n * @prop {{ dataSource: DataSource | undefined, numItems: number }} numItems\r\n * @prop {{ itemData: SlideData; index: number }} itemData\r\n * @prop {{ index: number, itemData: SlideData, instance: PhotoSwipe }} thumbBounds\r\n */\r\n\r\n/**\r\n * @typedef {Object} PhotoSwipeFiltersMap https://photoswipe.com/filters/\r\n *\r\n * @prop {(numItems: number, dataSource: DataSource | undefined) => number} numItems\r\n * Modify the total amount of slides. Example on Data sources page.\r\n * https://photoswipe.com/filters/#numitems\r\n *\r\n * @prop {(itemData: SlideData, index: number) => SlideData} itemData\r\n * Modify slide item data. Example on Data sources page.\r\n * https://photoswipe.com/filters/#itemdata\r\n *\r\n * @prop {(itemData: SlideData, element: HTMLElement, linkEl: HTMLAnchorElement) => SlideData} domItemData\r\n * Modify item data when it's parsed from DOM element. Example on Data sources page.\r\n * https://photoswipe.com/filters/#domitemdata\r\n *\r\n * @prop {(clickedIndex: number, e: MouseEvent, instance: PhotoSwipeLightbox) => number} clickedIndex\r\n * Modify clicked gallery item index.\r\n * https://photoswipe.com/filters/#clickedindex\r\n *\r\n * @prop {(placeholderSrc: string | false, content: Content) => string | false} placeholderSrc\r\n * Modify placeholder image source.\r\n * https://photoswipe.com/filters/#placeholdersrc\r\n *\r\n * @prop {(isContentLoading: boolean, content: Content) => boolean} isContentLoading\r\n * Modify if the content is currently loading.\r\n * https://photoswipe.com/filters/#iscontentloading\r\n *\r\n * @prop {(isContentZoomable: boolean, content: Content) => boolean} isContentZoomable\r\n * Modify if the content can be zoomed.\r\n * https://photoswipe.com/filters/#iscontentzoomable\r\n *\r\n * @prop {(useContentPlaceholder: boolean, content: Content) => boolean} useContentPlaceholder\r\n * Modify if the placeholder should be used for the content.\r\n * https://photoswipe.com/filters/#usecontentplaceholder\r\n *\r\n * @prop {(isKeepingPlaceholder: boolean, content: Content) => boolean} isKeepingPlaceholder\r\n * Modify if the placeholder should be kept after the content is loaded.\r\n * https://photoswipe.com/filters/#iskeepingplaceholder\r\n *\r\n *\r\n * @prop {(contentErrorElement: HTMLElement, content: Content) => HTMLElement} contentErrorElement\r\n * Modify an element when the content has error state (for example, if image cannot be loaded).\r\n * https://photoswipe.com/filters/#contenterrorelement\r\n *\r\n * @prop {(element: HTMLElement, data: UIElementData) => HTMLElement} uiElement\r\n * Modify a UI element that's being created.\r\n * https://photoswipe.com/filters/#uielement\r\n *\r\n * @prop {(thumbnail: HTMLElement | null | undefined, itemData: SlideData, index: number) => HTMLElement} thumbEl\r\n * Modify the thumbnail element from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbel\r\n *\r\n * @prop {(thumbBounds: Bounds | undefined, itemData: SlideData, index: number) => Bounds} thumbBounds\r\n * Modify the thumbnail bounds from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbbounds\r\n *\r\n * @prop {(srcsetSizesWidth: number, content: Content) => number} srcsetSizesWidth\r\n *\r\n * @prop {(preventPointerEvent: boolean, event: PointerEvent, pointerType: string) => boolean} preventPointerEvent\r\n *\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeFiltersMap} T\r\n * @typedef {{ fn: PhotoSwipeFiltersMap[T], priority: number }} Filter\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {PhotoSwipeEventsMap[T] extends undefined ? PhotoSwipeEvent<T> : PhotoSwipeEvent<T> & PhotoSwipeEventsMap[T]} AugmentedEvent\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {(event: AugmentedEvent<T>) => void} EventCallback\r\n */\r\n\r\n/**\r\n * Base PhotoSwipe event object\r\n *\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n */\r\nclass PhotoSwipeEvent {\r\n  /**\r\n   * @param {T} type\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   */\r\n  constructor(type, details) {\r\n    this.type = type;\r\n    this.defaultPrevented = false;\r\n    if (details) {\r\n      Object.assign(this, details);\r\n    }\r\n  }\r\n\r\n  preventDefault() {\r\n    this.defaultPrevented = true;\r\n  }\r\n}\r\n\r\n/**\r\n * PhotoSwipe base class that can listen and dispatch for events.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox, extended by base.js\r\n */\r\nclass Eventable {\r\n  constructor() {\r\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeEventsMap]?: ((event: AugmentedEvent<T>) => void)[] }}\r\n     */\r\n    this._listeners = {};\r\n\r\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeFiltersMap]?: Filter<T>[] }}\r\n     */\r\n    this._filters = {};\r\n\r\n    /** @type {PhotoSwipe | undefined} */\r\n    this.pswp = undefined;\r\n\r\n    /** @type {PhotoSwipeOptions | undefined} */\r\n    this.options = undefined;\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   * @param {number} priority\r\n   */\r\n  addFilter(name, fn, priority = 100) {\r\n    if (!this._filters[name]) {\r\n      this._filters[name] = [];\r\n    }\r\n\r\n    this._filters[name]?.push({ fn, priority });\r\n    this._filters[name]?.sort((f1, f2) => f1.priority - f2.priority);\r\n\r\n    this.pswp?.addFilter(name, fn, priority);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   */\r\n  removeFilter(name, fn) {\r\n    if (this._filters[name]) {\r\n      // @ts-expect-error\r\n      this._filters[name] = this._filters[name].filter(filter => (filter.fn !== fn));\r\n    }\r\n\r\n    if (this.pswp) {\r\n      this.pswp.removeFilter(name, fn);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {Parameters<PhotoSwipeFiltersMap[T]>} args\r\n   * @returns {Parameters<PhotoSwipeFiltersMap[T]>[0]}\r\n   */\r\n  applyFilters(name, ...args) {\r\n    this._filters[name]?.forEach((filter) => {\r\n      // @ts-expect-error\r\n      args[0] = filter.fn.apply(this, args);\r\n    });\r\n    return args[0];\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\r\n  on(name, fn) {\r\n    if (!this._listeners[name]) {\r\n      this._listeners[name] = [];\r\n    }\r\n    this._listeners[name]?.push(fn);\r\n\r\n    // When binding events to lightbox,\r\n    // also bind events to PhotoSwipe Core,\r\n    // if it's open.\r\n    this.pswp?.on(name, fn);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\r\n  off(name, fn) {\r\n    if (this._listeners[name]) {\r\n      // @ts-expect-error\r\n      this._listeners[name] = this._listeners[name].filter(listener => (fn !== listener));\r\n    }\r\n\r\n    this.pswp?.off(name, fn);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   * @returns {AugmentedEvent<T>}\r\n   */\r\n  dispatch(name, details) {\r\n    if (this.pswp) {\r\n      return this.pswp.dispatch(name, details);\r\n    }\r\n\r\n    const event = /** @type {AugmentedEvent<T>} */ (new PhotoSwipeEvent(name, details));\r\n\r\n    this._listeners[name]?.forEach((listener) => {\r\n      listener.call(this, event);\r\n    });\r\n\r\n    return event;\r\n  }\r\n}\r\n\r\nexport default Eventable;\r\n", "import { createElement, setWidthHeight, toTransformString } from '../util/util.js';\r\n\r\nclass Placeholder {\r\n  /**\r\n   * @param {string | false} imageSrc\r\n   * @param {HTMLElement} container\r\n   */\r\n  constructor(imageSrc, container) {\r\n    // Create placeholder\r\n    // (stretched thumbnail or simple div behind the main image)\r\n    /** @type {HTMLImageElement | HTMLDivElement | null} */\r\n    this.element = createElement(\r\n      'pswp__img pswp__img--placeholder',\r\n      imageSrc ? 'img' : 'div',\r\n      container\r\n    );\r\n\r\n    if (imageSrc) {\r\n      const imgEl = /** @type {HTMLImageElement} */ (this.element);\r\n      imgEl.decoding = 'async';\r\n      imgEl.alt = '';\r\n      imgEl.src = imageSrc;\r\n      imgEl.setAttribute('role', 'presentation');\r\n    }\r\n\r\n    this.element.setAttribute('aria-hidden', 'true');\r\n  }\r\n\r\n  /**\r\n   * @param {number} width\r\n   * @param {number} height\r\n   */\r\n  setDisplayedSize(width, height) {\r\n    if (!this.element) {\r\n      return;\r\n    }\r\n\r\n    if (this.element.tagName === 'IMG') {\r\n      // Use transform scale() to modify img placeholder size\r\n      // (instead of changing width/height directly).\r\n      // This helps with performance, specifically in iOS15 Safari.\r\n      setWidthHeight(this.element, 250, 'auto');\r\n      this.element.style.transformOrigin = '0 0';\r\n      this.element.style.transform = toTransformString(0, 0, width / 250);\r\n    } else {\r\n      setWidthHeight(this.element, width, height);\r\n    }\r\n  }\r\n\r\n  destroy() {\r\n    if (this.element?.parentNode) {\r\n      this.element.remove();\r\n    }\r\n    this.element = null;\r\n  }\r\n}\r\n\r\nexport default Placeholder;\r\n", "import { createElement, is<PERSON><PERSON><PERSON>, <PERSON>OAD_STATE, setWidthHeight } from '../util/util.js';\r\nimport Placeholder from './placeholder.js';\r\n\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../util/util.js').LoadState} LoadState */\r\n\r\nclass Content {\r\n  /**\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n   * @param {number} index\r\n   */\r\n  constructor(itemData, instance, index) {\r\n    this.instance = instance;\r\n    this.data = itemData;\r\n    this.index = index;\r\n\r\n    /** @type {HTMLImageElement | HTMLDivElement | undefined} */\r\n    this.element = undefined;\r\n    /** @type {Placeholder | undefined} */\r\n    this.placeholder = undefined;\r\n    /** @type {Slide | undefined} */\r\n    this.slide = undefined;\r\n\r\n    this.displayedImageWidth = 0;\r\n    this.displayedImageHeight = 0;\r\n\r\n    this.width = Number(this.data.w) || Number(this.data.width) || 0;\r\n    this.height = Number(this.data.h) || Number(this.data.height) || 0;\r\n\r\n    this.isAttached = false;\r\n    this.hasSlide = false;\r\n    this.isDecoding = false;\r\n    /** @type {LoadState} */\r\n    this.state = LOAD_STATE.IDLE;\r\n\r\n    if (this.data.type) {\r\n      this.type = this.data.type;\r\n    } else if (this.data.src) {\r\n      this.type = 'image';\r\n    } else {\r\n      this.type = 'html';\r\n    }\r\n\r\n    this.instance.dispatch('contentInit', { content: this });\r\n  }\r\n\r\n  removePlaceholder() {\r\n    if (this.placeholder && !this.keepPlaceholder()) {\r\n      // With delay, as image might be loaded, but not rendered\r\n      setTimeout(() => {\r\n        if (this.placeholder) {\r\n          this.placeholder.destroy();\r\n          this.placeholder = undefined;\r\n        }\r\n      }, 1000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Preload content\r\n   *\r\n   * @param {boolean} isLazy\r\n   * @param {boolean} [reload]\r\n   */\r\n  load(isLazy, reload) {\r\n    if (this.slide && this.usePlaceholder()) {\r\n      if (!this.placeholder) {\r\n        const placeholderSrc = this.instance.applyFilters(\r\n          'placeholderSrc',\r\n          // use  image-based placeholder only for the first slide,\r\n          // as rendering (even small stretched thumbnail) is an expensive operation\r\n          (this.data.msrc && this.slide.isFirstSlide) ? this.data.msrc : false,\r\n          this\r\n        );\r\n        this.placeholder = new Placeholder(\r\n          placeholderSrc,\r\n          this.slide.container\r\n        );\r\n      } else {\r\n        const placeholderEl = this.placeholder.element;\r\n        // Add placeholder to DOM if it was already created\r\n        if (placeholderEl && !placeholderEl.parentElement) {\r\n          this.slide.container.prepend(placeholderEl);\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.element && !reload) {\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentLoad', { content: this, isLazy }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this.isImageContent()) {\r\n      this.element = createElement('pswp__img', 'img');\r\n      // Start loading only after width is defined, as sizes might depend on it.\r\n      // Due to Safari feature, we must define sizes before srcset.\r\n      if (this.displayedImageWidth) {\r\n        this.loadImage(isLazy);\r\n      }\r\n    } else {\r\n      this.element = createElement('pswp__content', 'div');\r\n      this.element.innerHTML = this.data.html || '';\r\n    }\r\n\r\n    if (reload && this.slide) {\r\n      this.slide.updateContentSize(true);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Preload image\r\n   *\r\n   * @param {boolean} isLazy\r\n   */\r\n  loadImage(isLazy) {\r\n    if (!this.isImageContent()\r\n      || !this.element\r\n      || this.instance.dispatch('contentLoadImage', { content: this, isLazy }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const imageElement = /** @type HTMLImageElement */ (this.element);\r\n\r\n    this.updateSrcsetSizes();\r\n\r\n    if (this.data.srcset) {\r\n      imageElement.srcset = this.data.srcset;\r\n    }\r\n\r\n    imageElement.src = this.data.src ?? '';\r\n    imageElement.alt = this.data.alt ?? '';\r\n\r\n    this.state = LOAD_STATE.LOADING;\r\n\r\n    if (imageElement.complete) {\r\n      this.onLoaded();\r\n    } else {\r\n      imageElement.onload = () => {\r\n        this.onLoaded();\r\n      };\r\n\r\n      imageElement.onerror = () => {\r\n        this.onError();\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assign slide to content\r\n   *\r\n   * @param {Slide} slide\r\n   */\r\n  setSlide(slide) {\r\n    this.slide = slide;\r\n    this.hasSlide = true;\r\n    this.instance = slide.pswp;\r\n\r\n    // todo: do we need to unset slide?\r\n  }\r\n\r\n  /**\r\n   * Content load success handler\r\n   */\r\n  onLoaded() {\r\n    this.state = LOAD_STATE.LOADED;\r\n\r\n    if (this.slide && this.element) {\r\n      this.instance.dispatch('loadComplete', { slide: this.slide, content: this });\r\n\r\n      // if content is reloaded\r\n      if (this.slide.isActive\r\n          && this.slide.heavyAppended\r\n          && !this.element.parentNode) {\r\n        this.append();\r\n        this.slide.updateContentSize(true);\r\n      }\r\n\r\n      if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\r\n        this.removePlaceholder();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Content load error handler\r\n   */\r\n  onError() {\r\n    this.state = LOAD_STATE.ERROR;\r\n\r\n    if (this.slide) {\r\n      this.displayError();\r\n      this.instance.dispatch('loadComplete', { slide: this.slide, isError: true, content: this });\r\n      this.instance.dispatch('loadError', { slide: this.slide, content: this });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {Boolean} If the content is currently loading\r\n   */\r\n  isLoading() {\r\n    return this.instance.applyFilters(\r\n      'isContentLoading',\r\n      this.state === LOAD_STATE.LOADING,\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * @returns {Boolean} If the content is in error state\r\n   */\r\n  isError() {\r\n    return this.state === LOAD_STATE.ERROR;\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If the content is image\r\n   */\r\n  isImageContent() {\r\n    return this.type === 'image';\r\n  }\r\n\r\n  /**\r\n   * Update content size\r\n   *\r\n   * @param {Number} width\r\n   * @param {Number} height\r\n   */\r\n  setDisplayedSize(width, height) {\r\n    if (!this.element) {\r\n      return;\r\n    }\r\n\r\n    if (this.placeholder) {\r\n      this.placeholder.setDisplayedSize(width, height);\r\n    }\r\n\r\n    if (this.instance.dispatch(\r\n      'contentResize',\r\n      { content: this, width, height }).defaultPrevented\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    setWidthHeight(this.element, width, height);\r\n\r\n    if (this.isImageContent() && !this.isError()) {\r\n      const isInitialSizeUpdate = (!this.displayedImageWidth && width);\r\n\r\n      this.displayedImageWidth = width;\r\n      this.displayedImageHeight = height;\r\n\r\n      if (isInitialSizeUpdate) {\r\n        this.loadImage(false);\r\n      } else {\r\n        this.updateSrcsetSizes();\r\n      }\r\n\r\n      if (this.slide) {\r\n        this.instance.dispatch(\r\n          'imageSizeChange',\r\n          { slide: this.slide, width, height, content: this }\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If the content can be zoomed\r\n   */\r\n  isZoomable() {\r\n    return this.instance.applyFilters(\r\n      'isContentZoomable',\r\n      this.isImageContent() && (this.state !== LOAD_STATE.ERROR),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update image srcset sizes attribute based on width and height\r\n   */\r\n  updateSrcsetSizes() {\r\n    // Handle srcset sizes attribute.\r\n    //\r\n    // Never lower quality, if it was increased previously.\r\n    // Chrome does this automatically, Firefox and Safari do not,\r\n    // so we store largest used size in dataset.\r\n    if (!this.isImageContent() || !this.element || !this.data.srcset) {\r\n      return;\r\n    }\r\n\r\n    const image = /** @type HTMLImageElement */ (this.element);\r\n    const sizesWidth = this.instance.applyFilters(\r\n      'srcsetSizesWidth',\r\n      this.displayedImageWidth,\r\n      this\r\n    );\r\n\r\n    if (\r\n      !image.dataset.largestUsedSize\r\n      || sizesWidth > parseInt(image.dataset.largestUsedSize, 10)\r\n    ) {\r\n      image.sizes = sizesWidth + 'px';\r\n      image.dataset.largestUsedSize = String(sizesWidth);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If content should use a placeholder (from msrc by default)\r\n   */\r\n  usePlaceholder() {\r\n    return this.instance.applyFilters(\r\n      'useContentPlaceholder',\r\n      this.isImageContent(),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Preload content with lazy-loading param\r\n   */\r\n  lazyLoad() {\r\n    if (this.instance.dispatch('contentLazyLoad', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.load(true);\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If placeholder should be kept after content is loaded\r\n   */\r\n  keepPlaceholder() {\r\n    return this.instance.applyFilters(\r\n      'isKeepingPlaceholder',\r\n      this.isLoading(),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Destroy the content\r\n   */\r\n  destroy() {\r\n    this.hasSlide = false;\r\n    this.slide = undefined;\r\n\r\n    if (this.instance.dispatch('contentDestroy', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.remove();\r\n\r\n    if (this.placeholder) {\r\n      this.placeholder.destroy();\r\n      this.placeholder = undefined;\r\n    }\r\n\r\n    if (this.isImageContent() && this.element) {\r\n      this.element.onload = null;\r\n      this.element.onerror = null;\r\n      this.element = undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Display error message\r\n   */\r\n  displayError() {\r\n    if (this.slide) {\r\n      let errorMsgEl = createElement('pswp__error-msg', 'div');\r\n      errorMsgEl.innerText = this.instance.options?.errorMsg ?? '';\r\n      errorMsgEl = /** @type {HTMLDivElement} */ (this.instance.applyFilters(\r\n        'contentErrorElement',\r\n        errorMsgEl,\r\n        this\r\n      ));\r\n      this.element = createElement('pswp__content pswp__error-msg-container', 'div');\r\n      this.element.appendChild(errorMsgEl);\r\n      this.slide.container.innerText = '';\r\n      this.slide.container.appendChild(this.element);\r\n      this.slide.updateContentSize(true);\r\n      this.removePlaceholder();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Append the content\r\n   */\r\n  append() {\r\n    if (this.isAttached || !this.element) {\r\n      return;\r\n    }\r\n\r\n    this.isAttached = true;\r\n\r\n    if (this.state === LOAD_STATE.ERROR) {\r\n      this.displayError();\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentAppend', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const supportsDecode = ('decode' in this.element);\r\n\r\n    if (this.isImageContent()) {\r\n      // Use decode() on nearby slides\r\n      //\r\n      // Nearby slide images are in DOM and not hidden via display:none.\r\n      // However, they are placed offscreen (to the left and right side).\r\n      //\r\n      // Some browsers do not composite the image until it's actually visible,\r\n      // using decode() helps.\r\n      //\r\n      // You might ask \"why dont you just decode() and then append all images\",\r\n      // that's because I want to show image before it's fully loaded,\r\n      // as browser can render parts of image while it is loading.\r\n      // We do not do this in Safari due to partial loading bug.\r\n      if (supportsDecode && this.slide && (!this.slide.isActive || isSafari())) {\r\n        this.isDecoding = true;\r\n        // purposefully using finally instead of then,\r\n        // as if srcset sizes changes dynamically - it may cause decode error\r\n        /** @type {HTMLImageElement} */\r\n        (this.element).decode().catch(() => {}).finally(() => {\r\n          this.isDecoding = false;\r\n          this.appendImage();\r\n        });\r\n      } else {\r\n        this.appendImage();\r\n      }\r\n    } else if (this.slide && !this.element.parentNode) {\r\n      this.slide.container.appendChild(this.element);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Activate the slide,\r\n   * active slide is generally the current one,\r\n   * meaning the user can see it.\r\n   */\r\n  activate() {\r\n    if (this.instance.dispatch('contentActivate', { content: this }).defaultPrevented\r\n      || !this.slide) {\r\n      return;\r\n    }\r\n\r\n    if (this.isImageContent() && this.isDecoding && !isSafari()) {\r\n      // add image to slide when it becomes active,\r\n      // even if it's not finished decoding\r\n      this.appendImage();\r\n    } else if (this.isError()) {\r\n      this.load(false, true); // try to reload\r\n    }\r\n\r\n    if (this.slide.holderElement) {\r\n      this.slide.holderElement.setAttribute('aria-hidden', 'false');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deactivate the content\r\n   */\r\n  deactivate() {\r\n    this.instance.dispatch('contentDeactivate', { content: this });\r\n    if (this.slide && this.slide.holderElement) {\r\n      this.slide.holderElement.setAttribute('aria-hidden', 'true');\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * Remove the content from DOM\r\n   */\r\n  remove() {\r\n    this.isAttached = false;\r\n\r\n    if (this.instance.dispatch('contentRemove', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this.element && this.element.parentNode) {\r\n      this.element.remove();\r\n    }\r\n\r\n    if (this.placeholder && this.placeholder.element) {\r\n      this.placeholder.element.remove();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Append the image content to slide container\r\n   */\r\n  appendImage() {\r\n    if (!this.isAttached) {\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentAppendImage', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    // ensure that element exists and is not already appended\r\n    if (this.slide && this.element && !this.element.parentNode) {\r\n      this.slide.container.appendChild(this.element);\r\n    }\r\n\r\n    if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\r\n      this.removePlaceholder();\r\n    }\r\n  }\r\n}\r\n\r\nexport default Content;\r\n", "/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n\r\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {PhotoSwipeBase} pswp\r\n * @returns {Point}\r\n */\r\nexport function getViewportSize(options, pswp) {\r\n  if (options.getViewportSizeFn) {\r\n    const newViewportSize = options.getViewportSizeFn(options, pswp);\r\n    if (newViewportSize) {\r\n      return newViewportSize;\r\n    }\r\n  }\r\n\r\n  return {\r\n    x: document.documentElement.clientWidth,\r\n\r\n    // TODO: height on mobile is very incosistent due to toolbar\r\n    // find a way to improve this\r\n    //\r\n    // document.documentElement.clientHeight - doesn't seem to work well\r\n    y: window.innerHeight\r\n  };\r\n}\r\n\r\n/**\r\n * Parses padding option.\r\n * Supported formats:\r\n *\r\n * // Object\r\n * padding: {\r\n *  top: 0,\r\n *  bottom: 0,\r\n *  left: 0,\r\n *  right: 0\r\n * }\r\n *\r\n * // A function that returns the object\r\n * paddingFn: (viewportSize, itemData, index) => {\r\n *  return {\r\n *    top: 0,\r\n *    bottom: 0,\r\n *    left: 0,\r\n *    right: 0\r\n *  };\r\n * }\r\n *\r\n * // Legacy variant\r\n * paddingLeft: 0,\r\n * paddingRight: 0,\r\n * paddingTop: 0,\r\n * paddingBottom: 0,\r\n *\r\n * @param {'left' | 'top' | 'bottom' | 'right'} prop\r\n * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n * @param {Point} viewportSize PhotoSwipe viewport size, for example: { x:800, y:600 }\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {number} index Slide index\r\n * @returns {number}\r\n */\r\nexport function parsePaddingOption(prop, options, viewportSize, itemData, index) {\r\n  let paddingValue = 0;\r\n\r\n  if (options.paddingFn) {\r\n    paddingValue = options.paddingFn(viewportSize, itemData, index)[prop];\r\n  } else if (options.padding) {\r\n    paddingValue = options.padding[prop];\r\n  } else {\r\n    const legacyPropName = 'padding' + prop[0].toUpperCase() + prop.slice(1);\r\n    // @ts-expect-error\r\n    if (options[legacyPropName]) {\r\n      // @ts-expect-error\r\n      paddingValue = options[legacyPropName];\r\n    }\r\n  }\r\n\r\n  return Number(paddingValue) || 0;\r\n}\r\n\r\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {Point} viewportSize\r\n * @param {SlideData} itemData\r\n * @param {number} index\r\n * @returns {Point}\r\n */\r\nexport function getPanAreaSize(options, viewportSize, itemData, index) {\r\n  return {\r\n    x: viewportSize.x\r\n      - parsePaddingOption('left', options, viewportSize, itemData, index)\r\n      - parsePaddingOption('right', options, viewportSize, itemData, index),\r\n    y: viewportSize.y\r\n      - parsePaddingOption('top', options, viewportSize, itemData, index)\r\n      - parsePaddingOption('bottom', options, viewportSize, itemData, index)\r\n  };\r\n}\r\n", "const MAX_IMAGE_WIDTH = 4000;\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n\r\n/** @typedef {'fit' | 'fill' | number | ((zoomLevelObject: ZoomLevel) => number)} ZoomLevelOption */\r\n\r\n/**\r\n * Calculates zoom levels for specific slide.\r\n * Depends on viewport size and image size.\r\n */\r\nclass ZoomLevel {\r\n  /**\r\n   * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {number} index Slide index\r\n   * @param {PhotoSwipe} [pswp] PhotoSwipe instance, can be undefined if not initialized yet\r\n   */\r\n  constructor(options, itemData, index, pswp) {\r\n    this.pswp = pswp;\r\n    this.options = options;\r\n    this.itemData = itemData;\r\n    this.index = index;\r\n    /** @type { Point | null } */\r\n    this.panAreaSize = null;\r\n    /** @type { Point | null } */\r\n    this.elementSize = null;\r\n    this.fit = 1;\r\n    this.fill = 1;\r\n    this.vFill = 1;\r\n    this.initial = 1;\r\n    this.secondary = 1;\r\n    this.max = 1;\r\n    this.min = 1;\r\n  }\r\n\r\n  /**\r\n   * Calculate initial, secondary and maximum zoom level for the specified slide.\r\n   *\r\n   * It should be called when either image or viewport size changes.\r\n   *\r\n   * @param {number} maxWidth\r\n   * @param {number} maxHeight\r\n   * @param {Point} panAreaSize\r\n   */\r\n  update(maxWidth, maxHeight, panAreaSize) {\r\n    /** @type {Point} */\r\n    const elementSize = { x: maxWidth, y: maxHeight };\r\n    this.elementSize = elementSize;\r\n    this.panAreaSize = panAreaSize;\r\n\r\n    const hRatio = panAreaSize.x / elementSize.x;\r\n    const vRatio = panAreaSize.y / elementSize.y;\r\n\r\n    this.fit = Math.min(1, hRatio < vRatio ? hRatio : vRatio);\r\n    this.fill = Math.min(1, hRatio > vRatio ? hRatio : vRatio);\r\n\r\n    // zoom.vFill defines zoom level of the image\r\n    // when it has 100% of viewport vertical space (height)\r\n    this.vFill = Math.min(1, vRatio);\r\n\r\n    this.initial = this._getInitial();\r\n    this.secondary = this._getSecondary();\r\n    this.max = Math.max(\r\n      this.initial,\r\n      this.secondary,\r\n      this._getMax()\r\n    );\r\n\r\n    this.min = Math.min(\r\n      this.fit,\r\n      this.initial,\r\n      this.secondary\r\n    );\r\n\r\n    if (this.pswp) {\r\n      this.pswp.dispatch('zoomLevelsUpdate', { zoomLevels: this, slideData: this.itemData });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parses user-defined zoom option.\r\n   *\r\n   * @private\r\n   * @param {'initial' | 'secondary' | 'max'} optionPrefix Zoom level option prefix (initial, secondary, max)\r\n   * @returns { number | undefined }\r\n   */\r\n  _parseZoomLevelOption(optionPrefix) {\r\n    const optionName = /** @type {'initialZoomLevel' | 'secondaryZoomLevel' | 'maxZoomLevel'} */ (\r\n      optionPrefix + 'ZoomLevel'\r\n    );\r\n    const optionValue = this.options[optionName];\r\n\r\n    if (!optionValue) {\r\n      return;\r\n    }\r\n\r\n    if (typeof optionValue === 'function') {\r\n      return optionValue(this);\r\n    }\r\n\r\n    if (optionValue === 'fill') {\r\n      return this.fill;\r\n    }\r\n\r\n    if (optionValue === 'fit') {\r\n      return this.fit;\r\n    }\r\n\r\n    return Number(optionValue);\r\n  }\r\n\r\n  /**\r\n   * Get zoom level to which image will be zoomed after double-tap gesture,\r\n   * or when user clicks on zoom icon,\r\n   * or mouse-click on image itself.\r\n   * If you return 1 image will be zoomed to its original size.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getSecondary() {\r\n    let currZoomLevel = this._parseZoomLevelOption('secondary');\r\n\r\n    if (currZoomLevel) {\r\n      return currZoomLevel;\r\n    }\r\n\r\n    // 3x of \"fit\" state, but not larger than original\r\n    currZoomLevel = Math.min(1, this.fit * 3);\r\n\r\n    if (this.elementSize && currZoomLevel * this.elementSize.x > MAX_IMAGE_WIDTH) {\r\n      currZoomLevel = MAX_IMAGE_WIDTH / this.elementSize.x;\r\n    }\r\n\r\n    return currZoomLevel;\r\n  }\r\n\r\n  /**\r\n   * Get initial image zoom level.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getInitial() {\r\n    return this._parseZoomLevelOption('initial') || this.fit;\r\n  }\r\n\r\n  /**\r\n   * Maximum zoom level when user zooms\r\n   * via zoom/pinch gesture,\r\n   * via cmd/ctrl-wheel or via trackpad.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getMax() {\r\n    // max zoom level is x4 from \"fit state\",\r\n    // used for zoom gesture and ctrl/trackpad zoom\r\n    return this._parseZoomLevelOption('max') || Math.max(1, this.fit * 4);\r\n  }\r\n}\r\n\r\nexport default ZoomLevel;\r\n", "import { getViewportSize, getPanAreaSize } from '../util/viewport-size.js';\r\nimport ZoomLevel from './zoom-level.js';\r\n\r\n/** @typedef {import('./content.js').default} Content */\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\nconst MIN_SLIDES_TO_CACHE = 5;\r\n\r\n/**\r\n * Lazy-load an image\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n * @param {number} index\r\n * @returns {Content} Image that is being decoded or false.\r\n */\r\nexport function lazyLoadData(itemData, instance, index) {\r\n  const content = instance.createContentFromData(itemData, index);\r\n  /** @type {ZoomLevel | undefined} */\r\n  let zoomLevel;\r\n\r\n  const { options } = instance;\r\n\r\n  // We need to know dimensions of the image to preload it,\r\n  // as it might use srcset, and we need to define sizes\r\n  if (options) {\r\n    zoomLevel = new ZoomLevel(options, itemData, -1);\r\n\r\n    let viewportSize;\r\n    if (instance.pswp) {\r\n      viewportSize = instance.pswp.viewportSize;\r\n    } else {\r\n      viewportSize = getViewportSize(options, instance);\r\n    }\r\n\r\n    const panAreaSize = getPanAreaSize(options, viewportSize, itemData, index);\r\n    zoomLevel.update(content.width, content.height, panAreaSize);\r\n  }\r\n\r\n  content.lazyLoad();\r\n\r\n  if (zoomLevel) {\r\n    content.setDisplayedSize(\r\n      Math.ceil(content.width * zoomLevel.initial),\r\n      Math.ceil(content.height * zoomLevel.initial)\r\n    );\r\n  }\r\n\r\n  return content;\r\n}\r\n\r\n\r\n/**\r\n * Lazy-loads specific slide.\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * By default, it loads image based on viewport size and initial zoom level.\r\n *\r\n * @param {number} index Slide index\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox eventable instance\r\n * @returns {Content | undefined}\r\n */\r\nexport function lazyLoadSlide(index, instance) {\r\n  const itemData = instance.getItemData(index);\r\n\r\n  if (instance.dispatch('lazyLoadSlide', { index, itemData }).defaultPrevented) {\r\n    return;\r\n  }\r\n\r\n  return lazyLoadData(itemData, instance, index);\r\n}\r\n\r\nclass ContentLoader {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    // Total amount of cached images\r\n    this.limit = Math.max(\r\n      pswp.options.preload[0] + pswp.options.preload[1] + 1,\r\n      MIN_SLIDES_TO_CACHE\r\n    );\r\n    /** @type {Content[]} */\r\n    this._cachedItems = [];\r\n  }\r\n\r\n  /**\r\n   * Lazy load nearby slides based on `preload` option.\r\n   *\r\n   * @param {number} [diff] Difference between slide indexes that was changed recently, or 0.\r\n   */\r\n  updateLazy(diff) {\r\n    const { pswp } = this;\r\n\r\n    if (pswp.dispatch('lazyLoad').defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const { preload } = pswp.options;\r\n    const isForward = diff === undefined ? true : (diff >= 0);\r\n    let i;\r\n\r\n    // preload[1] - num items to preload in forward direction\r\n    for (i = 0; i <= preload[1]; i++) {\r\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? i : (-i)));\r\n    }\r\n\r\n    // preload[0] - num items to preload in backward direction\r\n    for (i = 1; i <= preload[0]; i++) {\r\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? (-i) : i));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} initialIndex\r\n   */\r\n  loadSlideByIndex(initialIndex) {\r\n    const index = this.pswp.getLoopedIndex(initialIndex);\r\n    // try to get cached content\r\n    let content = this.getContentByIndex(index);\r\n    if (!content) {\r\n      // no cached content, so try to load from scratch:\r\n      content = lazyLoadSlide(index, this.pswp);\r\n      // if content can be loaded, add it to cache:\r\n      if (content) {\r\n        this.addToCache(content);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Slide} slide\r\n   * @returns {Content}\r\n   */\r\n  getContentBySlide(slide) {\r\n    let content = this.getContentByIndex(slide.index);\r\n    if (!content) {\r\n      // create content if not found in cache\r\n      content = this.pswp.createContentFromData(slide.data, slide.index);\r\n      this.addToCache(content);\r\n    }\r\n\r\n    // assign slide to content\r\n    content.setSlide(slide);\r\n\r\n    return content;\r\n  }\r\n\r\n  /**\r\n   * @param {Content} content\r\n   */\r\n  addToCache(content) {\r\n    // move to the end of array\r\n    this.removeByIndex(content.index);\r\n    this._cachedItems.push(content);\r\n\r\n    if (this._cachedItems.length > this.limit) {\r\n      // Destroy the first content that's not attached\r\n      const indexToRemove = this._cachedItems.findIndex((item) => {\r\n        return !item.isAttached && !item.hasSlide;\r\n      });\r\n      if (indexToRemove !== -1) {\r\n        const removedItem = this._cachedItems.splice(indexToRemove, 1)[0];\r\n        removedItem.destroy();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes an image from cache, does not destroy() it, just removes.\r\n   *\r\n   * @param {number} index\r\n   */\r\n  removeByIndex(index) {\r\n    const indexToRemove = this._cachedItems.findIndex(item => item.index === index);\r\n    if (indexToRemove !== -1) {\r\n      this._cachedItems.splice(indexToRemove, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} index\r\n   * @returns {Content | undefined}\r\n   */\r\n  getContentByIndex(index) {\r\n    return this._cachedItems.find(content => content.index === index);\r\n  }\r\n\r\n  destroy() {\r\n    this._cachedItems.forEach(content => content.destroy());\r\n    this._cachedItems = [];\r\n  }\r\n}\r\n\r\nexport default ContentLoader;\r\n", "import Eventable from './eventable.js';\r\nimport { getElementsFromOption } from '../util/util.js';\r\nimport Content from '../slide/content.js';\r\nimport { lazyLoadData } from '../slide/loader.js';\r\n\r\n/** @typedef {import(\"../photoswipe.js\").default} PhotoSwipe */\r\n/** @typedef {import(\"../slide/slide.js\").SlideData} SlideData */\r\n\r\n/**\r\n * PhotoSwipe base class that can retrieve data about every slide.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox\r\n */\r\nclass PhotoSwipeBase extends Eventable {\r\n  /**\r\n   * Get total number of slides\r\n   *\r\n   * @returns {number}\r\n   */\r\n  getNumItems() {\r\n    let numItems = 0;\r\n    const dataSource = this.options?.dataSource;\r\n\r\n    if (dataSource && 'length' in dataSource) {\r\n      // may be an array or just object with length property\r\n      numItems = dataSource.length;\r\n    } else if (dataSource && 'gallery' in dataSource) {\r\n      // query DOM elements\r\n      if (!dataSource.items) {\r\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\r\n      }\r\n\r\n      if (dataSource.items) {\r\n        numItems = dataSource.items.length;\r\n      }\r\n    }\r\n\r\n    // legacy event, before filters were introduced\r\n    const event = this.dispatch('numItems', {\r\n      dataSource,\r\n      numItems\r\n    });\r\n    return this.applyFilters('numItems', event.numItems, dataSource);\r\n  }\r\n\r\n  /**\r\n   * @param {SlideData} slideData\r\n   * @param {number} index\r\n   * @returns {Content}\r\n   */\r\n  createContentFromData(slideData, index) {\r\n    return new Content(slideData, this, index);\r\n  }\r\n\r\n  /**\r\n   * Get item data by index.\r\n   *\r\n   * \"item data\" should contain normalized information that PhotoSwipe needs to generate a slide.\r\n   * For example, it may contain properties like\r\n   * `src`, `srcset`, `w`, `h`, which will be used to generate a slide with image.\r\n   *\r\n   * @param {number} index\r\n   * @returns {SlideData}\r\n   */\r\n  getItemData(index) {\r\n    const dataSource = this.options?.dataSource;\r\n    /** @type {SlideData | HTMLElement} */\r\n    let dataSourceItem = {};\r\n    if (Array.isArray(dataSource)) {\r\n      // Datasource is an array of elements\r\n      dataSourceItem = dataSource[index];\r\n    } else if (dataSource && 'gallery' in dataSource) {\r\n      // dataSource has gallery property,\r\n      // thus it was created by Lightbox, based on\r\n      // gallery and children options\r\n\r\n      // query DOM elements\r\n      if (!dataSource.items) {\r\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\r\n      }\r\n\r\n      dataSourceItem = dataSource.items[index];\r\n    }\r\n\r\n    let itemData = dataSourceItem;\r\n\r\n    if (itemData instanceof Element) {\r\n      itemData = this._domElementToItemData(itemData);\r\n    }\r\n\r\n    // Dispatching the itemData event,\r\n    // it's a legacy verion before filters were introduced\r\n    const event = this.dispatch('itemData', {\r\n      itemData: itemData || {},\r\n      index\r\n    });\r\n\r\n    return this.applyFilters('itemData', event.itemData, index);\r\n  }\r\n\r\n  /**\r\n   * Get array of gallery DOM elements,\r\n   * based on childSelector and gallery element.\r\n   *\r\n   * @param {HTMLElement} galleryElement\r\n   * @returns {HTMLElement[]}\r\n   */\r\n  _getGalleryDOMElements(galleryElement) {\r\n    if (this.options?.children || this.options?.childSelector) {\r\n      return getElementsFromOption(\r\n        this.options.children,\r\n        this.options.childSelector,\r\n        galleryElement\r\n      ) || [];\r\n    }\r\n\r\n    return [galleryElement];\r\n  }\r\n\r\n  /**\r\n   * Converts DOM element to item data object.\r\n   *\r\n   * @param {HTMLElement} element DOM element\r\n   * @returns {SlideData}\r\n   */\r\n  _domElementToItemData(element) {\r\n    /** @type {SlideData} */\r\n    const itemData = {\r\n      element\r\n    };\r\n\r\n    const linkEl = /** @type {HTMLAnchorElement} */ (\r\n      element.tagName === 'A'\r\n        ? element\r\n        : element.querySelector('a')\r\n    );\r\n\r\n    if (linkEl) {\r\n      // src comes from data-pswp-src attribute,\r\n      // if it's empty link href is used\r\n      itemData.src = linkEl.dataset.pswpSrc || linkEl.href;\r\n\r\n      if (linkEl.dataset.pswpSrcset) {\r\n        itemData.srcset = linkEl.dataset.pswpSrcset;\r\n      }\r\n\r\n      itemData.width = linkEl.dataset.pswpWidth ? parseInt(linkEl.dataset.pswpWidth, 10) : 0;\r\n      itemData.height = linkEl.dataset.pswpHeight ? parseInt(linkEl.dataset.pswpHeight, 10) : 0;\r\n\r\n      // support legacy w & h properties\r\n      itemData.w = itemData.width;\r\n      itemData.h = itemData.height;\r\n\r\n      if (linkEl.dataset.pswpType) {\r\n        itemData.type = linkEl.dataset.pswpType;\r\n      }\r\n\r\n      const thumbnailEl = element.querySelector('img');\r\n\r\n      if (thumbnailEl) {\r\n        // msrc is URL to placeholder image that's displayed before large image is loaded\r\n        // by default it's displayed only for the first slide\r\n        itemData.msrc = thumbnailEl.currentSrc || thumbnailEl.src;\r\n        itemData.alt = thumbnailEl.getAttribute('alt') ?? '';\r\n      }\r\n\r\n      if (linkEl.dataset.pswpCropped || linkEl.dataset.cropped) {\r\n        itemData.thumbCropped = true;\r\n      }\r\n    }\r\n\r\n    return this.applyFilters('domItemData', itemData, element, linkEl);\r\n  }\r\n\r\n  /**\r\n   * Lazy-load by slide data\r\n   *\r\n   * @param {SlideData} itemData Data about the slide\r\n   * @param {number} index\r\n   * @returns {Content} Image that is being decoded or false.\r\n   */\r\n  lazyLoadData(itemData, index) {\r\n    return lazyLoadData(itemData, this, index);\r\n  }\r\n}\r\n\r\nexport default PhotoSwipeBase;\r\n", "import {\r\n  specialKeyUsed,\r\n  getElementsFromOption,\r\n  isPswpClass\r\n} from '../util/util.js';\r\n\r\nimport PhotoSwipeBase from '../core/base.js';\r\nimport { lazyLoadSlide } from '../slide/loader.js';\r\n\r\n/**\r\n * @template T\r\n * @typedef {import('../types.js').Type<T>} Type<T>\r\n */\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../photoswipe.js').DataSource} DataSource */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('../slide/content.js').default} Content */\r\n/** @typedef {import('../core/eventable.js').PhotoSwipeEventsMap} PhotoSwipeEventsMap */\r\n/** @typedef {import('../core/eventable.js').PhotoSwipeFiltersMap} PhotoSwipeFiltersMap */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {import('../core/eventable.js').EventCallback<T>} EventCallback<T>\r\n */\r\n\r\n/**\r\n * PhotoSwipe Lightbox\r\n *\r\n * - If user has unsupported browser it falls back to default browser action (just opens URL)\r\n * - Binds click event to links that should open PhotoSwipe\r\n * - parses DOM strcture for PhotoSwipe (retrieves large image URLs and sizes)\r\n * - Initializes PhotoSwipe\r\n *\r\n *\r\n * Loader options use the same object as PhotoSwipe, and supports such options:\r\n *\r\n * gallery - Element | Element[] | NodeList | string selector for the gallery element\r\n * children - Element | Element[] | NodeList | string selector for the gallery children\r\n *\r\n */\r\nclass PhotoSwipeLightbox extends PhotoSwipeBase {\r\n  /**\r\n   * @param {PhotoSwipeOptions} [options]\r\n   */\r\n  constructor(options) {\r\n    super();\r\n    /** @type {PhotoSwipeOptions} */\r\n    this.options = options || {};\r\n    this._uid = 0;\r\n    this.shouldOpen = false;\r\n    /**\r\n     * @private\r\n     * @type {Content | undefined}\r\n     */\r\n    this._preloadedContent = undefined;\r\n\r\n    this.onThumbnailsClick = this.onThumbnailsClick.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Initialize lightbox, should be called only once.\r\n   * It's not included in the main constructor, so you may bind events before it.\r\n   */\r\n  init() {\r\n    // Bind click events to each gallery\r\n    getElementsFromOption(this.options.gallery, this.options.gallerySelector)\r\n      .forEach((galleryElement) => {\r\n        galleryElement.addEventListener('click', this.onThumbnailsClick, false);\r\n      });\r\n  }\r\n\r\n  /**\r\n   * @param {MouseEvent} e\r\n   */\r\n  onThumbnailsClick(e) {\r\n    // Exit and allow default browser action if:\r\n    if (specialKeyUsed(e) // ... if clicked with a special key (ctrl/cmd...)\r\n        || window.pswp) { // ... if PhotoSwipe is already open\r\n      return;\r\n    }\r\n\r\n    // If both clientX and clientY are 0 or not defined,\r\n    // the event is likely triggered by keyboard,\r\n    // so we do not pass the initialPoint\r\n    //\r\n    // Note that some screen readers emulate the mouse position,\r\n    // so it's not the ideal way to detect them.\r\n    //\r\n    /** @type {Point | null} */\r\n    let initialPoint = { x: e.clientX, y: e.clientY };\r\n\r\n    if (!initialPoint.x && !initialPoint.y) {\r\n      initialPoint = null;\r\n    }\r\n\r\n    let clickedIndex = this.getClickedIndex(e);\r\n    clickedIndex = this.applyFilters('clickedIndex', clickedIndex, e, this);\r\n    /** @type {DataSource} */\r\n    const dataSource = {\r\n      gallery: /** @type {HTMLElement} */ (e.currentTarget)\r\n    };\r\n\r\n    if (clickedIndex >= 0) {\r\n      e.preventDefault();\r\n      this.loadAndOpen(clickedIndex, dataSource, initialPoint);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get index of gallery item that was clicked.\r\n   *\r\n   * @param {MouseEvent} e click event\r\n   * @returns {number}\r\n   */\r\n  getClickedIndex(e) {\r\n    // legacy option\r\n    if (this.options.getClickedIndexFn) {\r\n      return this.options.getClickedIndexFn.call(this, e);\r\n    }\r\n\r\n    const clickedTarget = /** @type {HTMLElement} */ (e.target);\r\n    const childElements = getElementsFromOption(\r\n      this.options.children,\r\n      this.options.childSelector,\r\n      /** @type {HTMLElement} */ (e.currentTarget)\r\n    );\r\n    const clickedChildIndex = childElements.findIndex(\r\n      child => child === clickedTarget || child.contains(clickedTarget)\r\n    );\r\n\r\n    if (clickedChildIndex !== -1) {\r\n      return clickedChildIndex;\r\n    } else if (this.options.children || this.options.childSelector) {\r\n      // click wasn't on a child element\r\n      return -1;\r\n    }\r\n\r\n    // There is only one item (which is the gallery)\r\n    return 0;\r\n  }\r\n\r\n  /**\r\n   * Load and open PhotoSwipe\r\n   *\r\n   * @param {number} index\r\n   * @param {DataSource} [dataSource]\r\n   * @param {Point | null} [initialPoint]\r\n   * @returns {boolean}\r\n   */\r\n  loadAndOpen(index, dataSource, initialPoint) {\r\n    // Check if the gallery is already open\r\n    if (window.pswp || !this.options) {\r\n      return false;\r\n    }\r\n\r\n    // Use the first gallery element if dataSource is not provided\r\n    if (!dataSource && this.options.gallery && this.options.children) {\r\n      const galleryElements = getElementsFromOption(this.options.gallery);\r\n      if (galleryElements[0]) {\r\n        dataSource = {\r\n          gallery: galleryElements[0]\r\n        };\r\n      }\r\n    }\r\n\r\n    // set initial index\r\n    this.options.index = index;\r\n\r\n    // define options for PhotoSwipe constructor\r\n    this.options.initialPointerPos = initialPoint;\r\n\r\n    this.shouldOpen = true;\r\n    this.preload(index, dataSource);\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Load the main module and the slide content by index\r\n   *\r\n   * @param {number} index\r\n   * @param {DataSource} [dataSource]\r\n   */\r\n  preload(index, dataSource) {\r\n    const { options } = this;\r\n\r\n    if (dataSource) {\r\n      options.dataSource = dataSource;\r\n    }\r\n\r\n    // Add the main module\r\n    /** @type {Promise<Type<PhotoSwipe>>[]} */\r\n    const promiseArray = [];\r\n\r\n    const pswpModuleType = typeof options.pswpModule;\r\n    if (isPswpClass(options.pswpModule)) {\r\n      promiseArray.push(Promise.resolve(/** @type {Type<PhotoSwipe>} */ (options.pswpModule)));\r\n    } else if (pswpModuleType === 'string') {\r\n      throw new Error('pswpModule as string is no longer supported');\r\n    } else if (pswpModuleType === 'function') {\r\n      promiseArray.push(/** @type {() => Promise<Type<PhotoSwipe>>} */ (options.pswpModule)());\r\n    } else {\r\n      throw new Error('pswpModule is not valid');\r\n    }\r\n\r\n    // Add custom-defined promise, if any\r\n    if (typeof options.openPromise === 'function') {\r\n      // allow developers to perform some task before opening\r\n      promiseArray.push(options.openPromise());\r\n    }\r\n\r\n    if (options.preloadFirstSlide !== false && index >= 0) {\r\n      this._preloadedContent = lazyLoadSlide(index, this);\r\n    }\r\n\r\n    // Wait till all promises resolve and open PhotoSwipe\r\n    const uid = ++this._uid;\r\n    Promise.all(promiseArray).then((iterableModules) => {\r\n      if (this.shouldOpen) {\r\n        const mainModule = iterableModules[0];\r\n        this._openPhotoswipe(mainModule, uid);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {Type<PhotoSwipe> | { default: Type<PhotoSwipe> }} module\r\n   * @param {number} uid\r\n   */\r\n  _openPhotoswipe(module, uid) {\r\n    // Cancel opening if UID doesn't match the current one\r\n    // (if user clicked on another gallery item before current was loaded).\r\n    //\r\n    // Or if shouldOpen flag is set to false\r\n    // (developer may modify it via public API)\r\n    if (uid !== this._uid && this.shouldOpen) {\r\n      return;\r\n    }\r\n\r\n    this.shouldOpen = false;\r\n\r\n    // PhotoSwipe is already open\r\n    if (window.pswp) {\r\n      return;\r\n    }\r\n\r\n    /**\r\n     * Pass data to PhotoSwipe and open init\r\n     *\r\n     * @type {PhotoSwipe}\r\n     */\r\n    const pswp = typeof module === 'object'\r\n        ? new module.default(this.options) // eslint-disable-line\r\n        : new module(this.options); // eslint-disable-line\r\n\r\n    this.pswp = pswp;\r\n    window.pswp = pswp;\r\n\r\n    // map listeners from Lightbox to PhotoSwipe Core\r\n    /** @type {(keyof PhotoSwipeEventsMap)[]} */\r\n    (Object.keys(this._listeners)).forEach((name) => {\r\n      this._listeners[name]?.forEach((fn) => {\r\n        pswp.on(name, /** @type {EventCallback<typeof name>} */(fn));\r\n      });\r\n    });\r\n\r\n    // same with filters\r\n    /** @type {(keyof PhotoSwipeFiltersMap)[]} */\r\n    (Object.keys(this._filters)).forEach((name) => {\r\n      this._filters[name]?.forEach((filter) => {\r\n        pswp.addFilter(name, filter.fn, filter.priority);\r\n      });\r\n    });\r\n\r\n    if (this._preloadedContent) {\r\n      pswp.contentLoader.addToCache(this._preloadedContent);\r\n      this._preloadedContent = undefined;\r\n    }\r\n\r\n    pswp.on('destroy', () => {\r\n      // clean up public variables\r\n      this.pswp = undefined;\r\n      delete window.pswp;\r\n    });\r\n\r\n    pswp.init();\r\n  }\r\n\r\n  /**\r\n   * Unbinds all events, closes PhotoSwipe if it's open.\r\n   */\r\n  destroy() {\r\n    this.pswp?.destroy();\r\n\r\n    this.shouldOpen = false;\r\n    this._listeners = {};\r\n\r\n    getElementsFromOption(this.options.gallery, this.options.gallerySelector)\r\n      .forEach((galleryElement) => {\r\n        galleryElement.removeEventListener('click', this.onThumbnailsClick, false);\r\n      });\r\n  }\r\n}\r\n\r\nexport default PhotoSwipeLightbox;\r\n"], "names": ["createElement", "className", "tagName", "appendToEl", "el", "document", "append<PERSON><PERSON><PERSON>", "toTransformString", "x", "y", "scale", "propValue", "undefined", "setWidthHeight", "w", "h", "style", "width", "height", "LOAD_STATE", "IDLE", "LOADING", "LOADED", "ERROR", "specialKeyUsed", "e", "button", "ctrl<PERSON>ey", "metaKey", "altKey", "shift<PERSON>ey", "getElementsFromOption", "option", "legacySelector", "parent", "elements", "Element", "NodeList", "Array", "isArray", "from", "selector", "querySelectorAll", "isPswpClass", "fn", "prototype", "goTo", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "vendor", "match", "PhotoSwipeEvent", "constructor", "type", "details", "defaultPrevented", "Object", "assign", "preventDefault", "Eventable", "_listeners", "_filters", "pswp", "options", "addFilter", "name", "priority", "push", "sort", "f1", "f2", "removeFilter", "filter", "applyFilters", "args", "for<PERSON>ach", "apply", "on", "off", "listener", "dispatch", "event", "call", "Placeholder", "imageSrc", "container", "element", "imgEl", "decoding", "alt", "src", "setAttribute", "setDisplayedSize", "transform<PERSON><PERSON>in", "transform", "destroy", "parentNode", "remove", "Content", "itemData", "instance", "index", "data", "placeholder", "slide", "displayedImageWidth", "displayedImageHeight", "Number", "isAttached", "hasSlide", "isDecoding", "state", "content", "removePlaceholder", "keepPlaceholder", "setTimeout", "load", "isLazy", "reload", "usePlaceholder", "placeholderSrc", "msrc", "isFirstSlide", "placeholder<PERSON><PERSON>", "parentElement", "prepend", "isImageContent", "loadImage", "innerHTML", "html", "updateContentSize", "imageElement", "updateSrcsetSizes", "srcset", "complete", "onLoaded", "onload", "onerror", "onError", "setSlide", "isActive", "heavyAppended", "append", "displayError", "isError", "isLoading", "isInitialSizeUpdate", "isZoomable", "image", "sizesWidth", "dataset", "largestUsedSize", "parseInt", "sizes", "String", "lazyLoad", "errorMsgEl", "innerText", "errorMsg", "supportsDecode", "decode", "catch", "finally", "appendImage", "activate", "holderElement", "deactivate", "getViewportSize", "getViewportSizeFn", "newViewportSize", "documentElement", "clientWidth", "window", "innerHeight", "parsePaddingOption", "prop", "viewportSize", "paddingValue", "paddingFn", "padding", "legacyPropName", "toUpperCase", "slice", "getPanAreaSize", "MAX_IMAGE_WIDTH", "ZoomLevel", "panAreaSize", "elementSize", "fit", "fill", "vFill", "initial", "secondary", "max", "min", "update", "max<PERSON><PERSON><PERSON>", "maxHeight", "hRatio", "vRatio", "Math", "_getInitial", "_getSecondary", "_getMax", "zoomLevels", "slideData", "_parseZoomLevelOption", "optionPrefix", "optionName", "optionValue", "currZoomLevel", "lazyLoadData", "createContentFromData", "zoomLevel", "ceil", "lazyLoadSlide", "getItemData", "PhotoSwipeBase", "getNumItems", "numItems", "dataSource", "length", "items", "_getGalleryDOMElements", "gallery", "dataSourceItem", "_domElementToItemData", "galleryElement", "children", "childSelector", "linkEl", "querySelector", "pswpSrc", "href", "pswpSrcset", "pswpWidth", "pswpHeight", "pswpType", "thumbnailEl", "currentSrc", "getAttribute", "pswpCropped", "cropped", "thumbCropped", "PhotoSwipeLightbox", "_uid", "shouldOpen", "_preloadedContent", "onThumbnailsClick", "bind", "init", "gallerySelector", "addEventListener", "initialPoint", "clientX", "clientY", "clickedIndex", "getClickedIndex", "currentTarget", "loadAndOpen", "getClickedIndexFn", "<PERSON><PERSON><PERSON>get", "target", "childElements", "clickedChildIndex", "findIndex", "child", "contains", "galleryElements", "initialPointerPos", "preload", "promiseArray", "pswpModuleType", "pswpModule", "Promise", "resolve", "Error", "openPromise", "preloadFirstSlide", "uid", "all", "then", "iterableModules", "mainModule", "_openPhotoswipe", "module", "default", "keys", "contentLoader", "addToCache", "removeEventListener"], "mappings": ";;;;AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,aAAT,CAAuBC,SAAvB,EAAkCC,OAAlC,EAA2CC,UAA3C,EAAuD;AAC5D,EAAA,MAAMC,EAAE,GAAGC,QAAQ,CAACL,aAAT,CAAuBE,OAAvB,CAAX,CAAA;;AACA,EAAA,IAAID,SAAJ,EAAe;IACbG,EAAE,CAACH,SAAH,GAAeA,SAAf,CAAA;AACD,GAAA;;AACD,EAAA,IAAIE,UAAJ,EAAgB;IACdA,UAAU,CAACG,WAAX,CAAuBF,EAAvB,CAAA,CAAA;AACD,GAAA;;AACD,EAAA,OAAOA,EAAP,CAAA;AACD,CAAA;AA4DD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASG,iBAAT,CAA2BC,CAA3B,EAA8BC,CAA9B,EAAiCC,KAAjC,EAAwC;EAC7C,IAAIC,SAAS,GAAI,CAAcH,YAAAA,EAAAA,CAAE,MAAKC,CAAC,IAAI,CAAE,CAA7C,KAAA,CAAA,CAAA;;EAEA,IAAIC,KAAK,KAAKE,SAAd,EAAyB;AACvBD,IAAAA,SAAS,IAAK,CAAA,SAAA,EAAWD,KAAM,CAAA,CAAA,EAAGA,KAAM,CAAxC,GAAA,CAAA,CAAA;AACD,GAAA;;AAED,EAAA,OAAOC,SAAP,CAAA;AACD,CAAA;AAiCD;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASE,cAAT,CAAwBT,EAAxB,EAA4BU,CAA5B,EAA+BC,CAA/B,EAAkC;AACvCX,EAAAA,EAAE,CAACY,KAAH,CAASC,KAAT,GAAkB,OAAOH,CAAP,KAAa,QAAd,GAA2B,CAAA,EAAEA,CAAE,CAAA,EAAA,CAA/B,GAAqCA,CAAtD,CAAA;AACAV,EAAAA,EAAE,CAACY,KAAH,CAASE,MAAT,GAAmB,OAAOH,CAAP,KAAa,QAAd,GAA2B,CAAA,EAAEA,CAAE,CAAA,EAAA,CAA/B,GAAqCA,CAAvD,CAAA;AACD,CAAA;AA4BD;;AACA;;AACO,MAAMI,UAAU,GAAG;AACxBC,EAAAA,IAAI,EAAE,MADkB;AAExBC,EAAAA,OAAO,EAAE,SAFe;AAGxBC,EAAAA,MAAM,EAAE,QAHgB;AAIxBC,EAAAA,KAAK,EAAE,OAAA;AAJiB,CAAnB,CAAA;AAQP;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,cAAT,CAAwBC,CAAxB,EAA2B;EAChC,OAAQ,QAAA,IAAYA,CAAZ,IAAiBA,CAAC,CAACC,MAAF,KAAa,CAA/B,IAAqCD,CAAC,CAACE,OAAvC,IAAkDF,CAAC,CAACG,OAApD,IAA+DH,CAAC,CAACI,MAAjE,IAA2EJ,CAAC,CAACK,QAApF,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,qBAAT,CAA+BC,MAA/B,EAAuCC,cAAvC,EAAuDC,MAAM,GAAG7B,QAAhE,EAA0E;AAC/E;EACA,IAAI8B,QAAQ,GAAG,EAAf,CAAA;;EAEA,IAAIH,MAAM,YAAYI,OAAtB,EAA+B;IAC7BD,QAAQ,GAAG,CAACH,MAAD,CAAX,CAAA;AACD,GAFD,MAEO,IAAIA,MAAM,YAAYK,QAAlB,IAA8BC,KAAK,CAACC,OAAN,CAAcP,MAAd,CAAlC,EAAyD;AAC9DG,IAAAA,QAAQ,GAAGG,KAAK,CAACE,IAAN,CAAWR,MAAX,CAAX,CAAA;AACD,GAFM,MAEA;IACL,MAAMS,QAAQ,GAAG,OAAOT,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCC,cAAvD,CAAA;;AACA,IAAA,IAAIQ,QAAJ,EAAc;MACZN,QAAQ,GAAGG,KAAK,CAACE,IAAN,CAAWN,MAAM,CAACQ,gBAAP,CAAwBD,QAAxB,CAAX,CAAX,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,OAAON,QAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;;AACO,SAASQ,WAAT,CAAqBC,EAArB,EAAyB;AAC9B,EAAA,OAAO,OAAOA,EAAP,KAAc,UAAd,IACFA,EAAE,CAACC,SADD,IAEFD,EAAE,CAACC,SAAH,CAAaC,IAFlB,CAAA;AAGD,CAAA;AAED;AACA;AACA;AACA;AACA;;AACO,SAASC,QAAT,GAAoB;AACzB,EAAA,OAAO,CAAC,EAAEC,SAAS,CAACC,MAAV,IAAoBD,SAAS,CAACC,MAAV,CAAiBC,KAAjB,CAAuB,QAAvB,CAAtB,CAAR,CAAA;AACD;;ACvOD;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAN,CAAsB;AACpB;AACF;AACA;AACA;AACEC,EAAAA,WAAW,CAACC,IAAD,EAAOC,OAAP,EAAgB;IACzB,IAAKD,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKE,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;AACA,IAAA,IAAID,OAAJ,EAAa;AACXE,MAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBH,OAApB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDI,EAAAA,cAAc,GAAG;IACf,IAAKH,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;AACD,GAAA;;AAfmB,CAAA;AAkBtB;AACA;AACA;AACA;;;AACA,MAAMI,SAAN,CAAgB;AACdP,EAAAA,WAAW,GAAG;AACZ;AACJ;AACA;IACI,IAAKQ,CAAAA,UAAL,GAAkB,EAAlB,CAAA;AAEA;AACJ;AACA;;IACI,IAAKC,CAAAA,QAAL,GAAgB,EAAhB,CAAA;AAEA;;IACA,IAAKC,CAAAA,IAAL,GAAYlD,SAAZ,CAAA;AAEA;;IACA,IAAKmD,CAAAA,OAAL,GAAenD,SAAf,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACEoD,SAAS,CAACC,IAAD,EAAOrB,EAAP,EAAWsB,QAAQ,GAAG,GAAtB,EAA2B;AAAA,IAAA,IAAA,mBAAA,EAAA,oBAAA,EAAA,UAAA,CAAA;;AAClC,IAAA,IAAI,CAAC,IAAKL,CAAAA,QAAL,CAAcI,IAAd,CAAL,EAA0B;AACxB,MAAA,IAAA,CAAKJ,QAAL,CAAcI,IAAd,CAAA,GAAsB,EAAtB,CAAA;AACD,KAAA;;AAED,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAKJ,QAAL,CAAcI,IAAd,CAAA,MAAA,IAAA,IAAA,mBAAA,KAAA,KAAA,CAAA,IAAA,mBAAA,CAAqBE,IAArB,CAA0B;MAAEvB,EAAF;AAAMsB,MAAAA,QAAAA;KAAhC,CAAA,CAAA;AACA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAKL,QAAL,CAAcI,IAAd,CAAqBG,MAAAA,IAAAA,IAAAA,oBAAAA,KAAAA,KAAAA,CAAAA,IAAAA,oBAAAA,CAAAA,IAArB,CAA0B,CAACC,EAAD,EAAKC,EAAL,KAAYD,EAAE,CAACH,QAAH,GAAcI,EAAE,CAACJ,QAAvD,CAAA,CAAA;IAEA,CAAKJ,UAAAA,GAAAA,IAAAA,CAAAA,IAAL,kDAAWE,SAAX,CAAqBC,IAArB,EAA2BrB,EAA3B,EAA+BsB,QAA/B,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEK,EAAAA,YAAY,CAACN,IAAD,EAAOrB,EAAP,EAAW;AACrB,IAAA,IAAI,IAAKiB,CAAAA,QAAL,CAAcI,IAAd,CAAJ,EAAyB;AACvB;AACA,MAAA,IAAA,CAAKJ,QAAL,CAAcI,IAAd,IAAsB,IAAKJ,CAAAA,QAAL,CAAcI,IAAd,CAAA,CAAoBO,MAApB,CAA2BA,MAAM,IAAKA,MAAM,CAAC5B,EAAP,KAAcA,EAApD,CAAtB,CAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKkB,IAAT,EAAe;AACb,MAAA,IAAA,CAAKA,IAAL,CAAUS,YAAV,CAAuBN,IAAvB,EAA6BrB,EAA7B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACE6B,EAAAA,YAAY,CAACR,IAAD,EAAO,GAAGS,IAAV,EAAgB;AAAA,IAAA,IAAA,oBAAA,CAAA;;AAC1B,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAKb,QAAL,CAAcI,IAAd,uEAAqBU,OAArB,CAA8BH,MAAD,IAAY;AACvC;AACAE,MAAAA,IAAI,CAAC,CAAD,CAAJ,GAAUF,MAAM,CAAC5B,EAAP,CAAUgC,KAAV,CAAgB,IAAhB,EAAsBF,IAAtB,CAAV,CAAA;KAFF,CAAA,CAAA;IAIA,OAAOA,IAAI,CAAC,CAAD,CAAX,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEG,EAAAA,EAAE,CAACZ,IAAD,EAAOrB,EAAP,EAAW;AAAA,IAAA,IAAA,qBAAA,EAAA,WAAA,CAAA;;AACX,IAAA,IAAI,CAAC,IAAKgB,CAAAA,UAAL,CAAgBK,IAAhB,CAAL,EAA4B;AAC1B,MAAA,IAAA,CAAKL,UAAL,CAAgBK,IAAhB,CAAA,GAAwB,EAAxB,CAAA;AACD,KAAA;;IACD,CAAKL,qBAAAA,GAAAA,IAAAA,CAAAA,UAAL,CAAgBK,IAAhB,CAAA,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAAuBE,IAAvB,CAA4BvB,EAA5B,EAJW;AAOX;AACA;;AACA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAKkB,IAAL,MAAWe,IAAAA,IAAAA,WAAAA,KAAAA,KAAAA,CAAAA,IAAAA,WAAAA,CAAAA,EAAX,CAAcZ,IAAd,EAAoBrB,EAApB,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEkC,EAAAA,GAAG,CAACb,IAAD,EAAOrB,EAAP,EAAW;AAAA,IAAA,IAAA,WAAA,CAAA;;AACZ,IAAA,IAAI,IAAKgB,CAAAA,UAAL,CAAgBK,IAAhB,CAAJ,EAA2B;AACzB;AACA,MAAA,IAAA,CAAKL,UAAL,CAAgBK,IAAhB,CAAwB,GAAA,IAAA,CAAKL,UAAL,CAAgBK,IAAhB,CAAsBO,CAAAA,MAAtB,CAA6BO,QAAQ,IAAKnC,EAAE,KAAKmC,QAAjD,CAAxB,CAAA;AACD,KAAA;;AAED,IAAA,CAAA,WAAA,GAAA,IAAA,CAAKjB,IAAL,MAAWgB,IAAAA,IAAAA,WAAAA,KAAAA,KAAAA,CAAAA,IAAAA,WAAAA,CAAAA,GAAX,CAAeb,IAAf,EAAqBrB,EAArB,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEoC,EAAAA,QAAQ,CAACf,IAAD,EAAOX,OAAP,EAAgB;AAAA,IAAA,IAAA,sBAAA,CAAA;;IACtB,IAAI,IAAA,CAAKQ,IAAT,EAAe;MACb,OAAO,IAAA,CAAKA,IAAL,CAAUkB,QAAV,CAAmBf,IAAnB,EAAyBX,OAAzB,CAAP,CAAA;AACD,KAAA;;AAED,IAAA,MAAM2B,KAAK;AAAG;AAAkC,IAAA,IAAI9B,eAAJ,CAAoBc,IAApB,EAA0BX,OAA1B,CAAhD,CAAA;AAEA,IAAA,CAAA,sBAAA,GAAA,IAAA,CAAKM,UAAL,CAAgBK,IAAhB,2EAAuBU,OAAvB,CAAgCI,QAAD,IAAc;AAC3CA,MAAAA,QAAQ,CAACG,IAAT,CAAc,IAAd,EAAoBD,KAApB,CAAA,CAAA;KADF,CAAA,CAAA;AAIA,IAAA,OAAOA,KAAP,CAAA;AACD,GAAA;;AAnHa;;ACpOhB,MAAME,WAAN,CAAkB;AAChB;AACF;AACA;AACA;AACE/B,EAAAA,WAAW,CAACgC,QAAD,EAAWC,SAAX,EAAsB;AAC/B;AACA;;AACA;AACA,IAAA,IAAA,CAAKC,OAAL,GAAetF,aAAa,CAC1B,kCAD0B,EAE1BoF,QAAQ,GAAG,KAAH,GAAW,KAFO,EAG1BC,SAH0B,CAA5B,CAAA;;AAMA,IAAA,IAAID,QAAJ,EAAc;AACZ,MAAA,MAAMG,KAAK;AAAG;AAAiC,MAAA,IAAA,CAAKD,OAApD,CAAA;MACAC,KAAK,CAACC,QAAN,GAAiB,OAAjB,CAAA;MACAD,KAAK,CAACE,GAAN,GAAY,EAAZ,CAAA;MACAF,KAAK,CAACG,GAAN,GAAYN,QAAZ,CAAA;AACAG,MAAAA,KAAK,CAACI,YAAN,CAAmB,MAAnB,EAA2B,cAA3B,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKL,OAAL,CAAaK,YAAb,CAA0B,aAA1B,EAAyC,MAAzC,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEC,EAAAA,gBAAgB,CAAC3E,KAAD,EAAQC,MAAR,EAAgB;IAC9B,IAAI,CAAC,IAAKoE,CAAAA,OAAV,EAAmB;AACjB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKA,OAAL,CAAapF,OAAb,KAAyB,KAA7B,EAAoC;AAClC;AACA;AACA;AACAW,MAAAA,cAAc,CAAC,IAAKyE,CAAAA,OAAN,EAAe,GAAf,EAAoB,MAApB,CAAd,CAAA;AACA,MAAA,IAAA,CAAKA,OAAL,CAAatE,KAAb,CAAmB6E,eAAnB,GAAqC,KAArC,CAAA;AACA,MAAA,IAAA,CAAKP,OAAL,CAAatE,KAAb,CAAmB8E,SAAnB,GAA+BvF,iBAAiB,CAAC,CAAD,EAAI,CAAJ,EAAOU,KAAK,GAAG,GAAf,CAAhD,CAAA;AACD,KAPD,MAOO;AACLJ,MAAAA,cAAc,CAAC,IAAKyE,CAAAA,OAAN,EAAerE,KAAf,EAAsBC,MAAtB,CAAd,CAAA;AACD,KAAA;AACF,GAAA;;AAED6E,EAAAA,OAAO,GAAG;AAAA,IAAA,IAAA,aAAA,CAAA;;AACR,IAAA,IAAA,CAAA,aAAA,GAAI,IAAKT,CAAAA,OAAT,MAAI,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,IAAA,aAAA,CAAcU,UAAlB,EAA8B;MAC5B,IAAKV,CAAAA,OAAL,CAAaW,MAAb,EAAA,CAAA;AACD,KAAA;;IACD,IAAKX,CAAAA,OAAL,GAAe,IAAf,CAAA;AACD,GAAA;;AApDe;;ACClB;;AACA;;AACA;;AACA;;AAEA,MAAMY,OAAN,CAAc;AACZ;AACF;AACA;AACA;AACA;AACE9C,EAAAA,WAAW,CAAC+C,QAAD,EAAWC,QAAX,EAAqBC,KAArB,EAA4B;IACrC,IAAKD,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;IACA,IAAKE,CAAAA,IAAL,GAAYH,QAAZ,CAAA;IACA,IAAKE,CAAAA,KAAL,GAAaA,KAAb,CAAA;AAEA;;IACA,IAAKf,CAAAA,OAAL,GAAe1E,SAAf,CAAA;AACA;;IACA,IAAK2F,CAAAA,WAAL,GAAmB3F,SAAnB,CAAA;AACA;;IACA,IAAK4F,CAAAA,KAAL,GAAa5F,SAAb,CAAA;IAEA,IAAK6F,CAAAA,mBAAL,GAA2B,CAA3B,CAAA;IACA,IAAKC,CAAAA,oBAAL,GAA4B,CAA5B,CAAA;AAEA,IAAA,IAAA,CAAKzF,KAAL,GAAa0F,MAAM,CAAC,IAAKL,CAAAA,IAAL,CAAUxF,CAAX,CAAN,IAAuB6F,MAAM,CAAC,IAAKL,CAAAA,IAAL,CAAUrF,KAAX,CAA7B,IAAkD,CAA/D,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL,GAAcyF,MAAM,CAAC,IAAKL,CAAAA,IAAL,CAAUvF,CAAX,CAAN,IAAuB4F,MAAM,CAAC,IAAKL,CAAAA,IAAL,CAAUpF,MAAX,CAA7B,IAAmD,CAAjE,CAAA;IAEA,IAAK0F,CAAAA,UAAL,GAAkB,KAAlB,CAAA;IACA,IAAKC,CAAAA,QAAL,GAAgB,KAAhB,CAAA;IACA,IAAKC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACA;;AACA,IAAA,IAAA,CAAKC,KAAL,GAAa5F,UAAU,CAACC,IAAxB,CAAA;;AAEA,IAAA,IAAI,IAAKkF,CAAAA,IAAL,CAAUjD,IAAd,EAAoB;AAClB,MAAA,IAAA,CAAKA,IAAL,GAAY,IAAKiD,CAAAA,IAAL,CAAUjD,IAAtB,CAAA;AACD,KAFD,MAEO,IAAI,IAAA,CAAKiD,IAAL,CAAUZ,GAAd,EAAmB;MACxB,IAAKrC,CAAAA,IAAL,GAAY,OAAZ,CAAA;AACD,KAFM,MAEA;MACL,IAAKA,CAAAA,IAAL,GAAY,MAAZ,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK+C,QAAL,CAAcpB,QAAd,CAAuB,aAAvB,EAAsC;AAAEgC,MAAAA,OAAO,EAAE,IAAA;KAAjD,CAAA,CAAA;AACD,GAAA;;AAEDC,EAAAA,iBAAiB,GAAG;AAClB,IAAA,IAAI,KAAKV,WAAL,IAAoB,CAAC,IAAKW,CAAAA,eAAL,EAAzB,EAAiD;AAC/C;AACAC,MAAAA,UAAU,CAAC,MAAM;QACf,IAAI,IAAA,CAAKZ,WAAT,EAAsB;UACpB,IAAKA,CAAAA,WAAL,CAAiBR,OAAjB,EAAA,CAAA;UACA,IAAKQ,CAAAA,WAAL,GAAmB3F,SAAnB,CAAA;AACD,SAAA;OAJO,EAKP,IALO,CAAV,CAAA;AAMD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEwG,EAAAA,IAAI,CAACC,MAAD,EAASC,MAAT,EAAiB;AACnB,IAAA,IAAI,KAAKd,KAAL,IAAc,IAAKe,CAAAA,cAAL,EAAlB,EAAyC;MACvC,IAAI,CAAC,IAAKhB,CAAAA,WAAV,EAAuB;QACrB,MAAMiB,cAAc,GAAG,IAAKpB,CAAAA,QAAL,CAAc3B,YAAd,CACrB,gBADqB;AAGrB;AACC,QAAA,IAAA,CAAK6B,IAAL,CAAUmB,IAAV,IAAkB,IAAA,CAAKjB,KAAL,CAAWkB,YAA9B,GAA8C,IAAA,CAAKpB,IAAL,CAAUmB,IAAxD,GAA+D,KAJ1C,EAKrB,IALqB,CAAvB,CAAA;QAOA,IAAKlB,CAAAA,WAAL,GAAmB,IAAIpB,WAAJ,CACjBqC,cADiB,EAEjB,IAAKhB,CAAAA,KAAL,CAAWnB,SAFM,CAAnB,CAAA;AAID,OAZD,MAYO;AACL,QAAA,MAAMsC,aAAa,GAAG,IAAA,CAAKpB,WAAL,CAAiBjB,OAAvC,CADK;;AAGL,QAAA,IAAIqC,aAAa,IAAI,CAACA,aAAa,CAACC,aAApC,EAAmD;AACjD,UAAA,IAAA,CAAKpB,KAAL,CAAWnB,SAAX,CAAqBwC,OAArB,CAA6BF,aAA7B,CAAA,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;;AAED,IAAA,IAAI,IAAKrC,CAAAA,OAAL,IAAgB,CAACgC,MAArB,EAA6B;AAC3B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKlB,QAAL,CAAcpB,QAAd,CAAuB,aAAvB,EAAsC;AAAEgC,MAAAA,OAAO,EAAE,IAAX;AAAiBK,MAAAA,MAAAA;KAAvD,CAAA,CAAiE9D,gBAArE,EAAuF;AACrF,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKuE,cAAL,EAAJ,EAA2B;MACzB,IAAKxC,CAAAA,OAAL,GAAetF,aAAa,CAAC,WAAD,EAAc,KAAd,CAA5B,CADyB;AAGzB;;MACA,IAAI,IAAA,CAAKyG,mBAAT,EAA8B;QAC5B,IAAKsB,CAAAA,SAAL,CAAeV,MAAf,CAAA,CAAA;AACD,OAAA;AACF,KAPD,MAOO;AACL,MAAA,IAAA,CAAK/B,OAAL,GAAetF,aAAa,CAAC,eAAD,EAAkB,KAAlB,CAA5B,CAAA;MACA,IAAKsF,CAAAA,OAAL,CAAa0C,SAAb,GAAyB,KAAK1B,IAAL,CAAU2B,IAAV,IAAkB,EAA3C,CAAA;AACD,KAAA;;AAED,IAAA,IAAIX,MAAM,IAAI,IAAKd,CAAAA,KAAnB,EAA0B;AACxB,MAAA,IAAA,CAAKA,KAAL,CAAW0B,iBAAX,CAA6B,IAA7B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEH,SAAS,CAACV,MAAD,EAAS;AAAA,IAAA,IAAA,cAAA,EAAA,cAAA,CAAA;;AAChB,IAAA,IAAI,CAAC,IAAA,CAAKS,cAAL,EAAD,IACC,CAAC,IAAA,CAAKxC,OADP,IAEC,KAAKc,QAAL,CAAcpB,QAAd,CAAuB,kBAAvB,EAA2C;AAAEgC,MAAAA,OAAO,EAAE,IAAX;AAAiBK,MAAAA,MAAAA;KAA5D,CAAA,CAAsE9D,gBAF3E,EAE6F;AAC3F,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM4E,YAAY;AAAG;AAA+B,IAAA,IAAA,CAAK7C,OAAzD,CAAA;AAEA,IAAA,IAAA,CAAK8C,iBAAL,EAAA,CAAA;;AAEA,IAAA,IAAI,IAAK9B,CAAAA,IAAL,CAAU+B,MAAd,EAAsB;AACpBF,MAAAA,YAAY,CAACE,MAAb,GAAsB,IAAK/B,CAAAA,IAAL,CAAU+B,MAAhC,CAAA;AACD,KAAA;;IAEDF,YAAY,CAACzC,GAAb,GAAmB,CAAA,cAAA,GAAA,IAAA,CAAKY,IAAL,CAAUZ,GAA7B,2DAAoC,EAApC,CAAA;IACAyC,YAAY,CAAC1C,GAAb,GAAmB,CAAA,cAAA,GAAA,IAAA,CAAKa,IAAL,CAAUb,GAA7B,2DAAoC,EAApC,CAAA;AAEA,IAAA,IAAA,CAAKsB,KAAL,GAAa5F,UAAU,CAACE,OAAxB,CAAA;;IAEA,IAAI8G,YAAY,CAACG,QAAjB,EAA2B;AACzB,MAAA,IAAA,CAAKC,QAAL,EAAA,CAAA;AACD,KAFD,MAEO;MACLJ,YAAY,CAACK,MAAb,GAAsB,MAAM;AAC1B,QAAA,IAAA,CAAKD,QAAL,EAAA,CAAA;OADF,CAAA;;MAIAJ,YAAY,CAACM,OAAb,GAAuB,MAAM;AAC3B,QAAA,IAAA,CAAKC,OAAL,EAAA,CAAA;OADF,CAAA;AAGD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEC,QAAQ,CAACnC,KAAD,EAAQ;IACd,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;IACA,IAAKK,CAAAA,QAAL,GAAgB,IAAhB,CAAA;AACA,IAAA,IAAA,CAAKT,QAAL,GAAgBI,KAAK,CAAC1C,IAAtB,CAHc;AAMf,GAAA;AAED;AACF;AACA;;;AACEyE,EAAAA,QAAQ,GAAG;AACT,IAAA,IAAA,CAAKxB,KAAL,GAAa5F,UAAU,CAACG,MAAxB,CAAA;;AAEA,IAAA,IAAI,IAAKkF,CAAAA,KAAL,IAAc,IAAA,CAAKlB,OAAvB,EAAgC;AAC9B,MAAA,IAAA,CAAKc,QAAL,CAAcpB,QAAd,CAAuB,cAAvB,EAAuC;QAAEwB,KAAK,EAAE,KAAKA,KAAd;AAAqBQ,QAAAA,OAAO,EAAE,IAAA;AAA9B,OAAvC,EAD8B;;AAI9B,MAAA,IAAI,IAAKR,CAAAA,KAAL,CAAWoC,QAAX,IACG,IAAKpC,CAAAA,KAAL,CAAWqC,aADd,IAEG,CAAC,IAAA,CAAKvD,OAAL,CAAaU,UAFrB,EAEiC;AAC/B,QAAA,IAAA,CAAK8C,MAAL,EAAA,CAAA;AACA,QAAA,IAAA,CAAKtC,KAAL,CAAW0B,iBAAX,CAA6B,IAA7B,CAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAI,IAAKnB,CAAAA,KAAL,KAAe5F,UAAU,CAACG,MAA1B,IAAoC,IAAA,CAAKyF,KAAL,KAAe5F,UAAU,CAACI,KAAlE,EAAyE;AACvE,QAAA,IAAA,CAAK0F,iBAAL,EAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEyB,EAAAA,OAAO,GAAG;AACR,IAAA,IAAA,CAAK3B,KAAL,GAAa5F,UAAU,CAACI,KAAxB,CAAA;;IAEA,IAAI,IAAA,CAAKiF,KAAT,EAAgB;AACd,MAAA,IAAA,CAAKuC,YAAL,EAAA,CAAA;AACA,MAAA,IAAA,CAAK3C,QAAL,CAAcpB,QAAd,CAAuB,cAAvB,EAAuC;QAAEwB,KAAK,EAAE,KAAKA,KAAd;AAAqBwC,QAAAA,OAAO,EAAE,IAA9B;AAAoChC,QAAAA,OAAO,EAAE,IAAA;OAApF,CAAA,CAAA;AACA,MAAA,IAAA,CAAKZ,QAAL,CAAcpB,QAAd,CAAuB,WAAvB,EAAoC;QAAEwB,KAAK,EAAE,KAAKA,KAAd;AAAqBQ,QAAAA,OAAO,EAAE,IAAA;OAAlE,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEiC,EAAAA,SAAS,GAAG;AACV,IAAA,OAAO,IAAK7C,CAAAA,QAAL,CAAc3B,YAAd,CACL,kBADK,EAEL,IAAKsC,CAAAA,KAAL,KAAe5F,UAAU,CAACE,OAFrB,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACE2H,EAAAA,OAAO,GAAG;AACR,IAAA,OAAO,IAAKjC,CAAAA,KAAL,KAAe5F,UAAU,CAACI,KAAjC,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACEuG,EAAAA,cAAc,GAAG;IACf,OAAO,IAAA,CAAKzE,IAAL,KAAc,OAArB,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEuC,EAAAA,gBAAgB,CAAC3E,KAAD,EAAQC,MAAR,EAAgB;IAC9B,IAAI,CAAC,IAAKoE,CAAAA,OAAV,EAAmB;AACjB,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKiB,WAAT,EAAsB;AACpB,MAAA,IAAA,CAAKA,WAAL,CAAiBX,gBAAjB,CAAkC3E,KAAlC,EAAyCC,MAAzC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKkF,QAAL,CAAcpB,QAAd,CACF,eADE,EAEF;AAAEgC,MAAAA,OAAO,EAAE,IAAX;MAAiB/F,KAAjB;AAAwBC,MAAAA,MAAAA;KAFtB,CAAA,CAEgCqC,gBAFpC,EAGE;AACA,MAAA,OAAA;AACD,KAAA;;AAED1C,IAAAA,cAAc,CAAC,IAAKyE,CAAAA,OAAN,EAAerE,KAAf,EAAsBC,MAAtB,CAAd,CAAA;;AAEA,IAAA,IAAI,KAAK4G,cAAL,EAAA,IAAyB,CAAC,IAAKkB,CAAAA,OAAL,EAA9B,EAA8C;AAC5C,MAAA,MAAME,mBAAmB,GAAI,CAAC,IAAKzC,CAAAA,mBAAN,IAA6BxF,KAA1D,CAAA;MAEA,IAAKwF,CAAAA,mBAAL,GAA2BxF,KAA3B,CAAA;MACA,IAAKyF,CAAAA,oBAAL,GAA4BxF,MAA5B,CAAA;;AAEA,MAAA,IAAIgI,mBAAJ,EAAyB;QACvB,IAAKnB,CAAAA,SAAL,CAAe,KAAf,CAAA,CAAA;AACD,OAFD,MAEO;AACL,QAAA,IAAA,CAAKK,iBAAL,EAAA,CAAA;AACD,OAAA;;MAED,IAAI,IAAA,CAAK5B,KAAT,EAAgB;AACd,QAAA,IAAA,CAAKJ,QAAL,CAAcpB,QAAd,CACE,iBADF,EAEE;UAAEwB,KAAK,EAAE,KAAKA,KAAd;UAAqBvF,KAArB;UAA4BC,MAA5B;AAAoC8F,UAAAA,OAAO,EAAE,IAAA;SAF/C,CAAA,CAAA;AAID,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEmC,EAAAA,UAAU,GAAG;AACX,IAAA,OAAO,KAAK/C,QAAL,CAAc3B,YAAd,CACL,mBADK,EAEL,IAAKqD,CAAAA,cAAL,EAA0B,IAAA,IAAA,CAAKf,KAAL,KAAe5F,UAAU,CAACI,KAF/C,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACE6G,EAAAA,iBAAiB,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI,CAAC,IAAA,CAAKN,cAAL,EAAD,IAA0B,CAAC,IAAA,CAAKxC,OAAhC,IAA2C,CAAC,IAAA,CAAKgB,IAAL,CAAU+B,MAA1D,EAAkE;AAChE,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMe,KAAK;AAAG;AAA+B,IAAA,IAAA,CAAK9D,OAAlD,CAAA;AACA,IAAA,MAAM+D,UAAU,GAAG,IAAKjD,CAAAA,QAAL,CAAc3B,YAAd,CACjB,kBADiB,EAEjB,IAAA,CAAKgC,mBAFY,EAGjB,IAHiB,CAAnB,CAAA;;AAMA,IAAA,IACE,CAAC2C,KAAK,CAACE,OAAN,CAAcC,eAAf,IACGF,UAAU,GAAGG,QAAQ,CAACJ,KAAK,CAACE,OAAN,CAAcC,eAAf,EAAgC,EAAhC,CAF1B,EAGE;AACAH,MAAAA,KAAK,CAACK,KAAN,GAAcJ,UAAU,GAAG,IAA3B,CAAA;MACAD,KAAK,CAACE,OAAN,CAAcC,eAAd,GAAgCG,MAAM,CAACL,UAAD,CAAtC,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE9B,EAAAA,cAAc,GAAG;AACf,IAAA,OAAO,IAAKnB,CAAAA,QAAL,CAAc3B,YAAd,CACL,uBADK,EAEL,IAAA,CAAKqD,cAAL,EAFK,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACE6B,EAAAA,QAAQ,GAAG;AACT,IAAA,IAAI,KAAKvD,QAAL,CAAcpB,QAAd,CAAuB,iBAAvB,EAA0C;AAAEgC,MAAAA,OAAO,EAAE,IAAA;KAArD,CAAA,CAA6DzD,gBAAjE,EAAmF;AACjF,MAAA,OAAA;AACD,KAAA;;IAED,IAAK6D,CAAAA,IAAL,CAAU,IAAV,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACEF,EAAAA,eAAe,GAAG;AAChB,IAAA,OAAO,IAAKd,CAAAA,QAAL,CAAc3B,YAAd,CACL,sBADK,EAEL,IAAA,CAAKwE,SAAL,EAFK,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACElD,EAAAA,OAAO,GAAG;IACR,IAAKc,CAAAA,QAAL,GAAgB,KAAhB,CAAA;IACA,IAAKL,CAAAA,KAAL,GAAa5F,SAAb,CAAA;;AAEA,IAAA,IAAI,KAAKwF,QAAL,CAAcpB,QAAd,CAAuB,gBAAvB,EAAyC;AAAEgC,MAAAA,OAAO,EAAE,IAAA;KAApD,CAAA,CAA4DzD,gBAAhE,EAAkF;AAChF,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK0C,MAAL,EAAA,CAAA;;IAEA,IAAI,IAAA,CAAKM,WAAT,EAAsB;MACpB,IAAKA,CAAAA,WAAL,CAAiBR,OAAjB,EAAA,CAAA;MACA,IAAKQ,CAAAA,WAAL,GAAmB3F,SAAnB,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKkH,CAAAA,cAAL,EAAyB,IAAA,IAAA,CAAKxC,OAAlC,EAA2C;AACzC,MAAA,IAAA,CAAKA,OAAL,CAAakD,MAAb,GAAsB,IAAtB,CAAA;AACA,MAAA,IAAA,CAAKlD,OAAL,CAAamD,OAAb,GAAuB,IAAvB,CAAA;MACA,IAAKnD,CAAAA,OAAL,GAAe1E,SAAf,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEmI,EAAAA,YAAY,GAAG;IACb,IAAI,IAAA,CAAKvC,KAAT,EAAgB;AAAA,MAAA,IAAA,qBAAA,EAAA,sBAAA,CAAA;;AACd,MAAA,IAAIoD,UAAU,GAAG5J,aAAa,CAAC,iBAAD,EAAoB,KAApB,CAA9B,CAAA;MACA4J,UAAU,CAACC,SAAX,GAAA,CAAA,qBAAA,GAAA,CAAA,sBAAA,GAAuB,IAAKzD,CAAAA,QAAL,CAAcrC,OAArC,MAAuB,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAuB+F,QAA9C,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,qBAAA,GAA0D,EAA1D,CAAA;MACAF,UAAU;AAAG;MAA+B,IAAKxD,CAAAA,QAAL,CAAc3B,YAAd,CAC1C,qBAD0C,EAE1CmF,UAF0C,EAG1C,IAH0C,CAA5C,CAAA;AAKA,MAAA,IAAA,CAAKtE,OAAL,GAAetF,aAAa,CAAC,yCAAD,EAA4C,KAA5C,CAA5B,CAAA;AACA,MAAA,IAAA,CAAKsF,OAAL,CAAahF,WAAb,CAAyBsJ,UAAzB,CAAA,CAAA;AACA,MAAA,IAAA,CAAKpD,KAAL,CAAWnB,SAAX,CAAqBwE,SAArB,GAAiC,EAAjC,CAAA;AACA,MAAA,IAAA,CAAKrD,KAAL,CAAWnB,SAAX,CAAqB/E,WAArB,CAAiC,KAAKgF,OAAtC,CAAA,CAAA;AACA,MAAA,IAAA,CAAKkB,KAAL,CAAW0B,iBAAX,CAA6B,IAA7B,CAAA,CAAA;AACA,MAAA,IAAA,CAAKjB,iBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE6B,EAAAA,MAAM,GAAG;AACP,IAAA,IAAI,KAAKlC,UAAL,IAAmB,CAAC,IAAA,CAAKtB,OAA7B,EAAsC;AACpC,MAAA,OAAA;AACD,KAAA;;IAED,IAAKsB,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;AAEA,IAAA,IAAI,KAAKG,KAAL,KAAe5F,UAAU,CAACI,KAA9B,EAAqC;AACnC,MAAA,IAAA,CAAKwH,YAAL,EAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAK3C,QAAL,CAAcpB,QAAd,CAAuB,eAAvB,EAAwC;AAAEgC,MAAAA,OAAO,EAAE,IAAA;KAAnD,CAAA,CAA2DzD,gBAA/D,EAAiF;AAC/E,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMwG,cAAc,IAAI,QAAY,IAAA,IAAA,CAAKzE,OAArB,CAApB,CAAA;;IAEA,IAAI,IAAA,CAAKwC,cAAL,EAAJ,EAA2B;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,IAAIiC,cAAc,IAAI,IAAKvD,CAAAA,KAAvB,KAAiC,CAAC,IAAA,CAAKA,KAAL,CAAWoC,QAAZ,IAAwB7F,QAAQ,EAAjE,CAAJ,EAA0E;AACxE,QAAA,IAAA,CAAK+D,UAAL,GAAkB,IAAlB,CADwE;AAGxE;;AACA;;AACC,QAAA,IAAA,CAAKxB,OAAN,CAAe0E,MAAf,EAAA,CAAwBC,KAAxB,CAA8B,MAAM,EAApC,CAAA,CAAwCC,OAAxC,CAAgD,MAAM;UACpD,IAAKpD,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACA,UAAA,IAAA,CAAKqD,WAAL,EAAA,CAAA;SAFF,CAAA,CAAA;AAID,OATD,MASO;AACL,QAAA,IAAA,CAAKA,WAAL,EAAA,CAAA;AACD,OAAA;KAxBH,MAyBO,IAAI,IAAA,CAAK3D,KAAL,IAAc,CAAC,IAAKlB,CAAAA,OAAL,CAAaU,UAAhC,EAA4C;AACjD,MAAA,IAAA,CAAKQ,KAAL,CAAWnB,SAAX,CAAqB/E,WAArB,CAAiC,KAAKgF,OAAtC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACE8E,EAAAA,QAAQ,GAAG;AACT,IAAA,IAAI,KAAKhE,QAAL,CAAcpB,QAAd,CAAuB,iBAAvB,EAA0C;AAAEgC,MAAAA,OAAO,EAAE,IAAA;AAAX,KAA1C,EAA6DzD,gBAA7D,IACC,CAAC,IAAA,CAAKiD,KADX,EACkB;AAChB,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKsB,cAAL,EAAyB,IAAA,IAAA,CAAKhB,UAA9B,IAA4C,CAAC/D,QAAQ,EAAzD,EAA6D;AAC3D;AACA;AACA,MAAA,IAAA,CAAKoH,WAAL,EAAA,CAAA;AACD,KAJD,MAIO,IAAI,IAAKnB,CAAAA,OAAL,EAAJ,EAAoB;AACzB,MAAA,IAAA,CAAK5B,IAAL,CAAU,KAAV,EAAiB,IAAjB,EADyB;AAE1B,KAAA;;AAED,IAAA,IAAI,IAAKZ,CAAAA,KAAL,CAAW6D,aAAf,EAA8B;MAC5B,IAAK7D,CAAAA,KAAL,CAAW6D,aAAX,CAAyB1E,YAAzB,CAAsC,aAAtC,EAAqD,OAArD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE2E,EAAAA,UAAU,GAAG;AACX,IAAA,IAAA,CAAKlE,QAAL,CAAcpB,QAAd,CAAuB,mBAAvB,EAA4C;AAAEgC,MAAAA,OAAO,EAAE,IAAA;KAAvD,CAAA,CAAA;;AACA,IAAA,IAAI,KAAKR,KAAL,IAAc,KAAKA,KAAL,CAAW6D,aAA7B,EAA4C;MAC1C,IAAK7D,CAAAA,KAAL,CAAW6D,aAAX,CAAyB1E,YAAzB,CAAsC,aAAtC,EAAqD,MAArD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAGD;AACF;AACA;;;AACEM,EAAAA,MAAM,GAAG;IACP,IAAKW,CAAAA,UAAL,GAAkB,KAAlB,CAAA;;AAEA,IAAA,IAAI,KAAKR,QAAL,CAAcpB,QAAd,CAAuB,eAAvB,EAAwC;AAAEgC,MAAAA,OAAO,EAAE,IAAA;KAAnD,CAAA,CAA2DzD,gBAA/D,EAAiF;AAC/E,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAK+B,OAAL,IAAgB,KAAKA,OAAL,CAAaU,UAAjC,EAA6C;MAC3C,IAAKV,CAAAA,OAAL,CAAaW,MAAb,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKM,WAAL,IAAoB,KAAKA,WAAL,CAAiBjB,OAAzC,EAAkD;AAChD,MAAA,IAAA,CAAKiB,WAAL,CAAiBjB,OAAjB,CAAyBW,MAAzB,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEkE,EAAAA,WAAW,GAAG;IACZ,IAAI,CAAC,IAAKvD,CAAAA,UAAV,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKR,QAAL,CAAcpB,QAAd,CAAuB,oBAAvB,EAA6C;AAAEgC,MAAAA,OAAO,EAAE,IAAA;KAAxD,CAAA,CAAgEzD,gBAApE,EAAsF;AACpF,MAAA,OAAA;AACD,KAPW;;;IAUZ,IAAI,IAAA,CAAKiD,KAAL,IAAc,IAAKlB,CAAAA,OAAnB,IAA8B,CAAC,IAAKA,CAAAA,OAAL,CAAaU,UAAhD,EAA4D;AAC1D,MAAA,IAAA,CAAKQ,KAAL,CAAWnB,SAAX,CAAqB/E,WAArB,CAAiC,KAAKgF,OAAtC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKyB,CAAAA,KAAL,KAAe5F,UAAU,CAACG,MAA1B,IAAoC,IAAA,CAAKyF,KAAL,KAAe5F,UAAU,CAACI,KAAlE,EAAyE;AACvE,MAAA,IAAA,CAAK0F,iBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AA5fW;;ACRd;;AACA;;AACA;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASsD,eAAT,CAAyBxG,OAAzB,EAAkCD,IAAlC,EAAwC;EAC7C,IAAIC,OAAO,CAACyG,iBAAZ,EAA+B;IAC7B,MAAMC,eAAe,GAAG1G,OAAO,CAACyG,iBAAR,CAA0BzG,OAA1B,EAAmCD,IAAnC,CAAxB,CAAA;;AACA,IAAA,IAAI2G,eAAJ,EAAqB;AACnB,MAAA,OAAOA,eAAP,CAAA;AACD,KAAA;AACF,GAAA;;EAED,OAAO;AACLjK,IAAAA,CAAC,EAAEH,QAAQ,CAACqK,eAAT,CAAyBC,WADvB;AAGL;AACA;AACA;AACA;IACAlK,CAAC,EAAEmK,MAAM,CAACC,WAAAA;GAPZ,CAAA;AASD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,kBAAT,CAA4BC,IAA5B,EAAkChH,OAAlC,EAA2CiH,YAA3C,EAAyD7E,QAAzD,EAAmEE,KAAnE,EAA0E;EAC/E,IAAI4E,YAAY,GAAG,CAAnB,CAAA;;EAEA,IAAIlH,OAAO,CAACmH,SAAZ,EAAuB;AACrBD,IAAAA,YAAY,GAAGlH,OAAO,CAACmH,SAAR,CAAkBF,YAAlB,EAAgC7E,QAAhC,EAA0CE,KAA1C,CAAiD0E,CAAAA,IAAjD,CAAf,CAAA;AACD,GAFD,MAEO,IAAIhH,OAAO,CAACoH,OAAZ,EAAqB;AAC1BF,IAAAA,YAAY,GAAGlH,OAAO,CAACoH,OAAR,CAAgBJ,IAAhB,CAAf,CAAA;AACD,GAFM,MAEA;AACL,IAAA,MAAMK,cAAc,GAAG,SAAA,GAAYL,IAAI,CAAC,CAAD,CAAJ,CAAQM,WAAR,EAAZ,GAAoCN,IAAI,CAACO,KAAL,CAAW,CAAX,CAA3D,CADK;;AAGL,IAAA,IAAIvH,OAAO,CAACqH,cAAD,CAAX,EAA6B;AAC3B;AACAH,MAAAA,YAAY,GAAGlH,OAAO,CAACqH,cAAD,CAAtB,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,OAAOzE,MAAM,CAACsE,YAAD,CAAN,IAAwB,CAA/B,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASM,cAAT,CAAwBxH,OAAxB,EAAiCiH,YAAjC,EAA+C7E,QAA/C,EAAyDE,KAAzD,EAAgE;EACrE,OAAO;AACL7F,IAAAA,CAAC,EAAEwK,YAAY,CAACxK,CAAb,GACCsK,kBAAkB,CAAC,MAAD,EAAS/G,OAAT,EAAkBiH,YAAlB,EAAgC7E,QAAhC,EAA0CE,KAA1C,CADnB,GAECyE,kBAAkB,CAAC,OAAD,EAAU/G,OAAV,EAAmBiH,YAAnB,EAAiC7E,QAAjC,EAA2CE,KAA3C,CAHjB;AAIL5F,IAAAA,CAAC,EAAEuK,YAAY,CAACvK,CAAb,GACCqK,kBAAkB,CAAC,KAAD,EAAQ/G,OAAR,EAAiBiH,YAAjB,EAA+B7E,QAA/B,EAAyCE,KAAzC,CADnB,GAECyE,kBAAkB,CAAC,QAAD,EAAW/G,OAAX,EAAoBiH,YAApB,EAAkC7E,QAAlC,EAA4CE,KAA5C,CAAA;GANxB,CAAA;AAQD;;ACnGD,MAAMmF,eAAe,GAAG,IAAxB,CAAA;AAEA;;AACA;;AACA;;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AACA,MAAMC,SAAN,CAAgB;AACd;AACF;AACA;AACA;AACA;AACA;EACErI,WAAW,CAACW,OAAD,EAAUoC,QAAV,EAAoBE,KAApB,EAA2BvC,IAA3B,EAAiC;IAC1C,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAeA,OAAf,CAAA;IACA,IAAKoC,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;IACA,IAAKE,CAAAA,KAAL,GAAaA,KAAb,CAAA;AACA;;IACA,IAAKqF,CAAAA,WAAL,GAAmB,IAAnB,CAAA;AACA;;IACA,IAAKC,CAAAA,WAAL,GAAmB,IAAnB,CAAA;IACA,IAAKC,CAAAA,GAAL,GAAW,CAAX,CAAA;IACA,IAAKC,CAAAA,IAAL,GAAY,CAAZ,CAAA;IACA,IAAKC,CAAAA,KAAL,GAAa,CAAb,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAe,CAAf,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,CAAjB,CAAA;IACA,IAAKC,CAAAA,GAAL,GAAW,CAAX,CAAA;IACA,IAAKC,CAAAA,GAAL,GAAW,CAAX,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEC,EAAAA,MAAM,CAACC,QAAD,EAAWC,SAAX,EAAsBX,WAAtB,EAAmC;AACvC;AACA,IAAA,MAAMC,WAAW,GAAG;AAAEnL,MAAAA,CAAC,EAAE4L,QAAL;AAAe3L,MAAAA,CAAC,EAAE4L,SAAAA;KAAtC,CAAA;IACA,IAAKV,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;IACA,IAAKD,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;IAEA,MAAMY,MAAM,GAAGZ,WAAW,CAAClL,CAAZ,GAAgBmL,WAAW,CAACnL,CAA3C,CAAA;IACA,MAAM+L,MAAM,GAAGb,WAAW,CAACjL,CAAZ,GAAgBkL,WAAW,CAAClL,CAA3C,CAAA;AAEA,IAAA,IAAA,CAAKmL,GAAL,GAAWY,IAAI,CAACN,GAAL,CAAS,CAAT,EAAYI,MAAM,GAAGC,MAAT,GAAkBD,MAAlB,GAA2BC,MAAvC,CAAX,CAAA;AACA,IAAA,IAAA,CAAKV,IAAL,GAAYW,IAAI,CAACN,GAAL,CAAS,CAAT,EAAYI,MAAM,GAAGC,MAAT,GAAkBD,MAAlB,GAA2BC,MAAvC,CAAZ,CAVuC;AAavC;;IACA,IAAKT,CAAAA,KAAL,GAAaU,IAAI,CAACN,GAAL,CAAS,CAAT,EAAYK,MAAZ,CAAb,CAAA;AAEA,IAAA,IAAA,CAAKR,OAAL,GAAe,IAAKU,CAAAA,WAAL,EAAf,CAAA;AACA,IAAA,IAAA,CAAKT,SAAL,GAAiB,IAAKU,CAAAA,aAAL,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKT,GAAL,GAAWO,IAAI,CAACP,GAAL,CACT,IAAA,CAAKF,OADI,EAET,KAAKC,SAFI,EAGT,IAAKW,CAAAA,OAAL,EAHS,CAAX,CAAA;AAMA,IAAA,IAAA,CAAKT,GAAL,GAAWM,IAAI,CAACN,GAAL,CACT,IAAA,CAAKN,GADI,EAET,IAAKG,CAAAA,OAFI,EAGT,IAAA,CAAKC,SAHI,CAAX,CAAA;;IAMA,IAAI,IAAA,CAAKlI,IAAT,EAAe;AACb,MAAA,IAAA,CAAKA,IAAL,CAAUkB,QAAV,CAAmB,kBAAnB,EAAuC;AAAE4H,QAAAA,UAAU,EAAE,IAAd;AAAoBC,QAAAA,SAAS,EAAE,IAAK1G,CAAAA,QAAAA;OAA3E,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE2G,qBAAqB,CAACC,YAAD,EAAe;AAClC,IAAA,MAAMC,UAAU;AAAG;AACjBD,IAAAA,YAAY,GAAG,WADjB,CAAA;AAGA,IAAA,MAAME,WAAW,GAAG,IAAA,CAAKlJ,OAAL,CAAaiJ,UAAb,CAApB,CAAA;;IAEA,IAAI,CAACC,WAAL,EAAkB;AAChB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,OAAOA,WAAP,KAAuB,UAA3B,EAAuC;MACrC,OAAOA,WAAW,CAAC,IAAD,CAAlB,CAAA;AACD,KAAA;;IAED,IAAIA,WAAW,KAAK,MAApB,EAA4B;AAC1B,MAAA,OAAO,KAAKpB,IAAZ,CAAA;AACD,KAAA;;IAED,IAAIoB,WAAW,KAAK,KAApB,EAA2B;AACzB,MAAA,OAAO,KAAKrB,GAAZ,CAAA;AACD,KAAA;;IAED,OAAOjF,MAAM,CAACsG,WAAD,CAAb,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEP,EAAAA,aAAa,GAAG;AACd,IAAA,IAAIQ,aAAa,GAAG,IAAA,CAAKJ,qBAAL,CAA2B,WAA3B,CAApB,CAAA;;AAEA,IAAA,IAAII,aAAJ,EAAmB;AACjB,MAAA,OAAOA,aAAP,CAAA;AACD,KALa;;;IAQdA,aAAa,GAAGV,IAAI,CAACN,GAAL,CAAS,CAAT,EAAY,IAAKN,CAAAA,GAAL,GAAW,CAAvB,CAAhB,CAAA;;IAEA,IAAI,IAAA,CAAKD,WAAL,IAAoBuB,aAAa,GAAG,IAAKvB,CAAAA,WAAL,CAAiBnL,CAAjC,GAAqCgL,eAA7D,EAA8E;AAC5E0B,MAAAA,aAAa,GAAG1B,eAAe,GAAG,IAAKG,CAAAA,WAAL,CAAiBnL,CAAnD,CAAA;AACD,KAAA;;AAED,IAAA,OAAO0M,aAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACET,EAAAA,WAAW,GAAG;AACZ,IAAA,OAAO,KAAKK,qBAAL,CAA2B,SAA3B,CAAA,IAAyC,KAAKlB,GAArD,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEe,EAAAA,OAAO,GAAG;AACR;AACA;AACA,IAAA,OAAO,IAAKG,CAAAA,qBAAL,CAA2B,KAA3B,KAAqCN,IAAI,CAACP,GAAL,CAAS,CAAT,EAAY,IAAA,CAAKL,GAAL,GAAW,CAAvB,CAA5C,CAAA;AACD,GAAA;;AArJa;;ACFhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASuB,YAAT,CAAsBhH,QAAtB,EAAgCC,QAAhC,EAA0CC,KAA1C,EAAiD;EACtD,MAAMW,OAAO,GAAGZ,QAAQ,CAACgH,qBAAT,CAA+BjH,QAA/B,EAAyCE,KAAzC,CAAhB,CAAA;AACA;;AACA,EAAA,IAAIgH,SAAJ,CAAA;EAEA,MAAM;AAAEtJ,IAAAA,OAAAA;GAAYqC,GAAAA,QAApB,CALsD;AAQtD;;AACA,EAAA,IAAIrC,OAAJ,EAAa;IACXsJ,SAAS,GAAG,IAAI5B,SAAJ,CAAc1H,OAAd,EAAuBoC,QAAvB,EAAiC,CAAC,CAAlC,CAAZ,CAAA;AAEA,IAAA,IAAI6E,YAAJ,CAAA;;IACA,IAAI5E,QAAQ,CAACtC,IAAb,EAAmB;AACjBkH,MAAAA,YAAY,GAAG5E,QAAQ,CAACtC,IAAT,CAAckH,YAA7B,CAAA;AACD,KAFD,MAEO;AACLA,MAAAA,YAAY,GAAGT,eAAe,CAACxG,OAAD,EAAUqC,QAAV,CAA9B,CAAA;AACD,KAAA;;IAED,MAAMsF,WAAW,GAAGH,cAAc,CAACxH,OAAD,EAAUiH,YAAV,EAAwB7E,QAAxB,EAAkCE,KAAlC,CAAlC,CAAA;IACAgH,SAAS,CAAClB,MAAV,CAAiBnF,OAAO,CAAC/F,KAAzB,EAAgC+F,OAAO,CAAC9F,MAAxC,EAAgDwK,WAAhD,CAAA,CAAA;AACD,GAAA;;AAED1E,EAAAA,OAAO,CAAC2C,QAAR,EAAA,CAAA;;AAEA,EAAA,IAAI0D,SAAJ,EAAe;IACbrG,OAAO,CAACpB,gBAAR,CACE4G,IAAI,CAACc,IAAL,CAAUtG,OAAO,CAAC/F,KAAR,GAAgBoM,SAAS,CAACtB,OAApC,CADF,EAEES,IAAI,CAACc,IAAL,CAAUtG,OAAO,CAAC9F,MAAR,GAAiBmM,SAAS,CAACtB,OAArC,CAFF,CAAA,CAAA;AAID,GAAA;;AAED,EAAA,OAAO/E,OAAP,CAAA;AACD,CAAA;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASuG,aAAT,CAAuBlH,KAAvB,EAA8BD,QAA9B,EAAwC;AAC7C,EAAA,MAAMD,QAAQ,GAAGC,QAAQ,CAACoH,WAAT,CAAqBnH,KAArB,CAAjB,CAAA;;AAEA,EAAA,IAAID,QAAQ,CAACpB,QAAT,CAAkB,eAAlB,EAAmC;IAAEqB,KAAF;AAASF,IAAAA,QAAAA;GAA5C,CAAA,CAAwD5C,gBAA5D,EAA8E;AAC5E,IAAA,OAAA;AACD,GAAA;;AAED,EAAA,OAAO4J,YAAY,CAAChH,QAAD,EAAWC,QAAX,EAAqBC,KAArB,CAAnB,CAAA;AACD;;ACvED;;AACA;;AAEA;AACA;AACA;AACA;;AACA,MAAMoH,cAAN,SAA6B9J,SAA7B,CAAuC;AACrC;AACF;AACA;AACA;AACA;AACE+J,EAAAA,WAAW,GAAG;AAAA,IAAA,IAAA,aAAA,CAAA;;IACZ,IAAIC,QAAQ,GAAG,CAAf,CAAA;AACA,IAAA,MAAMC,UAAU,GAAG,CAAA,aAAA,GAAA,IAAA,CAAK7J,OAAR,MAAA,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,cAAc6J,UAAjC,CAAA;;AAEA,IAAA,IAAIA,UAAU,IAAI,QAAYA,IAAAA,UAA9B,EAA0C;AACxC;MACAD,QAAQ,GAAGC,UAAU,CAACC,MAAtB,CAAA;AACD,KAHD,MAGO,IAAID,UAAU,IAAI,SAAA,IAAaA,UAA/B,EAA2C;AAChD;AACA,MAAA,IAAI,CAACA,UAAU,CAACE,KAAhB,EAAuB;QACrBF,UAAU,CAACE,KAAX,GAAmB,IAAA,CAAKC,sBAAL,CAA4BH,UAAU,CAACI,OAAvC,CAAnB,CAAA;AACD,OAAA;;MAED,IAAIJ,UAAU,CAACE,KAAf,EAAsB;AACpBH,QAAAA,QAAQ,GAAGC,UAAU,CAACE,KAAX,CAAiBD,MAA5B,CAAA;AACD,OAAA;AACF,KAhBW;;;AAmBZ,IAAA,MAAM5I,KAAK,GAAG,IAAA,CAAKD,QAAL,CAAc,UAAd,EAA0B;MACtC4I,UADsC;AAEtCD,MAAAA,QAAAA;AAFsC,KAA1B,CAAd,CAAA;IAIA,OAAO,IAAA,CAAKlJ,YAAL,CAAkB,UAAlB,EAA8BQ,KAAK,CAAC0I,QAApC,EAA8CC,UAA9C,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACER,EAAAA,qBAAqB,CAACP,SAAD,EAAYxG,KAAZ,EAAmB;IACtC,OAAO,IAAIH,OAAJ,CAAY2G,SAAZ,EAAuB,IAAvB,EAA6BxG,KAA7B,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEmH,WAAW,CAACnH,KAAD,EAAQ;AAAA,IAAA,IAAA,cAAA,CAAA;;AACjB,IAAA,MAAMuH,UAAU,GAAG,CAAA,cAAA,GAAA,IAAA,CAAK7J,OAAR,MAAA,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,eAAc6J,UAAjC,CAAA;AACA;;IACA,IAAIK,cAAc,GAAG,EAArB,CAAA;;AACA,IAAA,IAAI3L,KAAK,CAACC,OAAN,CAAcqL,UAAd,CAAJ,EAA+B;AAC7B;AACAK,MAAAA,cAAc,GAAGL,UAAU,CAACvH,KAAD,CAA3B,CAAA;AACD,KAHD,MAGO,IAAIuH,UAAU,IAAI,SAAA,IAAaA,UAA/B,EAA2C;AAChD;AACA;AACA;AAEA;AACA,MAAA,IAAI,CAACA,UAAU,CAACE,KAAhB,EAAuB;QACrBF,UAAU,CAACE,KAAX,GAAmB,IAAA,CAAKC,sBAAL,CAA4BH,UAAU,CAACI,OAAvC,CAAnB,CAAA;AACD,OAAA;;AAEDC,MAAAA,cAAc,GAAGL,UAAU,CAACE,KAAX,CAAiBzH,KAAjB,CAAjB,CAAA;AACD,KAAA;;IAED,IAAIF,QAAQ,GAAG8H,cAAf,CAAA;;IAEA,IAAI9H,QAAQ,YAAY/D,OAAxB,EAAiC;AAC/B+D,MAAAA,QAAQ,GAAG,IAAA,CAAK+H,qBAAL,CAA2B/H,QAA3B,CAAX,CAAA;AACD,KAxBgB;AA2BjB;;;AACA,IAAA,MAAMlB,KAAK,GAAG,IAAA,CAAKD,QAAL,CAAc,UAAd,EAA0B;MACtCmB,QAAQ,EAAEA,QAAQ,IAAI,EADgB;AAEtCE,MAAAA,KAAAA;AAFsC,KAA1B,CAAd,CAAA;IAKA,OAAO,IAAA,CAAK5B,YAAL,CAAkB,UAAlB,EAA8BQ,KAAK,CAACkB,QAApC,EAA8CE,KAA9C,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE0H,sBAAsB,CAACI,cAAD,EAAiB;AAAA,IAAA,IAAA,cAAA,EAAA,cAAA,CAAA;;IACrC,IAAI,CAAA,cAAA,GAAA,IAAA,CAAKpK,OAAL,MAAA,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,IAAA,cAAA,CAAcqK,QAAd,IAAA,CAAA,cAAA,GAA0B,KAAKrK,OAA/B,MAAA,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,IAA0B,cAAcsK,CAAAA,aAA5C,EAA2D;AACzD,MAAA,OAAOtM,qBAAqB,CAC1B,IAAKgC,CAAAA,OAAL,CAAaqK,QADa,EAE1B,IAAKrK,CAAAA,OAAL,CAAasK,aAFa,EAG1BF,cAH0B,CAArB,IAIF,EAJL,CAAA;AAKD,KAAA;;IAED,OAAO,CAACA,cAAD,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACED,qBAAqB,CAAC5I,OAAD,EAAU;AAC7B;AACA,IAAA,MAAMa,QAAQ,GAAG;AACfb,MAAAA,OAAAA;KADF,CAAA;AAIA,IAAA,MAAMgJ,MAAM;AAAG;AACbhJ,IAAAA,OAAO,CAACpF,OAAR,KAAoB,GAApB,GACIoF,OADJ,GAEIA,OAAO,CAACiJ,aAAR,CAAsB,GAAtB,CAHN,CAAA;;AAMA,IAAA,IAAID,MAAJ,EAAY;AACV;AACA;MACAnI,QAAQ,CAACT,GAAT,GAAe4I,MAAM,CAAChF,OAAP,CAAekF,OAAf,IAA0BF,MAAM,CAACG,IAAhD,CAAA;;AAEA,MAAA,IAAIH,MAAM,CAAChF,OAAP,CAAeoF,UAAnB,EAA+B;AAC7BvI,QAAAA,QAAQ,CAACkC,MAAT,GAAkBiG,MAAM,CAAChF,OAAP,CAAeoF,UAAjC,CAAA;AACD,OAAA;;MAEDvI,QAAQ,CAAClF,KAAT,GAAiBqN,MAAM,CAAChF,OAAP,CAAeqF,SAAf,GAA2BnF,QAAQ,CAAC8E,MAAM,CAAChF,OAAP,CAAeqF,SAAhB,EAA2B,EAA3B,CAAnC,GAAoE,CAArF,CAAA;MACAxI,QAAQ,CAACjF,MAAT,GAAkBoN,MAAM,CAAChF,OAAP,CAAesF,UAAf,GAA4BpF,QAAQ,CAAC8E,MAAM,CAAChF,OAAP,CAAesF,UAAhB,EAA4B,EAA5B,CAApC,GAAsE,CAAxF,CAVU;;AAaVzI,MAAAA,QAAQ,CAACrF,CAAT,GAAaqF,QAAQ,CAAClF,KAAtB,CAAA;AACAkF,MAAAA,QAAQ,CAACpF,CAAT,GAAaoF,QAAQ,CAACjF,MAAtB,CAAA;;AAEA,MAAA,IAAIoN,MAAM,CAAChF,OAAP,CAAeuF,QAAnB,EAA6B;AAC3B1I,QAAAA,QAAQ,CAAC9C,IAAT,GAAgBiL,MAAM,CAAChF,OAAP,CAAeuF,QAA/B,CAAA;AACD,OAAA;;AAED,MAAA,MAAMC,WAAW,GAAGxJ,OAAO,CAACiJ,aAAR,CAAsB,KAAtB,CAApB,CAAA;;AAEA,MAAA,IAAIO,WAAJ,EAAiB;AAAA,QAAA,IAAA,qBAAA,CAAA;;AACf;AACA;QACA3I,QAAQ,CAACsB,IAAT,GAAgBqH,WAAW,CAACC,UAAZ,IAA0BD,WAAW,CAACpJ,GAAtD,CAAA;QACAS,QAAQ,CAACV,GAAT,GAAA,CAAA,qBAAA,GAAeqJ,WAAW,CAACE,YAAZ,CAAyB,KAAzB,CAAf,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,qBAAA,GAAkD,EAAlD,CAAA;AACD,OAAA;;MAED,IAAIV,MAAM,CAAChF,OAAP,CAAe2F,WAAf,IAA8BX,MAAM,CAAChF,OAAP,CAAe4F,OAAjD,EAA0D;QACxD/I,QAAQ,CAACgJ,YAAT,GAAwB,IAAxB,CAAA;AACD,OAAA;AACF,KAAA;;IAED,OAAO,IAAA,CAAK1K,YAAL,CAAkB,aAAlB,EAAiC0B,QAAjC,EAA2Cb,OAA3C,EAAoDgJ,MAApD,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACEnB,EAAAA,YAAY,CAAChH,QAAD,EAAWE,KAAX,EAAkB;AAC5B,IAAA,OAAO8G,YAAY,CAAChH,QAAD,EAAW,IAAX,EAAiBE,KAAjB,CAAnB,CAAA;AACD,GAAA;;AA1KoC;;ACHvC;AACA;AACA;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM+I,kBAAN,SAAiC3B,cAAjC,CAAgD;AAC9C;AACF;AACA;EACErK,WAAW,CAACW,OAAD,EAAU;AACnB,IAAA,KAAA,EAAA,CAAA;AACA;;AACA,IAAA,IAAA,CAAKA,OAAL,GAAeA,OAAO,IAAI,EAA1B,CAAA;IACA,IAAKsL,CAAAA,IAAL,GAAY,CAAZ,CAAA;IACA,IAAKC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACA;AACJ;AACA;AACA;;IACI,IAAKC,CAAAA,iBAAL,GAAyB3O,SAAzB,CAAA;IAEA,IAAK4O,CAAAA,iBAAL,GAAyB,IAAKA,CAAAA,iBAAL,CAAuBC,IAAvB,CAA4B,IAA5B,CAAzB,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEC,EAAAA,IAAI,GAAG;AACL;AACA3N,IAAAA,qBAAqB,CAAC,IAAA,CAAKgC,OAAL,CAAaiK,OAAd,EAAuB,IAAA,CAAKjK,OAAL,CAAa4L,eAApC,CAArB,CACGhL,OADH,CACYwJ,cAAD,IAAoB;MAC3BA,cAAc,CAACyB,gBAAf,CAAgC,OAAhC,EAAyC,IAAKJ,CAAAA,iBAA9C,EAAiE,KAAjE,CAAA,CAAA;KAFJ,CAAA,CAAA;AAID,GAAA;AAED;AACF;AACA;;;EACEA,iBAAiB,CAAC/N,CAAD,EAAI;AACnB;AACA,IAAA,IAAID,cAAc,CAACC,CAAD,CAAd;OACGmJ,MAAM,CAAC9G,IADd,EACoB;AAAE;AACpB,MAAA,OAAA;AACD,KALkB;AAQnB;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAA,IAAI+L,YAAY,GAAG;MAAErP,CAAC,EAAEiB,CAAC,CAACqO,OAAP;MAAgBrP,CAAC,EAAEgB,CAAC,CAACsO,OAAAA;KAAxC,CAAA;;IAEA,IAAI,CAACF,YAAY,CAACrP,CAAd,IAAmB,CAACqP,YAAY,CAACpP,CAArC,EAAwC;AACtCoP,MAAAA,YAAY,GAAG,IAAf,CAAA;AACD,KAAA;;AAED,IAAA,IAAIG,YAAY,GAAG,IAAA,CAAKC,eAAL,CAAqBxO,CAArB,CAAnB,CAAA;IACAuO,YAAY,GAAG,IAAKvL,CAAAA,YAAL,CAAkB,cAAlB,EAAkCuL,YAAlC,EAAgDvO,CAAhD,EAAmD,IAAnD,CAAf,CAAA;AACA;;AACA,IAAA,MAAMmM,UAAU,GAAG;MACjBI,OAAO;AAAE;AAA4BvM,MAAAA,CAAC,CAACyO,aAAAA;KADzC,CAAA;;IAIA,IAAIF,YAAY,IAAI,CAApB,EAAuB;AACrBvO,MAAAA,CAAC,CAACiC,cAAF,EAAA,CAAA;AACA,MAAA,IAAA,CAAKyM,WAAL,CAAiBH,YAAjB,EAA+BpC,UAA/B,EAA2CiC,YAA3C,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACEI,eAAe,CAACxO,CAAD,EAAI;AACjB;AACA,IAAA,IAAI,IAAKsC,CAAAA,OAAL,CAAaqM,iBAAjB,EAAoC;MAClC,OAAO,IAAA,CAAKrM,OAAL,CAAaqM,iBAAb,CAA+BlL,IAA/B,CAAoC,IAApC,EAA0CzD,CAA1C,CAAP,CAAA;AACD,KAAA;;AAED,IAAA,MAAM4O,aAAa;AAAG;AAA4B5O,IAAAA,CAAC,CAAC6O,MAApD,CAAA;AACA,IAAA,MAAMC,aAAa,GAAGxO,qBAAqB,CACzC,IAAKgC,CAAAA,OAAL,CAAaqK,QAD4B,EAEzC,IAAA,CAAKrK,OAAL,CAAasK,aAF4B;AAGzC;IAA4B5M,CAAC,CAACyO,aAHW,CAA3C,CAAA;AAKA,IAAA,MAAMM,iBAAiB,GAAGD,aAAa,CAACE,SAAd,CACxBC,KAAK,IAAIA,KAAK,KAAKL,aAAV,IAA2BK,KAAK,CAACC,QAAN,CAAeN,aAAf,CADZ,CAA1B,CAAA;;AAIA,IAAA,IAAIG,iBAAiB,KAAK,CAAC,CAA3B,EAA8B;AAC5B,MAAA,OAAOA,iBAAP,CAAA;KADF,MAEO,IAAI,IAAA,CAAKzM,OAAL,CAAaqK,QAAb,IAAyB,IAAKrK,CAAAA,OAAL,CAAasK,aAA1C,EAAyD;AAC9D;AACA,MAAA,OAAO,CAAC,CAAR,CAAA;AACD,KArBgB;;;AAwBjB,IAAA,OAAO,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACE8B,EAAAA,WAAW,CAAC9J,KAAD,EAAQuH,UAAR,EAAoBiC,YAApB,EAAkC;AAC3C;AACA,IAAA,IAAIjF,MAAM,CAAC9G,IAAP,IAAe,CAAC,IAAA,CAAKC,OAAzB,EAAkC;AAChC,MAAA,OAAO,KAAP,CAAA;AACD,KAJ0C;;;AAO3C,IAAA,IAAI,CAAC6J,UAAD,IAAe,IAAA,CAAK7J,OAAL,CAAaiK,OAA5B,IAAuC,IAAKjK,CAAAA,OAAL,CAAaqK,QAAxD,EAAkE;MAChE,MAAMwC,eAAe,GAAG7O,qBAAqB,CAAC,KAAKgC,OAAL,CAAaiK,OAAd,CAA7C,CAAA;;AACA,MAAA,IAAI4C,eAAe,CAAC,CAAD,CAAnB,EAAwB;AACtBhD,QAAAA,UAAU,GAAG;UACXI,OAAO,EAAE4C,eAAe,CAAC,CAAD,CAAA;SAD1B,CAAA;AAGD,OAAA;AACF,KAd0C;;;AAiB3C,IAAA,IAAA,CAAK7M,OAAL,CAAasC,KAAb,GAAqBA,KAArB,CAjB2C;;AAoB3C,IAAA,IAAA,CAAKtC,OAAL,CAAa8M,iBAAb,GAAiChB,YAAjC,CAAA;IAEA,IAAKP,CAAAA,UAAL,GAAkB,IAAlB,CAAA;AACA,IAAA,IAAA,CAAKwB,OAAL,CAAazK,KAAb,EAAoBuH,UAApB,CAAA,CAAA;AACA,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEkD,EAAAA,OAAO,CAACzK,KAAD,EAAQuH,UAAR,EAAoB;IACzB,MAAM;AAAE7J,MAAAA,OAAAA;AAAF,KAAA,GAAc,IAApB,CAAA;;AAEA,IAAA,IAAI6J,UAAJ,EAAgB;MACd7J,OAAO,CAAC6J,UAAR,GAAqBA,UAArB,CAAA;AACD,KALwB;;AAQzB;;;IACA,MAAMmD,YAAY,GAAG,EAArB,CAAA;AAEA,IAAA,MAAMC,cAAc,GAAG,OAAOjN,OAAO,CAACkN,UAAtC,CAAA;;AACA,IAAA,IAAItO,WAAW,CAACoB,OAAO,CAACkN,UAAT,CAAf,EAAqC;AACnCF,MAAAA,YAAY,CAAC5M,IAAb,CAAkB+M,OAAO,CAACC,OAAR;AAAgB;MAAiCpN,OAAO,CAACkN,UAAzD,CAAlB,CAAA,CAAA;AACD,KAFD,MAEO,IAAID,cAAc,KAAK,QAAvB,EAAiC;AACtC,MAAA,MAAM,IAAII,KAAJ,CAAU,6CAAV,CAAN,CAAA;AACD,KAFM,MAEA,IAAIJ,cAAc,KAAK,UAAvB,EAAmC;AACxCD,MAAAA,YAAY,CAAC5M,IAAb;AAAkB;MAAgDJ,OAAO,CAACkN,UAAT,EAAjE,CAAA,CAAA;AACD,KAFM,MAEA;AACL,MAAA,MAAM,IAAIG,KAAJ,CAAU,yBAAV,CAAN,CAAA;AACD,KApBwB;;;AAuBzB,IAAA,IAAI,OAAOrN,OAAO,CAACsN,WAAf,KAA+B,UAAnC,EAA+C;AAC7C;AACAN,MAAAA,YAAY,CAAC5M,IAAb,CAAkBJ,OAAO,CAACsN,WAAR,EAAlB,CAAA,CAAA;AACD,KAAA;;IAED,IAAItN,OAAO,CAACuN,iBAAR,KAA8B,KAA9B,IAAuCjL,KAAK,IAAI,CAApD,EAAuD;AACrD,MAAA,IAAA,CAAKkJ,iBAAL,GAAyBhC,aAAa,CAAClH,KAAD,EAAQ,IAAR,CAAtC,CAAA;AACD,KA9BwB;;;AAiCzB,IAAA,MAAMkL,GAAG,GAAG,EAAE,IAAA,CAAKlC,IAAnB,CAAA;IACA6B,OAAO,CAACM,GAAR,CAAYT,YAAZ,EAA0BU,IAA1B,CAAgCC,eAAD,IAAqB;MAClD,IAAI,IAAA,CAAKpC,UAAT,EAAqB;AACnB,QAAA,MAAMqC,UAAU,GAAGD,eAAe,CAAC,CAAD,CAAlC,CAAA;;AACA,QAAA,IAAA,CAAKE,eAAL,CAAqBD,UAArB,EAAiCJ,GAAjC,CAAA,CAAA;AACD,OAAA;KAJH,CAAA,CAAA;AAMD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEK,EAAAA,eAAe,CAACC,MAAD,EAASN,GAAT,EAAc;AAC3B;AACA;AACA;AACA;AACA;AACA,IAAA,IAAIA,GAAG,KAAK,IAAA,CAAKlC,IAAb,IAAqB,IAAA,CAAKC,UAA9B,EAA0C;AACxC,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKA,UAAL,GAAkB,KAAlB,CAV2B;;IAa3B,IAAI1E,MAAM,CAAC9G,IAAX,EAAiB;AACf,MAAA,OAAA;AACD,KAAA;AAED;AACJ;AACA;AACA;AACA;;;AACI,IAAA,MAAMA,IAAI,GAAG,OAAO+N,MAAP,KAAkB,QAAlB,GACP,IAAIA,MAAM,CAACC,OAAX,CAAmB,IAAK/N,CAAAA,OAAxB,CADO;AAAA,MAEP,IAAI8N,MAAJ,CAAW,KAAK9N,OAAhB,CAFN,CAtB2B;;IA0B3B,IAAKD,CAAAA,IAAL,GAAYA,IAAZ,CAAA;AACA8G,IAAAA,MAAM,CAAC9G,IAAP,GAAcA,IAAd,CA3B2B;;AA8B3B;;IACCN,MAAM,CAACuO,IAAP,CAAY,IAAKnO,CAAAA,UAAjB,CAAD,CAA+Be,OAA/B,CAAwCV,IAAD,IAAU;AAAA,MAAA,IAAA,qBAAA,CAAA;;AAC/C,MAAA,CAAA,qBAAA,GAAA,IAAA,CAAKL,UAAL,CAAgBK,IAAhB,yEAAuBU,OAAvB,CAAgC/B,EAAD,IAAQ;QACrCkB,IAAI,CAACe,EAAL,CAAQZ,IAAR;AAAc;QAA0CrB,EAAxD,CAAA,CAAA;OADF,CAAA,CAAA;AAGD,KAJD,EA/B2B;;AAsC3B;;IACCY,MAAM,CAACuO,IAAP,CAAY,IAAKlO,CAAAA,QAAjB,CAAD,CAA6Bc,OAA7B,CAAsCV,IAAD,IAAU;AAAA,MAAA,IAAA,mBAAA,CAAA;;AAC7C,MAAA,CAAA,mBAAA,GAAA,IAAA,CAAKJ,QAAL,CAAcI,IAAd,qEAAqBU,OAArB,CAA8BH,MAAD,IAAY;QACvCV,IAAI,CAACE,SAAL,CAAeC,IAAf,EAAqBO,MAAM,CAAC5B,EAA5B,EAAgC4B,MAAM,CAACN,QAAvC,CAAA,CAAA;OADF,CAAA,CAAA;KADF,CAAA,CAAA;;IAMA,IAAI,IAAA,CAAKqL,iBAAT,EAA4B;AAC1BzL,MAAAA,IAAI,CAACkO,aAAL,CAAmBC,UAAnB,CAA8B,KAAK1C,iBAAnC,CAAA,CAAA;MACA,IAAKA,CAAAA,iBAAL,GAAyB3O,SAAzB,CAAA;AACD,KAAA;;AAEDkD,IAAAA,IAAI,CAACe,EAAL,CAAQ,SAAR,EAAmB,MAAM;AACvB;MACA,IAAKf,CAAAA,IAAL,GAAYlD,SAAZ,CAAA;MACA,OAAOgK,MAAM,CAAC9G,IAAd,CAAA;KAHF,CAAA,CAAA;AAMAA,IAAAA,IAAI,CAAC4L,IAAL,EAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACE3J,EAAAA,OAAO,GAAG;AAAA,IAAA,IAAA,UAAA,CAAA;;IACR,CAAKjC,UAAAA,GAAAA,IAAAA,CAAAA,IAAL,kDAAWiC,OAAX,EAAA,CAAA;IAEA,IAAKuJ,CAAAA,UAAL,GAAkB,KAAlB,CAAA;IACA,IAAK1L,CAAAA,UAAL,GAAkB,EAAlB,CAAA;AAEA7B,IAAAA,qBAAqB,CAAC,IAAA,CAAKgC,OAAL,CAAaiK,OAAd,EAAuB,IAAA,CAAKjK,OAAL,CAAa4L,eAApC,CAArB,CACGhL,OADH,CACYwJ,cAAD,IAAoB;MAC3BA,cAAc,CAAC+D,mBAAf,CAAmC,OAAnC,EAA4C,IAAK1C,CAAAA,iBAAjD,EAAoE,KAApE,CAAA,CAAA;KAFJ,CAAA,CAAA;AAID,GAAA;;AArQ6C;;;;"}