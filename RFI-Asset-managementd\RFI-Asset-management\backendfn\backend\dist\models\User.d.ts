import { Model, Optional } from 'sequelize';
interface UserAttributes {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    mobile: string;
    userGroup: string;
    loginEnabled: boolean;
    loginId: string;
    accessLevel: 'county' | 'state' | 'precinct';
    county?: string;
    precinct?: string;
    status: boolean;
    image?: string;
    company?: string;
    employeeNo?: string;
    manager?: string;
    department?: string;
    location?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    state?: string;
    pincode?: string;
    country?: string;
    role: 'admin' | 'manager' | 'user' | 'Portal Admin';
    username?: string;
    createdAt: Date;
    updatedAt: Date;
}
interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {
}
declare class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    mobile: string;
    userGroup: string;
    loginEnabled: boolean;
    loginId: string;
    accessLevel: 'county' | 'state' | 'precinct';
    county?: string;
    precinct?: string;
    status: boolean;
    image?: string;
    company?: string;
    employeeNo?: string;
    manager?: string;
    department?: string;
    location?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    state?: string;
    pincode?: string;
    country?: string;
    role: 'admin' | 'manager' | 'user' | 'Portal Admin';
    username?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    comparePassword(candidatePassword: string): Promise<boolean>;
    generateToken(): string;
    getFullName(): string;
    isAdmin(): boolean;
    hasStateAccess(): boolean;
    hasCountyAccess(): boolean;
    hasPrecinctAccess(): boolean;
}
export default User;
