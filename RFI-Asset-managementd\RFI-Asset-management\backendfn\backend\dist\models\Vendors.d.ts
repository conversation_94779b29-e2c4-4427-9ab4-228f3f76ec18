import { Model, Optional } from 'sequelize';
export interface VendorAttributes {
    id: number;
    companyName: string;
    title?: string;
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    status: boolean;
    secondaryContacts?: string;
    addresses?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface VendorCreationAttributes extends Optional<VendorAttributes, 'id' | 'title' | 'secondaryContacts' | 'addresses' | 'createdAt' | 'updatedAt'> {
}
declare class Vendor extends Model<VendorAttributes, VendorCreationAttributes> implements VendorAttributes {
    id: number;
    companyName: string;
    title?: string;
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    status: boolean;
    secondaryContacts?: string;
    addresses?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    getSecondaryContactsData(): any[];
    setSecondaryContactsData(contacts: any[]): void;
    getAddressesData(): any[];
    setAddressesData(addresses: any[]): void;
}
export default Vendor;
