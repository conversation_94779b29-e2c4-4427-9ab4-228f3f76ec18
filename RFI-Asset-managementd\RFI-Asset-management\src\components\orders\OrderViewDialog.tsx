import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Order } from "@/utils/OrdersLocalStorage";
import { FileDown, Printer, X } from "lucide-react";

interface OrderViewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order?: Order;
}

export function OrderViewDialog({
  open,
  onOpenChange,
  order
}: OrderViewDialogProps) {
  if (!order) {
    return null;
  }

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  // Handle export
  const handleExport = () => {
    // In a real app, this would generate a PDF or other export format
    console.log("Exporting order:", order);
  };

  // Defensive: get location name
  const getLocationName = () => {
    return order.locationName || order.Location || order.location || '';
  };

  // Defensive: calculate subtotal and total if missing
  const subtotal = typeof order.subtotal === 'number'
    ? order.subtotal
    : Array.isArray(order.items)
      ? order.items.reduce((sum, item) => sum + (item.amount || 0), 0)
      : 0;
  const total = typeof order.total === 'number' ? order.total : subtotal;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex justify-between items-center flex-row">
          <DialogTitle>Order</DialogTitle>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </DialogHeader>

        <div className="mt-4">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-blue-600">
              Order #{order.id} <span className="text-sm text-gray-500">dated {order.date}</span>
            </h2>
          </div>

          <div className="grid grid-cols-2 gap-8 mb-8">
            <div>
              <div className="flex items-center mb-2">
                <div className="bg-blue-100 p-2 rounded-md">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 100-12 6 6 0 000 12z" clipRule="evenodd" />
                    <path fillRule="evenodd" d="M10 4a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 4z" clipRule="evenodd" />
                    <path fillRule="evenodd" d="M10 10a.75.75 0 01.75.75v.01a.75.75 0 01-1.5 0V10.75A.75.75 0 0110 10z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-2">
                  <div className="text-sm text-gray-500">{getLocationName()}</div>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="font-medium">Vendor</h3>
                <div className="mt-1">
                  <div className="font-medium">{order.vendor}</div>
                  {order.vendorContact && <div>{order.vendorContact}</div>}
                  {order.vendorEmail && (
                    <div className="flex items-center mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                      <a href={`mailto:${order.vendorEmail}`} className="text-blue-600 hover:underline">
                        {order.vendorEmail}
                      </a>
                    </div>
                  )}
                  {order.vendorPhone && (
                    <div className="flex items-center mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                      </svg>
                      <a href={`tel:${order.vendorPhone}`} className="text-blue-600 hover:underline">
                        {order.vendorPhone}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div>
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Status:</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    order.status === "Delivered"
                      ? "bg-green-100 text-green-800"
                      : order.status === "Pending" || order.status === "Processing"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-blue-100 text-blue-800"
                  }`}>
                    {order.status}
                  </span>
                </div>
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Created By:</span>
                  <span>{order.createdBy}</span>
                </div>
                {order.modifiedBy && (
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Modified By:</span>
                    <span>{order.modifiedBy}</span>
                  </div>
                )}
                {order.note && (
                  <div className="mt-4">
                    <span className="text-gray-600">Note:</span>
                    <p className="mt-1 text-sm">{order.note}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h3 className="font-medium mb-2">Product</h3>
            <div className="border rounded-md">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr className="border-b">
                    <th className="text-left py-2 px-4">Item</th>
                    <th className="text-right py-2 px-4">Qty</th>
                    <th className="text-right py-2 px-4">Unit Price</th>
                    <th className="text-right py-2 px-4">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {order.items.map((item) => (
                    <tr key={item.id} className="border-b">
                      <td className="py-3 px-4">{item.itemName}</td>
                      <td className="py-3 px-4 text-right">{item.quantity}</td>
                      <td className="py-3 px-4 text-right">${item.unitCost.toFixed(2)}</td>
                      <td className="py-3 px-4 text-right">${item.amount.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="flex justify-end">
            <div className="w-64">
              <div className="flex justify-between py-2">
                <span>Subtotal</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>

              {order.otherCosts.map((cost) => (
                <div key={cost.id} className="flex justify-between py-2">
                  <span>{cost.type}</span>
                  <span>${cost.amount.toFixed(2)}</span>
                </div>
              ))}

              <div className="flex justify-between py-2 font-bold border-t mt-2">
                <span>Total</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
