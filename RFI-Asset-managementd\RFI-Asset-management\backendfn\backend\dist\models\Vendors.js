"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
// Vendor model class
class Vendor extends sequelize_1.Model {
    // Helper methods to work with JSON fields
    getSecondaryContactsData() {
        try {
            return this.secondaryContacts ? JSON.parse(this.secondaryContacts) : [];
        }
        catch (error) {
            console.error('Error parsing secondary contacts:', error);
            return [];
        }
    }
    setSecondaryContactsData(contacts) {
        try {
            this.secondaryContacts = JSON.stringify(contacts);
        }
        catch (error) {
            console.error('Error stringifying secondary contacts:', error);
            this.secondaryContacts = '[]';
        }
    }
    getAddressesData() {
        try {
            return this.addresses ? JSON.parse(this.addresses) : [];
        }
        catch (error) {
            console.error('Error parsing addresses:', error);
            return [];
        }
    }
    setAddressesData(addresses) {
        try {
            this.addresses = JSON.stringify(addresses);
        }
        catch (error) {
            console.error('Error stringifying addresses:', error);
            this.addresses = '[]';
        }
    }
}
// Initialize the model
Vendor.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    companyName: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        field: 'company_name',
    },
    title: {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: true,
    },
    firstName: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        field: 'first_name',
    },
    lastName: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        field: 'last_name',
    },
    phone: {
        type: sequelize_1.DataTypes.STRING(20),
        allowNull: false,
    },
    email: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        validate: {
            isEmail: true,
        },
    },
    status: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
    },
    secondaryContacts: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        field: 'secondary_contacts',
    },
    addresses: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
}, {
    sequelize: database_1.sequelize,
    tableName: 'vendors',
    underscored: true,
    timestamps: true,
    indexes: [
        {
            name: 'idx_vendor_email',
            fields: ['email'],
            unique: true,
        },
        {
            name: 'idx_vendor_company',
            fields: ['company_name'],
        },
        {
            name: 'idx_vendor_status',
            fields: ['status'],
        },
        {
            name: 'idx_vendor_name',
            fields: ['first_name', 'last_name'],
        },
    ],
});
exports.default = Vendor;
