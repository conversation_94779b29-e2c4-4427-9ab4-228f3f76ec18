import express from 'express';
import TransactionOrder from '../models/TransactionOrders';
import ConsumablesCategory from '../models/ConsumablesCategory';
import PackingLocation from '../models/PackingLocation';
import { Op, fn, col } from 'sequelize';

const router = express.Router();

// Get all transaction orders with filtering and pagination
router.get('/', async (req: any, res: any) => {
  try {
    let {
      page = 1,
      limit = 10,
      search,
      status,
      dateFrom,
      dateTo,
      vendor,
      location, // Changed from stockLocation to location
      consumable // Added consumable filter
    } = req.query;

    // Ensure page and limit are strings for parseInt
    const pageNum = Array.isArray(page) ? page[0] : page;
    const limitNum = Array.isArray(limit) ? limit[0] : limit;
    const offset = (parseInt(pageNum as string) - 1) * parseInt(limitNum as string);
    // Use index signature for whereClause
    const whereClause: { [key: string]: any } = {};

    // Search filter (by refNo, vendor, consumable, or ID)
    if (search) {
      const searchVal = Array.isArray(search) ? search[0] : search;
      whereClause[Op.or as any] = [
        { id: { [Op.iLike as any]: `%${searchVal}%` } },
        { vendor: { [Op.iLike as any]: `%${searchVal}%` } },
        { consumable: { [Op.iLike as any]: `%${searchVal}%` } }, // Added consumable search
        { refNo: { [Op.eq]: isNaN(Number(searchVal)) ? null : parseInt(searchVal as string) } }
      ].filter(condition => condition.refNo !== null || !('refNo' in condition));
    }

    // Status filter
    if (status && status !== 'all') {
      const statusVal = Array.isArray(status) ? status[0] : status;
      whereClause.status = statusVal;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      whereClause.date = {};
      if (dateFrom) whereClause.date[Op.gte as any] = Array.isArray(dateFrom) ? dateFrom[0] : dateFrom;
      if (dateTo) whereClause.date[Op.lte as any] = Array.isArray(dateTo) ? dateTo[0] : dateTo;
    }

    // Vendor filter
    if (vendor) {
      const vendorVal = Array.isArray(vendor) ? vendor[0] : vendor;
      whereClause.vendor = { [Op.iLike as any]: `%${vendorVal}%` };
    }

    // Location filter (changed from stockLocation)
    if (location) {
      const locationVal = Array.isArray(location) ? location[0] : location;
      whereClause.location = locationVal; // Changed from stockLocation to location
    }

    // Consumable filter
    if (consumable) {
      const consumableVal = Array.isArray(consumable) ? consumable[0] : consumable;
      whereClause.consumable = { [Op.iLike as any]: `%${consumableVal}%` };
    }

    const { count, rows } = await TransactionOrder.findAndCountAll({
      where: whereClause,
      limit: parseInt(limitNum as string),
      offset: offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: rows,
      pagination: {
        total: count,
        page: parseInt(pageNum as string),
        limit: parseInt(limitNum as string),
        pages: Math.ceil(count / parseInt(limitNum as string))
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction orders',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get order summary statistics
router.get('/summary', async (req: any, res: any) => {
  try {
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const [
      totalOrders,
      pendingOrders,
      inTransitOrders,
      ordersThisMonth
    ] = await Promise.all([
      TransactionOrder.count(),
      TransactionOrder.count({ where: { status: 'pending' } }),
      TransactionOrder.count({ where: { status: 'approved' } }),
      TransactionOrder.count({
        where: {
          createdAt: {
            [Op.gte as any]: currentMonth,
            [Op.lt as any]: nextMonth
          }
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        totalOrders,
        pendingOrders,
        inTransitOrders,
        ordersThisMonth
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching order summary',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get consumable summary statistics
router.get('/consumable-summary', async (req: any, res: any) => {
  try {
    const { consumable, dateFrom, dateTo } = req.query;
    
    let whereClause: { [key: string]: any } = { status: 'completed' };
    
    if (consumable) {
      const consumableVal = Array.isArray(consumable) ? consumable[0] : consumable;
      whereClause.consumable = { [Op.iLike as any]: `%${consumableVal}%` };
    }

    if (dateFrom || dateTo) {
      whereClause.date = {};
      if (dateFrom) whereClause.date[Op.gte as any] = Array.isArray(dateFrom) ? dateFrom[0] : dateFrom;
      if (dateTo) whereClause.date[Op.lte as any] = Array.isArray(dateTo) ? dateTo[0] : dateTo;
    }

    const orders = await TransactionOrder.findAll({
      where: whereClause,
      attributes: ['consumable', 'qty', 'items', 'total', 'location', 'date'] // Added qty field
    });

    const consumableSummary: { [key: string]: any } = {};

    orders.forEach((order: any) => {
      const consumableName = order.consumable;
      if (!consumableSummary[consumableName]) {
        consumableSummary[consumableName] = {
          totalOrders: 0,
          totalAmount: 0,
          totalQty: 0, // Added total qty for the consumable itself
          totalItems: 0,
          locations: new Set(),
          items: {}
        };
      }

      consumableSummary[consumableName].totalOrders += 1;
      consumableSummary[consumableName].totalAmount += parseFloat(order.total);
      consumableSummary[consumableName].totalQty += order.qty; // Added qty aggregation
      consumableSummary[consumableName].locations.add(order.location); // Changed stockLocation to location

      order.items.forEach((item: any) => {
        const itemKey = item.item;
        if (!consumableSummary[consumableName].items[itemKey]) {
          consumableSummary[consumableName].items[itemKey] = {
            totalQty: 0,
            totalAmount: 0
          };
        }
        consumableSummary[consumableName].items[itemKey].totalQty += item.qty;
        consumableSummary[consumableName].items[itemKey].totalAmount += item.amount;
        consumableSummary[consumableName].totalItems += item.qty;
      });
    });

    // Convert Set to Array for locations
    Object.keys(consumableSummary).forEach(key => {
      consumableSummary[key].locations = Array.from(consumableSummary[key].locations);
    });

    res.json({
      success: true,
      data: consumableSummary
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching consumable summary',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get stock summary by location and item
router.get('/stock-summary', async (req: any, res: any) => {
  try {
    const { location, item, consumable } = req.query;
    const whereClause: { [key: string]: any } = { status: 'completed' };

    if (location) {
      whereClause.location = location; // Changed from stockLocation to location
    }

    if (consumable) {
      const consumableVal = Array.isArray(consumable) ? consumable[0] : consumable;
      whereClause.consumable = { [Op.iLike as any]: `%${consumableVal}%` };
    }

    const orders = await TransactionOrder.findAll({
      where: whereClause,
      attributes: ['items', 'qty', 'location', 'consumable'] // Added qty field
    });

    const stockSummary: { [key: string]: { [key: string]: any } } = {};

    orders.forEach((order: any) => {
      const location = (order as any).location; // Changed from stockLocation to location
      const consumableName = (order as any).consumable;
      const orderQty = (order as any).qty;
      
      const locationKey = `${location} - ${consumableName}`;
      if (!stockSummary[locationKey]) {
        stockSummary[locationKey] = {
          orderQty: 0, // Track the order-level qty
        };
      }
      
      stockSummary[locationKey].orderQty += orderQty; // Aggregate order-level qty
      
      order.items.forEach((orderItem: any) => {
        if (!item || orderItem.item === item) {
          const itemKey = `${orderItem.item} - ${orderItem.consumable}`;
          if (!stockSummary[locationKey][itemKey]) {
            stockSummary[locationKey][itemKey] = {
              item: orderItem.item,
              consumable: orderItem.consumable,
              location: location,
              orderConsumable: consumableName,
              totalQty: 0,
              totalAmount: 0
            };
          }
          stockSummary[locationKey][itemKey].totalQty += orderItem.qty;
          stockSummary[locationKey][itemKey].totalAmount += orderItem.amount;
        }
      });
    });

    res.json({
      success: true,
      data: stockSummary
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching stock summary',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get single transaction order by ID
router.get('/:id', async (req: any, res: any) => {
  try {
    const order = await TransactionOrder.findByPk(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Transaction order not found'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction order',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Create new transaction order
router.post('/', async (req: any, res: any) => {
  try {
    const orderData = req.body;
    
    // Validate required fields including consumable and qty
    if (!orderData.vendor ||
        !orderData.date || 
        !orderData.location || 
        !orderData.consumable ||
        !orderData.qty ||
        !orderData.items || 
        orderData.items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: vendor, date, location, consumable, qty, and items are required'
      });
    }

    // Validate qty is a positive number
    if (typeof orderData.qty !== 'number' || orderData.qty <= 0) {
      return res.status(400).json({
        success: false,
        message: 'qty must be a positive number'
      });
    }

    const order = await TransactionOrder.create(orderData);

    res.status(201).json({
      success: true,
      message: 'Transaction order created successfully',
      data: order
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: 'Error creating transaction order',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Update transaction order
router.put('/:id', async (req: any, res: any) => {
  try {
    const order = await TransactionOrder.findByPk(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Transaction order not found'
      });
    }

    // Validate qty if it's being updated
    if (req.body.qty !== undefined) {
      if (typeof req.body.qty !== 'number' || req.body.qty <= 0) {
        return res.status(400).json({
          success: false,
          message: 'qty must be a positive number'
        });
      }
    }

    await order.update(req.body);

    res.json({
      success: true,
      message: 'Transaction order updated successfully',
      data: order
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: 'Error updating transaction order',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Update order status
router.patch('/:id/status', async (req: any, res: any) => {
  try {
    const { status } = req.body;
    const validStatuses = ['pending', 'approved', 'completed', 'cancelled'];
    
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      });
    }

    const order = await TransactionOrder.findByPk(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Transaction order not found'
      });
    }

    await order.update({ status });

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: order
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: 'Error updating order status',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Delete transaction order
router.delete('/:id', async (req: any, res: any) => {
  try {
    const order = await TransactionOrder.findByPk(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Transaction order not found'
      });
    }

    await order.destroy();

    res.json({
      success: true,
      message: 'Transaction order deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting transaction order',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Bulk delete transaction orders
router.delete('/', async (req: any, res: any) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or missing ids array'
      });
    }

    const deletedCount = await TransactionOrder.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    return res.json({
      success: true,
      message: `${deletedCount} transaction orders deleted successfully`,
      deletedCount
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting transaction orders',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get consumable categories for item dropdown
router.get('/config/consumable-categories', async (req: any, res: any) => {
  try {
    const categories = await ConsumablesCategory.findAll({
      attributes: ['id', 'name'],
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching consumable categories',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get packing locations for location dropdown (changed comment)
router.get('/config/packing-locations', async (req: any, res: any) => {
  try {
    const locations = await PackingLocation.findAll({
      attributes: ['id', 'name'],
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: locations
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching packing locations',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get distinct vendors for dropdown
router.get('/config/vendors', async (req: any, res: any) => {
  try {
    const vendors = await TransactionOrder.findAll({
      attributes: [[fn('DISTINCT', col('vendor')), 'vendor']],
      order: [['vendor', 'ASC']],
      raw: true
    });

    res.json({
      success: true,
      data: vendors.map(v => ({ name: v.vendor }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching vendors',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get distinct consumables for dropdown
router.get('/config/consumables', async (req: any, res: any) => {
  try {
    const consumables = await TransactionOrder.findAll({
      attributes: [[fn('DISTINCT', col('consumable')), 'consumable']],
      order: [['consumable', 'ASC']],
      raw: true
    });

    res.json({
      success: true,
      data: consumables.map(c => ({ name: c.consumable }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching consumables',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

export default router;