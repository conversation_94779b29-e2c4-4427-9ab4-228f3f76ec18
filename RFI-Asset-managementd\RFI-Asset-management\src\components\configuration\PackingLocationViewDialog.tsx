import React from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Pencil, Printer, FileDown } from "lucide-react";
import { PackingLocation } from "@/services/packingLocationService";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { exportData } from "@/utils/exportUtils";

interface PackingLocationViewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  packingLocation: PackingLocation | null;
  onEdit: () => void;
}

export function PackingLocationViewDialog({
  open,
  onOpenChange,
  packingLocation,
  onEdit
}: PackingLocationViewDialogProps) {
  if (!packingLocation && open) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-xl">Error</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-red-500">No data available to display.</p>
          </div>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  if (!packingLocation) return null;

  // Handle print
  const handlePrint = () => {
    const printContent = `
      Location Details
      ========================
      Location: ${packingLocation.name}
      Area: ${packingLocation.area || 'N/A'}
      Status: ${packingLocation.status ? 'Active' : 'Inactive'}
      Created: ${new Date(packingLocation.createdAt).toLocaleDateString()}
      Updated: ${new Date(packingLocation.updatedAt).toLocaleDateString()}
      ID: ${packingLocation.id}
    `;
    
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Location - ${packingLocation.name}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { color: #333; }
              .detail { margin: 10px 0; }
            </style>
          </head>
          <body>
            <h1>Location Details</h1>
            <div class="detail"><strong>Location:</strong> ${packingLocation.name}</div>
            <div class="detail"><strong>Area:</strong> ${packingLocation.area || 'N/A'}</div>
            <div class="detail"><strong>Status:</strong> ${packingLocation.status ? 'Active' : 'Inactive'}</div>
            <div class="detail"><strong>Created:</strong> ${new Date(packingLocation.createdAt).toLocaleDateString()}</div>
            <div class="detail"><strong>Updated:</strong> ${new Date(packingLocation.updatedAt).toLocaleDateString()}</div>
            <div class="detail"><strong>ID:</strong> ${packingLocation.id}</div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = [{
      'Location Name': packingLocation.name,
      'Area': packingLocation.area || 'N/A',
      'Status': packingLocation.status ? 'Active' : 'Inactive',
      'Created': new Date(packingLocation.createdAt).toLocaleDateString(),
      'Updated': new Date(packingLocation.updatedAt).toLocaleDateString(),
      'ID': packingLocation.id,
    }];

    const headers = ['Location Name', 'Area', 'Status', 'Created', 'Updated', 'ID'];

    exportData(exportableData, format, `packing_location_${packingLocation.name.toLowerCase().replace(/\s+/g, '_')}`, 'Packing Location Details', headers);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-xl">Location Details</DialogTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleExport('pdf')}>
                  PDF Document
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('excel')}>
                  Excel Spreadsheet
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('csv')}>
                  CSV File
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <Label className="text-xs font-medium text-gray-600">Location</Label>
              <p className="text-lg font-semibold mt-1">{packingLocation.name}</p>
            </div>

            {packingLocation.area && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Area</Label>
                <p className="text-sm font-medium mt-1">{packingLocation.area}</p>
              </div>
            )}

            {packingLocation.area && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Area</Label>
                <p className="text-sm font-medium mt-1">{packingLocation.area}</p>
              </div>
            )}

            {(packingLocation.address || packingLocation.addressLine2 || packingLocation.city || packingLocation.state || packingLocation.zip) && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Address</Label>
                <div className="mt-1 space-y-1">
                  {packingLocation.address && <p className="text-sm font-medium">{packingLocation.address}</p>}
                  {packingLocation.addressLine2 && <p className="text-sm font-medium">{packingLocation.addressLine2}</p>}
                  {(packingLocation.city || packingLocation.state || packingLocation.zip) && (
                    <p className="text-sm font-medium">
                      {[packingLocation.city, packingLocation.state, packingLocation.zip].filter(Boolean).join(', ')}
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Status</Label>
                <p className={`text-sm font-medium mt-1 ${packingLocation.status ? "text-green-600" : "text-red-600"}`}>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    packingLocation.status ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                  }`}>
                    {packingLocation.status ? "Active" : "Inactive"}
                  </span>
                </p>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Created Date</Label>
                <p className="text-sm font-medium mt-1">{new Date(packingLocation.createdAt).toLocaleDateString()}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Last Updated</Label>
                <p className="text-sm font-medium mt-1">{new Date(packingLocation.updatedAt).toLocaleDateString()}</p>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Location ID</Label>
                <p className="text-xs font-mono mt-1 text-gray-600">{packingLocation.id}</p>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter className="flex justify-end space-x-3 mt-3 pt-2 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="px-8 h-9"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}