"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 5000;
// Middleware
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    credentials: true
}));
app.use(express_1.default.json());
// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Backend server is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});
// Test endpoint for frontend
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: 'API is working',
        data: {
            server: 'RFI Asset Management Backend',
            environment: process.env.NODE_ENV || 'development',
            database: {
                host: process.env.DB_HOST,
                port: process.env.DB_PORT,
                name: process.env.DB_NAME,
                username: process.env.DB_USERNAME
            }
        }
    });
});
// Simple auth endpoint for testing
app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    // Simple test credentials
    if (email === '<EMAIL>' && password === '**********') {
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: {
                    id: '1',
                    firstName: 'Admin',
                    lastName: 'User',
                    email: '<EMAIL>',
                    role: 'Portal Admin',
                    department: 'IT',
                    accessLevel: 'state'
                }
            }
        });
    }
    else {
        res.status(401).json({
            success: false,
            message: 'Invalid credentials'
        });
    }
});
// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl
    });
});
// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
    console.log(`💾 Database: ${process.env.DB_USERNAME}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`);
    console.log(`✅ Backend ready for connections`);
});
exports.default = app;
