import React from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Pencil, Printer, FileDown } from "lucide-react";
import { ConsumableCategory } from "@/services/consumablescategoryService";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { exportData } from "@/utils/exportUtils";

interface ConsumableCategoryViewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  consumableCategory: ConsumableCategory | null;
  onEdit: () => void;
}

export function ConsumableCategoryViewDialog({
  open,
  onOpenChange,
  consumableCategory,
  onEdit
}: ConsumableCategoryViewDialogProps) {
  if (!consumableCategory && open) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-xl">Error</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-red-500">No data available to display.</p>
          </div>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  if (!consumableCategory) return null;

  // Handle print
  const handlePrint = () => {
    const printContent = `
      Consumable Category Details
      =========================
      Consumable Category: ${consumableCategory.name}
      Status: ${consumableCategory.status ? 'Active' : 'Inactive'}
      Created: ${new Date(consumableCategory.createdAt).toLocaleDateString()}
      Updated: ${new Date(consumableCategory.updatedAt).toLocaleDateString()}
      ID: ${consumableCategory.id}
    `;
    
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Consumable Category - ${consumableCategory.name}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { color: #333; }
              .detail { margin: 10px 0; }
            </style>
          </head>
          <body>
            <h1>Consumable Category Details</h1>
            <div class="detail"><strong>Consumable Category:</strong> ${consumableCategory.name}</div>
            <div class="detail"><strong>Status:</strong> ${consumableCategory.status ? 'Active' : 'Inactive'}</div>
            <div class="detail"><strong>Created:</strong> ${new Date(consumableCategory.createdAt).toLocaleDateString()}</div>
            <div class="detail"><strong>Updated:</strong> ${new Date(consumableCategory.updatedAt).toLocaleDateString()}</div>
            <div class="detail"><strong>ID:</strong> ${consumableCategory.id}</div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = [{
      'Consumable Category': consumableCategory.name,
      'Status': consumableCategory.status ? 'Active' : 'Inactive',
      'Created': new Date(consumableCategory.createdAt).toLocaleDateString(),
      'Updated': new Date(consumableCategory.updatedAt).toLocaleDateString(),
      'ID': consumableCategory.id,
    }];

    const headers = ['Consumable Category', 'Status', 'Created', 'Updated', 'ID'];

    exportData(exportableData, format, `consumable_category_${consumableCategory.name.toLowerCase().replace(/\s+/g, '_')}`, 'Consumable Category Details', headers);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-xl">Consumable Category Details</DialogTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleExport('pdf')}>
                  PDF Document
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('excel')}>
                  Excel Spreadsheet
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('csv')}>
                  CSV File
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <Label className="text-xs font-medium text-gray-600">Consumable Category</Label>
              <p className="text-lg font-semibold mt-1">{consumableCategory.name}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Status</Label>
                <p className={`text-sm font-medium mt-1 ${consumableCategory.status ? "text-green-600" : "text-red-600"}`}>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    consumableCategory.status ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                  }`}>
                    {consumableCategory.status ? "Active" : "Inactive"}
                  </span>
                </p>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Created Date</Label>
                <p className="text-sm font-medium mt-1">{new Date(consumableCategory.createdAt).toLocaleDateString()}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Last Updated</Label>
                <p className="text-sm font-medium mt-1">{new Date(consumableCategory.updatedAt).toLocaleDateString()}</p>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label className="text-xs font-medium text-gray-600">Consumable Category ID</Label>
                <p className="text-xs font-mono mt-1 text-gray-600">{consumableCategory.id}</p>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter className="flex justify-end space-x-3 mt-3 pt-2 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="px-8 h-9"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}