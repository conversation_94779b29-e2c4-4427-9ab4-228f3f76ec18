"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sequelize = void 0;
exports.testConnection = testConnection;
const sequelize_1 = require("sequelize");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// Database configuration
exports.sequelize = new sequelize_1.Sequelize({
    database: process.env.DB_NAME || 'assetmanagementool',
    username: process.env.DB_USERNAME || 'ram',
    password: process.env.DB_PASSWORD || '12345678890R@m',
    host: process.env.DB_HOST || '**************',
    port: parseInt(process.env.DB_PORT || '3306'),
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
        max: 10,
        min: 0,
        acquire: 60000, // Increased timeout for remote connections
        idle: 10000
    },
    retry: {
        max: 3
    },
    define: {
        timestamps: true,
        underscored: true,
        freezeTableName: true
    },
    timezone: '+00:00',
    dialectOptions: {
        charset: 'utf8mb4',
        // Remove the collate option that's causing the warning
        connectTimeout: 60000, // 60 seconds
        acquireTimeout: 60000,
        timeout: 60000,
        // Add SSL configuration if needed for remote connection
        ssl: process.env.NODE_ENV === 'production' ? {
            require: true,
            rejectUnauthorized: false
        } : false
    }
});
// Test connection function
async function testConnection() {
    try {
        await exports.sequelize.authenticate();
        console.log('✅ Database connection has been established successfully.');
        return true;
    }
    catch (error) {
        console.error('❌ Unable to connect to the database:', error);
        return false;
    }
}
exports.default = exports.sequelize;
