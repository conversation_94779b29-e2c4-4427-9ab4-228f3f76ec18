"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
// Maintenance model class
class Maintenance extends sequelize_1.Model {
    // Helper method to check if maintenance is overdue
    isOverdue() {
        if (!this.scheduledDate)
            return false;
        return new Date() > this.scheduledDate && this.status !== 'COMPLETED';
    }
    // Helper method to get priority weight for sorting
    getPriorityWeight() {
        const weights = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
        return weights[this.priority] || 0;
    }
    // Helper method to calculate days until scheduled date
    getDaysUntilScheduled() {
        if (!this.scheduledDate)
            return null;
        const today = new Date();
        const scheduled = new Date(this.scheduledDate);
        const diffTime = scheduled.getTime() - today.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
}
// Initialize the model
Maintenance.init({
    assetTag: {
        type: sequelize_1.DataTypes.STRING,
        primaryKey: true,
        allowNull: false,
        field: 'asset_tag', // Map to actual database column name
        validate: {
            notEmpty: true,
        },
    },
    category: {
        type: sequelize_1.DataTypes.ENUM('AV computers', 'AV printers', 'tabs', 'others', 'scanners', 'pollpads', 'monitors'),
        allowNull: false,
        validate: {
            notEmpty: true,
        },
    },
    name: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        validate: {
            notEmpty: true,
        },
    },
    requestType: {
        type: sequelize_1.DataTypes.ENUM('preventive', 'corrective', 'emergency'),
        allowNull: false,
        field: 'request_type', // Map to actual database column name
        validate: {
            notEmpty: true,
        },
    },
    priority: {
        type: sequelize_1.DataTypes.ENUM('high', 'low', 'medium', 'critical'),
        allowNull: false,
        defaultValue: 'medium',
    },
    assignedTechnician: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
        field: 'assigned_technician', // Map to actual database column name
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    scheduledDate: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'scheduled_date', // Map to actual database column name
    },
    scheduledFrequency: {
        type: sequelize_1.DataTypes.ENUM('once', 'weekly', 'monthly', 'quarterly', 'half-yearly', 'yearly'),
        allowNull: true,
        field: 'scheduled_frequency', // Map to actual database column name
    },
    estimatedHours: {
        type: sequelize_1.DataTypes.DECIMAL(8, 2), // Allows for precise decimal values
        allowNull: true,
        field: 'estimated_hours', // Map to actual database column name
        validate: {
            min: 0,
        },
    },
    estimatedCost: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2), // Allows for currency values
        allowNull: true,
        field: 'estimated_cost', // Map to actual database column name
        validate: {
            min: 0,
        },
    },
    notes: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('REQUESTED', 'SCHEDULED', 'IN_PROGRESS', 'TESTING', 'COMPLETED'),
        allowNull: false,
        defaultValue: 'REQUESTED',
    },
}, {
    sequelize: database_1.sequelize,
    modelName: 'Maintenance',
    tableName: 'maintenance',
    timestamps: true,
    indexes: [
        {
            fields: ['status'],
        },
        {
            fields: ['priority'],
        },
        {
            fields: ['scheduled_date'],
        },
        {
            fields: ['assigned_technician'],
        },
        {
            fields: ['category'],
        },
    ],
});
exports.default = Maintenance;
