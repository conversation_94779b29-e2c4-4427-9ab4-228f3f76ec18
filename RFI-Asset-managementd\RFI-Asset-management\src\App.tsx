import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useLocation } from "react-router-dom";
import { SidebarProvider } from "./contexts/SidebarContext";
import LandingPage from "./pages/LandingPage";
import LoginPage from "./pages/LoginPage";
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import Index from "./pages/Index";

import { getUsers } from "./utils/UserManagementLocalStorage";
import { InstallPrompt } from "@/components/pwa/InstallPrompt";
import { OfflineIndicator } from "@/components/pwa/OfflineIndicator";
import AssetsPage from "./pages/masters/AssetsPage";
import TransactionsPage from "./pages/transactions/TransactionsPage";
//import transactionsIndex from "./pages/transactions/transactionsIndex"
// import AssetDashboard from "./pages/transactions/Transactionstabs/AssetDashboard";
// import AssetLifecycle from "./pages/transactions/Transactionstabs/AssetLifecycle";
// import CageManagement from "./pages/transactions/Transactionstabs/Cagemanagement";
// import CheckInOut from "./pages/transactions/Transactionstabs/CheckInOut";
// import DamageReports from "./pages/transactions/Transactionstabs/DamageReports";
// import RepairWorkflow from "./pages/transactions/Transactionstabs/RepairWorkflow";
// import TransferTracking from "./pages/transactions/Transactionstabs/TransferTracking";
// import Consumables from "./pages/transactions/Transactionstabs/Consumables";
import TransactionAssetsPage from "./pages/transactions/AssetsPage";
import CheckoutPage from "./pages/transactions/assets/CheckoutPage";
import CheckinPage from "./pages/transactions/assets/CheckinPage";
import TrackingPage from "./pages/transactions/assets/TrackingPage";
import TransactionConsumablesPage from "./pages/transactions/consumables/ConsumablesPage";
import OrdersPage from "./pages/transactions/consumables/OrdersPageFixed";
import StockPage from "./pages/transactions/consumables/StockPage";
import ConsumableCheckoutPage from "./pages/transactions/consumables/CheckoutPage";
import ConsumableCheckoutListPage from "./pages/transactions/consumables/CheckoutListPage";
 import ConsumableCheckoutViewPage from "./pages/transactions/consumables/CheckoutViewPage";
import MaintenancePage from "./pages/MaintenancePage";
import ReportsPage from "./pages/ReportsPage";

import CountiesPage from "./pages/CountiesPage";
import MapPage from "./pages/MapPage";

import SearchPage from "./pages/SearchPage";
import ProfilePage from "./pages/ProfilePage";
import NotFound from "./pages/NotFound";
import UserManagementPage from "./pages/user-management/UserManagementPage";
import UsersPageComponent from "./pages/user-management/UsersPage";

import UserGroupsPage from "./pages/user-management/UserGroupsPage";
import PrivilegesPage from "./pages/user-management/PrivilegesPage";

import MastersPage from "./pages/masters/MastersPage";
import ElectionsPage from "./pages/masters/ElectionsPage";
import LocationPage from "./pages/masters/LocationPage";
import VendorsPage from "./pages/masters/VendorsPage";
import AssetsMasterPage from "./pages/masters/AssetsMasterPage";
import ConsumablesPage from "./pages/masters/ConsumablesPage";
import ConsumablesCategoryPage from "./pages/configuration/settings/ConsumablesCategoryPage";
import OtherCostTypePage from "./pages/configuration/settings/OtherCostTypePage";
import PackingLocationPage from "./pages/configuration/settings/PackingLocationPage";
import PackingListMasterPage from "./pages/masters/PackingListPage";
import RollingCageMasterPage from "./pages/masters/RollingCagePage";
import SupplyPackingListPage from "./pages/supply-checklist/packing-list/PackingListPage";
import SupplyRollingCagePage from "./pages/supply-checklist/rolling-cage/RollingCagePage";
import RollingCagePackPage from "./pages/supply-checklist/rolling-cage/RollingCagePackPage";
import RollingCageProofPage from "./pages/supply-checklist/rolling-cage/RollingCageProofPage";
import RollingCageUnpackPage from "./pages/supply-checklist/rolling-cage/RollingCageUnpackPage";
import RollingCageCompletePage from "./pages/supply-checklist/rolling-cage/RollingCageCompletePage";
import VoterCardTestingPage from "./pages/la-checklist/pollpads/VoterCardTestingPage";
import VoterFileLoadedPage from "./pages/la-checklist/pollpads/VoterFileLoadedPage";

import LAChecklistPage from "./pages/la-checklist/LAChecklistPage";
import SimpleLAChecklistPage from "./pages/la-checklist/SimpleLAChecklistPage";
import PollpadsPage from "./pages/la-checklist/pollpads/PollpadsPage";
import SimplePollpadsPage from "./pages/la-checklist/pollpads/SimplePollpadsPage";
import ScannersPage from "./pages/la-checklist/scanners/ScannersPage";
import SimpleScannersPage from "./pages/la-checklist/scanners/SimpleScannersPage";
import BMDPage from "./pages/la-checklist/bmd/BMDPage";
import SimpleBMDPage from "./pages/la-checklist/bmd/SimpleBMDPage";
import SimpleVoterCardTestingPage from "./pages/la-checklist/pollpads/SimpleVoterCardTestingPage";
import SimpleVoterFileLoadedPage from "./pages/la-checklist/pollpads/SimpleVoterFileLoadedPage";
import ProofPage from "./pages/supply-checklist/packing-list/ProofPage";
import PackPage from "./pages/supply-checklist/packing-list/PackPage";
import CompletePage from "./pages/supply-checklist/packing-list/CompletePage";
import UnpackPage from "./pages/supply-checklist/packing-list/UnpackPage";
import PackingListDetailPage from "./pages/supply-checklist/packing-list/PackingListDetailPage";
import RollingCageDetailPage from "./pages/supply-checklist/rolling-cage/RollingCageDetailPage";
import AssetTypePage from "./pages/configuration/settings/AssetTypePage";
import ElectionReadinessPage from "./pages/supply-checklist/election-readiness/ElectionReadinessPage";
import ServiceMaintenanceTypePage from "./pages/configuration/settings/ServiceMaintenanceTypePage";
//import SupplyChainProgressPage from "./pages/supply-checklist/supply-chain/SupplyChainProgressPage";
import SupplyChecklistPage from "./pages/supply-checklist/SupplyChecklistPage";
import { WarrantyManagementPage } from "./pages/masters/warranty/WarrantyManagementPage";

// Maintenance Pages
// import ServicePage from "./pages/maintenance/ServicePage";
// import SimpleServicePage from "./pages/maintenance/SimpleServicePage";
// import PollpadChecklistPage from "./pages/maintenance/PollpadChecklistPage";
// import SimplePollpadChecklistPage from "./pages/maintenance/SimplePollpadChecklistPage";
// import TabulatorChecklistPage from "./pages/maintenance/TabulatorChecklistPage";
// import SimpleTabulatorChecklistPage from "./pages/maintenance/SimpleTabulatorChecklistPage";
// import BMDChecklistPage from "./pages/maintenance/BMDChecklistPage";
// import SimpleBMDChecklistPage from "./pages/maintenance/SimpleBMDChecklistPage";
// import SimpleAVComputerChecklistPage from "./pages/maintenance/SimpleAVComputerChecklistPage";
// import AVComputerChecklistListPage from "./pages/maintenance/AVComputerChecklistListPage";
// import SimpleAVPrinterChecklistPage from "./pages/maintenance/SimpleAVPrinterChecklistPage";

import { EditMaintenanceRequestModal } from "./pages/maintenance/EditMaintenanceRequestModal";
//import { ServiceProviderModal } from "./pages/maintenance/ServiceProviderModal";
import { GroupMaintenanceRequestModal } from "./pages/maintenance/GroupMaintenanceRequestModal";
import { MaintenanceTab } from "./pages/maintenance/MaintenanceTab";
import { MaintenanceCalendar } from "./pages/maintenance/MaintenanceCalendar";
import { MaintenanceHistoryModal } from "./pages/maintenance/MaintenanceHistoryModal";
//import { NewMaintenanceRequestModal } from "./pages/maintenance/NewMaintenanceRequestModal";
import { MaintenanceWorkflow } from "./pages/maintenance/MaintenanceWorkflow";
import { RepairTab } from "./pages/maintenance/RepairTab";
// Reports Pages
// import StandardReportsPage from "./pages/reports/StandardReportsPage";
// import AdvancedReportsPage from "./pages/reports/AdvancedReportsPage";
// import SimpleAdvancedReportsPage from "./pages/reports/SimpleAdvancedReportsPage";
// import ElectionReadinessReportPage from "./pages/reports/advanced/ElectionReadinessPage";
// import SimpleElectionReadinessPage from "./pages/reports/advanced/SimpleElectionReadinessPage";
// import SuppliesReorderPage from "./pages/reports/advanced/SuppliesReorderPage";
// import SimpleSuppliesReorderPage from "./pages/reports/advanced/SimpleSuppliesReorderPage";
// import SupplyChainReadinessPage from "./pages/reports/advanced/SupplyChainReadinessPage";
// import TestReportPage from "./pages/reports/TestReportPage";
import ReportsAnalyticsPage from "./pages/reports-analytics/ReportsAnalyticsPage";
import CustomReportsPage from "./pages/reports-analytics/CustomReportsPage";
import ScheduledReportsPage from "./pages/reports-analytics/ScheduledReportsPage";
import AuditReportsPage from "./pages/reports-analytics/AuditReportsPage";
import AssetScanLogsPage from "./pages/reports-analytics/audit/AssetScanLogsPage";
import ManageSchedulesPage from "./pages/reports-analytics/ManageSchedulesPage";
// Configuration Pages
import ConfigurationPage from "./pages/configuration/ConfigurationPage";
import APIConfigPage from "./pages/configuration/APIConfigPage";
import SettingsPage from "./pages/configuration/settings/SettingsPage";
import ElectionTypePage from "./pages/configuration/settings/ElectionTypePage";
import AssetModelPage from "./pages/configuration/settings/AssetModelPage";
import AssetStatusPage from "./pages/configuration/settings/AssetStatusPage";

// Create auth context
interface AuthContextType {
  isAuthenticated: boolean;
  login: (username: string, role: string, county?: string, accessLevel?: 'county' | 'state') => void;
  logout: () => void;
  userCounty?: string;
  accessLevel?: 'county' | 'state';
  selectedCountyData: any;
  setSelectedCountyData: (data: any) => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Protected route component
interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const location = useLocation();
  const isAuthenticated = localStorage.getItem("isLoggedIn") === "true";

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

const queryClient = new QueryClient();

import { AppProvider } from './context/AppContext';
import UserActivityPage from "./pages/audit/UserActivityPage";
import AssetMovementPage from "./pages/audit/AssetMovementPage";
import SystemAccessPage from "./pages/audit/SystemAccessPage";

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(
    localStorage.getItem("isLoggedIn") === "true"
  );
  const [userCounty, setUserCounty] = useState<string | undefined>(undefined);
  const [accessLevel, setAccessLevel] = useState<'county' | 'state' | undefined>(undefined);
  const [selectedCountyData, setSelectedCountyData] = useState<any>(null);

  useEffect(() => {
    // Initialize mock users on app load to ensure they're available
    getUsers();

    // Check if user is logged in on app load
    const loggedIn = localStorage.getItem("isLoggedIn") === "true";
    setIsAuthenticated(loggedIn);

    // Load user data if logged in
    if (loggedIn) {
      const userData = localStorage.getItem("user");
      if (userData) {
        const user = JSON.parse(userData);
        setUserCounty(user.county);
        setAccessLevel(user.accessLevel);
      }
    }
  }, []);

  const login = (username: string, role: string, county?: string, accessLevel?: 'county' | 'state') => {
    localStorage.setItem("isLoggedIn", "true");
    localStorage.setItem("user", JSON.stringify({ username, role, county, accessLevel }));
    setIsAuthenticated(true);
    setUserCounty(county);
    setAccessLevel(accessLevel);
  };

  const logout = () => {
    localStorage.removeItem("isLoggedIn");
    localStorage.removeItem("user");
    setIsAuthenticated(false);
    setUserCounty(undefined);
    setAccessLevel(undefined);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AppProvider>
        <AuthContext.Provider value={{
          isAuthenticated,
          login,
          logout,
          userCounty,
          accessLevel,
          selectedCountyData,
          setSelectedCountyData
        }}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <SidebarProvider>
                {import.meta.env.PROD && (
                  <>
                    <InstallPrompt />
                    <OfflineIndicator />
                  </>
                )}
                <Routes>
                  <Route path="/" element={<LandingPage />} />
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <Index />
                    </ProtectedRoute>
                  } />

                  {/* User Management Routes */}
                  <Route path="/user-management" element={<UserManagementPage />} />
                  <Route path="/user-management/user-groups" element={<UserGroupsPage />} />
                  <Route path="/user-management/privileges" element={<PrivilegesPage />} />
                  <Route path="/user-management/users" element={<UsersPageComponent />} />


                  {/* Masters Routes */}
                  <Route path="/masters" element={<MastersPage />} />
                  <Route path="/masters/elections" element={<ElectionsPage />} />
                  <Route path="/masters/location" element={<LocationPage />} />
                  <Route path="/masters/vendors" element={<VendorsPage />} />
                  <Route path="/masters/assets" element={<AssetsPage />} />
                  <Route path="/masters/consumables" element={<ConsumablesPage />} />
                  <Route path="/masters/packing-list" element={<PackingListMasterPage />} />
                  <Route path="/masters/rolling-cage" element={<RollingCageMasterPage />} />
                  <Route path="/masters/warranty" element={<WarrantyManagementPage />} />                  {/*Transactions Routes */}
                  <Route path="/transactions/*" element={<TransactionsPage />} />
                  <Route path="/transactions/assets" element={<TransactionAssetsPage />} />
                  <Route path="/transactions/assets/checkout" element={<CheckoutPage />} />
                  <Route path="/transactions/checkout" element={<CheckoutPage />} />
                  <Route path="/transactions/assets/checkin" element={<CheckinPage />} />
                  <Route path="/transactions/checkin" element={<CheckinPage />} /> 
                  <Route path="/transactions/assets/tracking" element={<TrackingPage />} />
                  <Route path="/transactions/assets/tracking/:checkoutId" element={<TrackingPage />} />
                  <Route path="/transactions/consumables" element={<TransactionConsumablesPage />} />
                  <Route path="/transactions/consumables/orders" element={<OrdersPage />} />
                  <Route path="/transactions/consumables/stock" element={<StockPage />} />
                  <Route path="/transactions/consumables/checkout" element={<ConsumableCheckoutPage />} />
                  <Route path="/transactions/consumables/checkout-list" element={<ConsumableCheckoutListPage />} />
                  <Route path="/transactions/consumables/checkout/:id" element={<ConsumableCheckoutViewPage />} />
                  <Route path="/transactions/consumables/checkout/:id/edit" element={<ConsumableCheckoutPage />} /> 

                  {/* Supply Checklist Routes */}
                  <Route path="/supply-checklist" element={<SupplyChecklistPage />} />
                  <Route path="/supply-checklist/election-readiness" element={<ElectionReadinessPage />} />
                  <Route path="/supply-checklist/packing-list" element={<SupplyPackingListPage />} />
                  <Route path="/supply-checklist/packing-list/pack" element={<PackPage />} />
                  <Route path="/supply-checklist/packing-list/proof" element={<ProofPage />} />
                  <Route path="/supply-checklist/packing-list/complete" element={<CompletePage />} />
                  <Route path="/supply-checklist/packing-list/unpack" element={<UnpackPage />} />
                  <Route path="/supply-checklist/packing-list/:id" element={<PackingListDetailPage />} />
                  <Route path="/supply-checklist/rolling-cage" element={<SupplyRollingCagePage />} />
                  <Route path="/supply-checklist/rolling-cage/pack" element={<RollingCagePackPage />} />
                  <Route path="/supply-checklist/rolling-cage/proof" element={<RollingCageProofPage />} />
                  <Route path="/supply-checklist/rolling-cage/unpack" element={<RollingCageUnpackPage />} />
                  <Route path="/supply-checklist/rolling-cage/complete" element={<RollingCageCompletePage />} />
                  <Route path="/supply-checklist/rolling-cage/:id" element={<RollingCageDetailPage />} />

                  {/* L&A Checklist Routes */}
                  <Route path="/la-checklist" element={<SimpleLAChecklistPage />} />
                  <Route path="/la-checklist/pollpads" element={<SimplePollpadsPage />} />
                  <Route path="/la-checklist/pollpads/voter-card-testing" element={<SimpleVoterCardTestingPage />} />
                  <Route path="/la-checklist/pollpads/voter-file-loaded" element={<SimpleVoterFileLoadedPage />} />

                  <Route path="/la-checklist/scanners" element={<SimpleScannersPage />} />
                  <Route path="/la-checklist/bmd" element={<SimpleBMDPage />} />

                  {/* Maintenance Routes */}
                  <Route path="/maintenance/*" element={<MaintenancePage />} />
                  
                  {/* Comment out old maintenance routes */}
                  {/* <Route path="/maintenance/service" element={<SimpleServicePage />} />
                  <Route path="/maintenance/pollpad-checklist" element={<SimplePollpadChecklistPage />} />
                  <Route path="/maintenance/tabulator-checklist" element={<SimpleTabulatorChecklistPage />} />
                  <Route path="/maintenance/bmd-checklist" element={<SimpleBMDChecklistPage />} />
                  <Route path="/maintenance/av-computer-checklist" element={<SimpleAVComputerChecklistPage />} />
                  <Route path="/maintenance/av-computer-checklist-list" element={<AVComputerChecklistListPage />} />
                  <Route path="/maintenance/av-printer-checklist" element={<SimpleAVPrinterChecklistPage />} /> */}

                  {/* Reports Routes
                  <Route path="/reports" element={<ReportsPage />} />
                  <Route path="/reports/test" element={<TestReportPage />} />
                  <Route path="/reports/standard" element={<StandardReportsPage />} />
                  <Route path="/reports/advanced" element={<SimpleAdvancedReportsPage />} />
                  <Route path="/reports/advanced/election-readiness" element={<SimpleElectionReadinessPage />} />
                  <Route path="/reports/advanced/supplies-reorder" element={<SimpleSuppliesReorderPage />} />
                  <Route path="/reports/advanced/supply-chain-readiness" element={<SupplyChainReadinessPage />} /> */}

                  {/* Reports & Analytics Routes */}
                  <Route path="/reports-analytics" element={<ProtectedRoute><ReportsAnalyticsPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/custom" element={<ProtectedRoute><CustomReportsPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/scheduled" element={<ProtectedRoute><ScheduledReportsPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/scheduled/manage" element={<ProtectedRoute><ManageSchedulesPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/audit" element={<ProtectedRoute><AuditReportsPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/audit/user-activity" element={<ProtectedRoute><UserActivityPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/audit/asset-movement" element={<ProtectedRoute><AssetMovementPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/audit/system-access" element={<ProtectedRoute><SystemAccessPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/audit/change-management" element={<ProtectedRoute><UserManagementPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/audit/asset-scan" element={<ProtectedRoute><AssetScanLogsPage /></ProtectedRoute>} />
                  <Route path="/reports-analytics/manage-schedules" element={<ProtectedRoute><ManageSchedulesPage /></ProtectedRoute>} />

                  {/* Configuration Routes */}
                  <Route path="/configuration" element={<ConfigurationPage />} />
                  <Route path="/configuration/api" element={<APIConfigPage />} />
                  <Route path="/configuration/settings" element={<SettingsPage />} />
                  <Route path="/configuration/settings/election-type" element={<ElectionTypePage />} />
                  <Route path="/configuration/settings/asset-type" element={<AssetTypePage />} />
                  <Route path="/configuration/settings/asset-model" element={<AssetModelPage />} />
                  <Route path="/configuration/settings/asset-status" element={<AssetStatusPage />} />
                  <Route path="/configuration/settings/consumables-category" element={<ConsumablesCategoryPage />} />
                  <Route path="/configuration/settings/service-maintenance-type" element={<ServiceMaintenanceTypePage />} />
                  <Route path="/configuration/settings/other-cost-type" element={<OtherCostTypePage />} />
                  <Route path="/configuration/settings/packing-location" element={<PackingLocationPage />} />

                  {/* Existing Routes */}

                  <Route path="/counties" element={<CountiesPage />} />
                  <Route path="/map" element={<MapPage />} />

                  <Route path="/search" element={<SearchPage />} />
                  <Route path="/profile" element={<ProfilePage />} />

                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </SidebarProvider>
            </BrowserRouter>
          </TooltipProvider>
        </AuthContext.Provider>
      </AppProvider>
    </QueryClientProvider>
  );
}

export default App;
