"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("./config/database");
const errorHandler_1 = require("./middleware/errorHandler");
const rateLimiter_1 = require("./middleware/rateLimiter");
// Import models (this ensures they are registered with Sequelize)
const AssetType_1 = __importDefault(require("./models/AssetType"));
const Vendors_1 = __importDefault(require("./models/Vendors"));
const User_1 = __importDefault(require("./models/User"));
const Assets_1 = __importDefault(require("./models/Assets"));
const CageManagement_1 = require("./models/CageManagement");
const Maintenance_1 = __importDefault(require("./models/Maintenance"));
// Import routes
const auth_1 = __importDefault(require("./routes/auth"));
const assetTypes_1 = __importDefault(require("./routes/assetTypes"));
const Vendors_2 = __importDefault(require("./routes/Vendors"));
const assets_1 = __importDefault(require("./routes/assets"));
const cagemanagement_1 = __importDefault(require("./routes/cagemanagement"));
const maintenance_1 = __importDefault(require("./routes/maintenance"));
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 5000;
// Security middleware
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));
app.use((0, compression_1.default)());
// CORS configuration
const allowedOrigins = [
    'http://localhost:8080',
    'http://localhost:8081',
    'http://localhost:8082',
    'http://localhost:8083',
    'http://localhost:8084',
    'http://localhost:8085',
    'http://localhost:5173',
];
if (process.env.FRONTEND_URL) {
    allowedOrigins.push(process.env.FRONTEND_URL);
}
const corsOptions = {
    origin: allowedOrigins,
    credentials: true,
    optionsSuccessStatus: 200
};
app.use((0, cors_1.default)(corsOptions));
// Logging
if (process.env.NODE_ENV !== 'test') {
    app.use((0, morgan_1.default)('combined'));
}
// Rate limiting
app.use(rateLimiter_1.rateLimiter);
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        database: 'Connected'
    });
});
// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'RFI Asset Management API',
        version: '1.0.0',
        status: 'running'
    });
});
// API routes
app.use('/api/auth', auth_1.default);
app.use('/api/asset-types', assetTypes_1.default);
app.use('/api/vendors', Vendors_2.default);
app.use('/api/assets', assets_1.default);
app.use('/api/cage-management', cagemanagement_1.default);
app.use('/api/maintenance', maintenance_1.default);
// Error handling middleware
app.use(errorHandler_1.errorHandler);
// 404 handler - must be last
app.use((req, res, next) => {
    res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl
    });
});
// Database connection and server startup
async function startServer() {
    try {
        console.log('🔄 Starting server initialization...');
        // Test database connection only
        await database_1.sequelize.authenticate();
        console.log('✅ Database connection established successfully.');
        // Import models without syncing - tables should already exist
        console.log('🔄 Loading database models...');
        // Test if tables exist by trying a simple query
        try {
            await User_1.default.count();
            await AssetType_1.default.count();
            await Vendors_1.default.count();
            await Assets_1.default.count();
            await CageManagement_1.CageManagement.count();
            await Maintenance_1.default.count();
            console.log('✅ All database tables are accessible.');
        }
        catch (error) {
            console.error('❌ Database tables not found or inaccessible.');
            console.error('📝 Please run the table creation script first:');
            console.error('   npm run create-tables');
            console.error('');
            console.error('Error details:', error.message);
            process.exit(1);
        }
        // Start server
        const server = app.listen(PORT, () => {
            console.log(`🚀 Server running on port ${PORT}`);
            console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
            console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
            console.log(`📡 API Base URL: http://localhost:${PORT}/api`);
            console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
            console.log('');
            console.log('📋 Available API Endpoints:');
            console.log('  GET    /health - Health check');
            console.log('  POST   /api/auth/login - User login');
            console.log('  POST   /api/auth/register - User registration');
            console.log('  GET    /api/auth/profile - Get user profile');
            console.log('  GET    /api/asset-types - Get all asset types');
            console.log('  POST   /api/asset-types - Create asset type');
            console.log('  GET    /api/vendors - Get all vendors');
            console.log('  POST   /api/vendors - Create new vendor');
            console.log('  PUT    /api/vendors/:id - Update vendor');
            console.log('  DELETE /api/vendors/:id - Delete vendor');
            console.log('  GET    /api/assets - Get all assets');
            console.log('  POST   /api/assets - Create new asset');
            console.log('  GET    /api/assets/:id - Get asset by ID');
            console.log('  PUT    /api/assets/:id - Update asset');
            console.log('  DELETE /api/assets/:id - Delete asset');
            console.log('  GET    /api/cage-management - Get all cage records');
            console.log('  POST   /api/cage-management - Create new cage record');
            console.log('  PUT    /api/cage-management/:id - Update cage record');
            console.log('  DELETE /api/cage-management/:id - Delete cage record');
            console.log('  GET    /api/maintenance - Get all maintenance records');
            console.log('  POST   /api/maintenance - Create new maintenance record');
            console.log('  GET    /api/maintenance/:id - Get maintenance record by ID');
            console.log('  PUT    /api/maintenance/:id - Update maintenance record');
            console.log('  DELETE /api/maintenance/:id - Delete maintenance record');
            console.log('');
            console.log('🔗 Database Info:');
            console.log(`  Host: ${process.env.DB_HOST || '**************'}`);
            console.log(`  Database: ${process.env.DB_NAME || 'assetmanagementool'}`);
            console.log(`  User: ${process.env.DB_USERNAME || 'ram'}`);
            console.log('');
            console.log('✅ Server ready to accept connections!');
        });
        // Graceful shutdown handling
        process.on('SIGTERM', async () => {
            console.log('SIGTERM received, shutting down gracefully...');
            server.close(async () => {
                try {
                    await database_1.sequelize.close();
                    console.log('✅ Database connection closed.');
                    process.exit(0);
                }
                catch (error) {
                    console.error('❌ Error closing database connection:', error);
                    process.exit(1);
                }
            });
        });
        process.on('SIGINT', async () => {
            console.log('\nSIGINT received, shutting down gracefully...');
            server.close(async () => {
                try {
                    await database_1.sequelize.close();
                    console.log('✅ Database connection closed.');
                    process.exit(0);
                }
                catch (error) {
                    console.error('❌ Error closing database connection:', error);
                    process.exit(1);
                }
            });
        });
    }
    catch (error) {
        console.error('❌ Unable to start server:', error);
        if (error instanceof Error) {
            console.error('Error details:', {
                name: error.name,
                message: error.message
            });
        }
        console.log('');
        console.log('🔧 Troubleshooting Tips:');
        console.log('1. Check if MySQL is running');
        console.log('2. Verify database connection details in .env file');
        console.log('3. Ensure the database exists');
        console.log('4. Check if the database user has proper permissions');
        console.log('5. Run table creation script: npm run create-tables');
        process.exit(1);
    }
}
// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
    console.error('❌ Unhandled Promise Rejection:', err);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('❌ Uncaught Exception:', err);
    process.exit(1);
});
// Start the server
console.log('🚀 Starting RFI Asset Management API Server...');
console.log('⏰ Timestamp:', new Date().toISOString());
console.log('');
startServer().catch((error) => {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
});
