"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// src/scripts/create-tables.ts
const database_1 = require("../config/database");
async function createTables() {
    try {
        console.log('🔄 Starting database table creation...');
        const queryInterface = database_1.sequelize.getQueryInterface();
        // Check database connection
        await database_1.sequelize.authenticate();
        console.log('✅ Database connection established.');
        // Create Users table
        console.log('📝 Creating users table...');
        await queryInterface.createTable('users', {
            id: {
                type: 'VARCHAR(255)',
                primaryKey: true,
                allowNull: false
            },
            first_name: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            last_name: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            email: {
                type: 'VARCHAR(255)',
                allowNull: false,
                unique: true
            },
            password: {
                type: 'VARCHA<PERSON>(255)',
                allowNull: false
            },
            mobile: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            user_group: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            login_enabled: {
                type: 'BOOLEAN',
                defaultValue: true
            },
            login_id: {
                type: 'VARCHAR(255)',
                allowNull: false,
                unique: true
            },
            access_level: {
                type: "ENUM('county', 'state')",
                allowNull: false
            },
            county: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            status: {
                type: 'BOOLEAN',
                defaultValue: true
            },
            image: {
                type: 'TEXT',
                allowNull: true
            },
            company: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            employee_no: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            manager: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            department: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            location: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            address_line1: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            address_line2: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            city: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            state: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            pincode: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            country: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            role: {
                type: "ENUM('admin', 'manager', 'user', 'Portal Admin')",
                allowNull: false,
                defaultValue: 'user'
            },
            username: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            created_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
            }
        });
        console.log('✅ Users table created successfully.');
        // Create Asset Types table
        console.log('📝 Creating asset_types table...');
        await queryInterface.createTable('asset_types', {
            id: {
                type: 'VARCHAR(255)',
                primaryKey: true,
                allowNull: false
            },
            name: {
                type: 'VARCHAR(100)',
                allowNull: false,
                unique: true
            },
            status: {
                type: 'BOOLEAN',
                defaultValue: true
            },
            created_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
            }
        });
        console.log('✅ Asset types table created successfully.');
        // Create Vendors table
        console.log('📝 Creating vendors table...');
        await queryInterface.createTable('vendors', {
            id: {
                type: 'INTEGER',
                autoIncrement: true,
                primaryKey: true
            },
            company_name: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            title: {
                type: 'VARCHAR(10)',
                allowNull: true
            },
            first_name: {
                type: 'VARCHAR(100)',
                allowNull: false
            },
            last_name: {
                type: 'VARCHAR(100)',
                allowNull: false
            },
            phone: {
                type: 'VARCHAR(20)',
                allowNull: false
            },
            email: {
                type: 'VARCHAR(255)',
                allowNull: false,
                unique: true
            },
            status: {
                type: 'BOOLEAN',
                allowNull: false,
                defaultValue: true
            },
            secondary_contacts: {
                type: 'TEXT',
                allowNull: true
            },
            addresses: {
                type: 'TEXT',
                allowNull: true
            },
            created_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
            }
        });
        console.log('✅ Vendors table created successfully.');
        // Create Assets table
        console.log('📝 Creating assets table...');
        await queryInterface.createTable('assets', {
            id: {
                type: 'INT',
                primaryKey: true,
                autoIncrement: true,
                allowNull: false
            },
            asset_id: {
                type: 'VARCHAR(255)',
                allowNull: false,
                unique: true
            },
            name: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            description: {
                type: 'TEXT',
                allowNull: true
            },
            serial_number: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            category: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            location: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            status: {
                type: "ENUM('available', 'checked_out', 'in_maintenance', 'damaged', 'retired')",
                allowNull: false,
                defaultValue: 'available'
            },
            condition: {
                type: "ENUM('excellent', 'good', 'fair', 'poor', 'damaged')",
                allowNull: false,
                defaultValue: 'good'
            },
            purchase_date: {
                type: 'DATE',
                allowNull: true
            },
            purchase_price: {
                type: 'DECIMAL(10,2)',
                allowNull: true
            },
            vendor_id: {
                type: 'INT',
                allowNull: true,
                references: {
                    model: 'vendors',
                    key: 'id'
                }
            },
            assigned_to: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            last_maintenance_date: {
                type: 'DATE',
                allowNull: true
            },
            next_maintenance_date: {
                type: 'DATE',
                allowNull: true
            },
            notes: {
                type: 'TEXT',
                allowNull: true
            },
            created_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
            }
        });
        console.log('✅ Assets table created successfully.');
        // Create Maintenance table
        console.log('📝 Creating maintenance table...');
        await queryInterface.createTable('maintenance', {
            asset_tag: {
                type: 'VARCHAR(255)',
                primaryKey: true,
                allowNull: false
            },
            category: {
                type: "ENUM('AV computers', 'AV printers', 'tabs', 'others', 'scanners', 'pollpads', 'monitors')",
                allowNull: false
            },
            name: {
                type: 'VARCHAR(255)',
                allowNull: false
            },
            request_type: {
                type: "ENUM('preventive', 'corrective', 'emergency')",
                allowNull: false
            },
            priority: {
                type: "ENUM('high', 'low', 'medium', 'critical')",
                allowNull: false,
                defaultValue: 'medium'
            },
            assigned_technician: {
                type: 'VARCHAR(255)',
                allowNull: true
            },
            description: {
                type: 'TEXT',
                allowNull: true
            },
            scheduled_date: {
                type: 'DATETIME',
                allowNull: true
            },
            scheduled_frequency: {
                type: "ENUM('once', 'weekly', 'monthly', 'quarterly', 'half-yearly', 'yearly')",
                allowNull: true
            },
            estimated_hours: {
                type: 'DECIMAL(8,2)',
                allowNull: true
            },
            estimated_cost: {
                type: 'DECIMAL(10,2)',
                allowNull: true
            },
            notes: {
                type: 'TEXT',
                allowNull: true
            },
            status: {
                type: "ENUM('REQUESTED', 'SCHEDULED', 'IN_PROGRESS', 'TESTING', 'COMPLETED')",
                allowNull: false,
                defaultValue: 'REQUESTED'
            },
            created_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
            }
        });
        console.log('✅ Maintenance table created successfully.');
        // Create Cage Management table
        console.log('📝 Creating cage_management table...');
        await queryInterface.createTable('cage_management', {
            cage_id: {
                type: 'VARCHAR(50)',
                primaryKey: true,
                allowNull: false
            },
            cage_name: {
                type: 'VARCHAR(100)',
                allowNull: false
            },
            current_location: {
                type: 'VARCHAR(100)',
                allowNull: false
            },
            status: {
                type: "ENUM('Available', 'Checked Out', 'In Transit', 'Maintenance')",
                allowNull: false,
                defaultValue: 'Available'
            },
            capacity: {
                type: 'INTEGER',
                allowNull: false,
                defaultValue: 0
            },
            current_item_count: {
                type: 'INTEGER',
                allowNull: false,
                defaultValue: 0
            },
            last_updated: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            },
            assigned_to: {
                type: 'VARCHAR(100)',
                allowNull: true
            },
            precinct: {
                type: 'VARCHAR(100)',
                allowNull: true
            },
            cage_type: {
                type: 'VARCHAR(50)',
                allowNull: true
            },
            qr_code: {
                type: 'VARCHAR(255)',
                allowNull: true,
                unique: true
            },
            Item_id: {
                type: 'VARCHAR(100)',
                allowNull: true,
                unique: true
            },
            notes: {
                type: 'TEXT',
                allowNull: true
            },
            received_by: {
                type: 'VARCHAR(100)',
                allowNull: true
            },
            expected_return_date: {
                type: 'DATETIME',
                allowNull: true
            },
            created_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: 'DATETIME',
                allowNull: false,
                defaultValue: database_1.sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
            }
        });
        console.log('✅ Cage Management table created successfully.');
        // Create indexes for vendors table
        console.log('📝 Creating indexes for vendors table...');
        await queryInterface.addIndex('vendors', ['email'], {
            name: 'idx_vendor_email',
            unique: true
        });
        await queryInterface.addIndex('vendors', ['company_name'], {
            name: 'idx_vendor_company'
        });
        await queryInterface.addIndex('vendors', ['status'], {
            name: 'idx_vendor_status'
        });
        await queryInterface.addIndex('vendors', ['first_name', 'last_name'], {
            name: 'idx_vendor_name'
        });
        console.log('✅ Vendor indexes created successfully.');
        // Assets indexes
        try {
            await queryInterface.addIndex('assets', ['asset_id'], {
                name: 'idx_asset_id',
                unique: true
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_asset_id already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('assets', ['status'], {
                name: 'idx_asset_status'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_asset_status already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('assets', ['location'], {
                name: 'idx_asset_location'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_asset_location already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('assets', ['category'], {
                name: 'idx_asset_category'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_asset_category already exists, skipping...');
        }
        // Create indexes for maintenance table
        console.log('📝 Creating indexes for maintenance table...');
        try {
            await queryInterface.addIndex('maintenance', ['status'], {
                name: 'idx_maintenance_status'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_maintenance_status already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('maintenance', ['priority'], {
                name: 'idx_maintenance_priority'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_maintenance_priority already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('maintenance', ['scheduled_date'], {
                name: 'idx_maintenance_scheduled_date'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_maintenance_scheduled_date already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('maintenance', ['assigned_technician'], {
                name: 'idx_maintenance_assigned_technician'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_maintenance_assigned_technician already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('maintenance', ['category'], {
                name: 'idx_maintenance_category'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_maintenance_category already exists, skipping...');
        }
        try {
            await queryInterface.addIndex('maintenance', ['request_type'], {
                name: 'idx_maintenance_request_type'
            });
        }
        catch (error) {
            if (error.original?.code !== 'ER_DUP_KEYNAME') {
                throw error;
            }
            console.log('⚠️ Index idx_maintenance_request_type already exists, skipping...');
        }
        console.log('✅ Maintenance indexes created successfully.');
        // Create indexes for cage_management table
        console.log('📝 Creating indexes for cage_management table...');
        await queryInterface.addIndex('cage_management', ['cage_id'], {
            name: 'idx_cage_id',
            unique: false
        });
        await queryInterface.addIndex('cage_management', ['current_location'], {
            name: 'idx_cage_location'
        });
        await queryInterface.addIndex('cage_management', ['status'], {
            name: 'idx_cage_status'
        });
        await queryInterface.addIndex('cage_management', ['cage_type'], {
            name: 'idx_cage_type'
        });
        await queryInterface.addIndex('cage_management', ['assigned_to'], {
            name: 'idx_cage_assigned'
        });
        await queryInterface.addIndex('cage_management', ['precinct'], {
            name: 'idx_cage_precinct'
        });
        await queryInterface.addIndex('cage_management', ['qr_code'], {
            name: 'idx_cage_qr_code',
            unique: true
        });
        await queryInterface.addIndex('cage_management', ['Item_id'], {
            name: 'idx_cage_item_id',
            unique: true
        });
        console.log('✅ Cage Management indexes created successfully.');
        console.log('🎉 All tables created successfully!');
    }
    catch (error) {
        console.error('❌ Error creating tables:', error);
        throw error;
    }
    finally {
        await database_1.sequelize.close();
    }
}
// Script execution
if (require.main === module) {
    createTables()
        .then(() => {
        console.log('✅ Database setup completed successfully!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('❌ Database setup failed:', error);
        process.exit(1);
    });
}
exports.default = createTables;
