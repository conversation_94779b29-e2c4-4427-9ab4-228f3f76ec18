"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testTcpConnection = testTcpConnection;
exports.testMysqlConnection = testMysqlConnection;
exports.runNetworkTests = runNetworkTests;
const net_1 = __importDefault(require("net"));
const promise_1 = __importDefault(require("mysql2/promise"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const DB_HOST = process.env.DB_HOST || '**************';
const DB_PORT = parseInt(process.env.DB_PORT || '3306');
const DB_USER = process.env.DB_USERNAME || 'ram';
const DB_PASSWORD = process.env.DB_PASSWORD || '12345678890R@m';
const DB_NAME = process.env.DB_NAME || 'assetmanagementool';
// Test basic TCP connectivity
async function testTcpConnection() {
    return new Promise((resolve) => {
        console.log(`🔄 Testing TCP connection to ${DB_HOST}:${DB_PORT}...`);
        const socket = new net_1.default.Socket();
        const timeout = 10000; // 10 seconds
        socket.setTimeout(timeout);
        socket.on('connect', () => {
            console.log('✅ TCP connection successful!');
            socket.destroy();
            resolve(true);
        });
        socket.on('timeout', () => {
            console.log('❌ TCP connection timed out');
            socket.destroy();
            resolve(false);
        });
        socket.on('error', (error) => {
            console.log('❌ TCP connection failed:', error.message);
            socket.destroy();
            resolve(false);
        });
        socket.connect(DB_PORT, DB_HOST);
    });
}
// Test MySQL connection directly
async function testMysqlConnection() {
    try {
        console.log('🔄 Testing MySQL connection directly...');
        const connection = await promise_1.default.createConnection({
            host: DB_HOST,
            port: DB_PORT,
            user: DB_USER,
            password: DB_PASSWORD,
            database: DB_NAME,
            connectTimeout: 30000 // 30 seconds
        });
        console.log('✅ MySQL connection successful!');
        // Test a simple query
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log('✅ MySQL query test successful:', rows);
        await connection.end();
        return true;
    }
    catch (error) {
        console.log('❌ MySQL connection failed:', error);
        return false;
    }
}
// Test with alternative connection options
async function testAlternativeConnection() {
    try {
        console.log('🔄 Testing MySQL with alternative settings...');
        const connection = await promise_1.default.createConnection({
            host: DB_HOST,
            port: DB_PORT,
            user: DB_USER,
            password: DB_PASSWORD,
            database: DB_NAME,
            connectTimeout: 60000 // 60 seconds
        });
        console.log('✅ Alternative MySQL connection successful!');
        // Get server info
        const [rows] = await connection.execute('SELECT VERSION() as version, DATABASE() as db, USER() as user');
        console.log('📊 Server info:', rows);
        await connection.end();
        return true;
    }
    catch (error) {
        console.log('❌ Alternative MySQL connection failed:', error);
        return false;
    }
}
// Run all tests
async function runNetworkTests() {
    console.log('🚀 Starting network connectivity tests...');
    console.log('📍 Target: **************:3306');
    console.log('👤 User: sumanth');
    console.log('🗄️ Database: assetmanagementool');
    console.log('');
    // Test 1: TCP connectivity
    const tcpSuccess = await testTcpConnection();
    console.log('');
    if (!tcpSuccess) {
        console.log('❌ TCP connection failed. Possible issues:');
        console.log('   • Firewall blocking port 3306');
        console.log('   • MySQL server not running');
        console.log('   • Incorrect IP address');
        console.log('   • Network routing issues');
        return false;
    }
    // Test 2: MySQL connection
    const mysqlSuccess = await testMysqlConnection();
    console.log('');
    if (!mysqlSuccess) {
        console.log('⚠️ TCP works but MySQL connection failed. Trying alternatives...');
        console.log('');
        // Test 3: Alternative MySQL connection
        const altSuccess = await testAlternativeConnection();
        console.log('');
        if (!altSuccess) {
            console.log('❌ All MySQL connection attempts failed. Possible issues:');
            console.log('   • Incorrect username/password');
            console.log('   • Database does not exist');
            console.log('   • User lacks privileges');
            console.log('   • MySQL authentication issues');
            return false;
        }
    }
    console.log('🎉 All network tests passed!');
    console.log('💡 Your connection should work. Try the main test again.');
    return true;
}
// Show current system info
function showSystemInfo() {
    console.log('🖥️ System Information:');
    console.log('   Platform:', process.platform);
    console.log('   Node.js:', process.version);
    console.log('   Architecture:', process.arch);
    console.log('');
}
// Main execution
if (require.main === module) {
    (async () => {
        showSystemInfo();
        try {
            const success = await runNetworkTests();
            process.exit(success ? 0 : 1);
        }
        catch (error) {
            console.error('❌ Network test failed:', error);
            process.exit(1);
        }
    })();
}
