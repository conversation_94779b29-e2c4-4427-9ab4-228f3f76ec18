import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ConsumableCategory, ConsumableCategoryCreationData } from "@/services/consumablescategoryService";

interface ConsumableCategoryEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  consumableCategory: ConsumableCategory | null;
  onSave: (consumableCategory: ConsumableCategoryCreationData) => void;
  isEditMode: boolean;
}

export function ConsumableCategoryEditDialog({
  open,
  onOpenChange,
  consumableCategory,
  onSave,
  isEditMode
}: ConsumableCategoryEditDialogProps) {
  const [formData, setFormData] = useState<ConsumableCategoryCreationData>({
    name: "",
    status: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when dialog opens or data changes
  useEffect(() => {
    if (open) {
      if (consumableCategory) {
        setFormData({
          name: consumableCategory.name,
          status: consumableCategory.status
        });
      } else {
        // Reset form for new entry
        setFormData({
          name: "",
          status: true
        });
      }
      setErrors({});
    }
  }, [consumableCategory, open]);

  // Handle input change
  const handleInputChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: ""
      });
    }
  };

  // Handle status change
  const handleStatusChange = (checked: boolean) => {
    setFormData({
      ...formData,
      status: checked
    });
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Consumable Category is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Consumable Category must be at least 2 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSave({
        name: formData.name.trim(),
        status: formData.status
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-xl">
            {isEditMode ? "Edit Consumable Category" : "Add Consumable Category"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium flex items-center">
                Consumable Category <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter consumable category name"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-xs text-red-500">{errors.name}</p>}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="status" className="text-sm font-medium">Status</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="status"
                    checked={formData.status}
                    onCheckedChange={handleStatusChange}
                  />
                  <span className={`text-sm ${formData.status ? "text-green-600" : "text-red-600"}`}>
                    {formData.status ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="flex justify-end space-x-3 mt-6 pt-2 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              {isEditMode ? "Save Changes" : "Add Consumable Category"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}