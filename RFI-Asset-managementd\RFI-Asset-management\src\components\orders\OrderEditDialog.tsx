import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar as CalendarIcon, Plus, X } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, parse } from "date-fns";
import { cn } from "@/lib/utils";
import { Order as LocalOrder, OrderItem, OtherCost, getOtherCostTypes } from "@/utils/OrdersLocalStorage";

// API Order interface (from OrdersPage)
interface ApiOrder {
  id: number;
  refNo: string;
  date: string;
  vendor: string;
  consumable: string;
  qty: number;
  unitCost: number;
  total: number;
  status: string;
  locationName?: string;
  Location?: string;
  location?: string;
  items?: Array<{
    id: number;
    name: string;
    quantity: number;
    unitCost: number;
    amount: number;
  }>;
  otherCosts?: OtherCost[];
  subtotal?: number;
  createdBy?: string;
  modifiedBy?: string;
  note?: string;
}
import { getVendors } from "@/utils/vendorStorage";
import { getConsumables } from "@/utils/ConsumablesLocalStorage";

interface OrderEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order?: ApiOrder;
  onSave: (order: LocalOrder) => void;
}

// Convert API order to Local order format
const convertApiOrderToLocal = (apiOrder: ApiOrder): LocalOrder => {
  try {
    console.log("🔄 Converting API order to local format:", apiOrder);

    // Handle items - backend orders might have individual consumable/qty fields instead of items array
    let items: OrderItem[] = [];

    if (apiOrder.items && Array.isArray(apiOrder.items) && apiOrder.items.length > 0) {
      // If items array exists, use it
      items = apiOrder.items.map((item, index) => ({
        id: (item.id || index).toString(),
        itemId: (item.id || index).toString(),
        itemName: item.name || '',
        quantity: item.quantity || 0,
        unitCost: item.unitCost || 0,
        amount: item.amount || (item.quantity * item.unitCost) || 0
      }));
    } else if (apiOrder.consumable) {
      // If no items array but has consumable field, create a single item
      items = [{
        id: '1',
        itemId: '1',
        itemName: apiOrder.consumable,
        quantity: apiOrder.qty || 0,
        unitCost: apiOrder.unitCost || 0,
        amount: (apiOrder.qty || 0) * (apiOrder.unitCost || 0)
      }];
    }

    const convertedOrder: LocalOrder = {
      id: (apiOrder.id || 0).toString(),
      refNo: apiOrder.refNo || '',
      date: apiOrder.date || new Date().toISOString().split('T')[0],
      vendor: apiOrder.vendor || '',
      location: apiOrder.location || apiOrder.Location || apiOrder.locationName || '',
      note: apiOrder.note || '',
      items: items,
      otherCosts: Array.isArray(apiOrder.otherCosts) ? apiOrder.otherCosts : [],
      subtotal: typeof apiOrder.subtotal === 'number' ? apiOrder.subtotal :
                (items.reduce((sum, item) => sum + item.amount, 0)),
      total: typeof apiOrder.total === 'number' ? apiOrder.total :
             (items.reduce((sum, item) => sum + item.amount, 0)),
      status: apiOrder.status as any || 'Pending',
      createdBy: apiOrder.createdBy || 'Unknown',
      modifiedBy: apiOrder.modifiedBy || undefined
    };

    console.log("✅ Converted order:", convertedOrder);
    return convertedOrder;
  } catch (error) {
    console.error("❌ Error converting API order to local format:", error);
    // Return a minimal valid order
    return {
      id: '0',
      refNo: '',
      date: new Date().toISOString().split('T')[0],
      vendor: '',
      location: '',
      note: '',
      items: [],
      otherCosts: [],
      subtotal: 0,
      total: 0,
      status: 'Pending',
      createdBy: 'Unknown'
    };
  }
};

export function OrderEditDialog({
  open,
  onOpenChange,
  order,
  onSave
}: OrderEditDialogProps) {
  console.log("✏️ OrderEditDialog received props - open:", open, "order:", order);

  const [vendors, setVendors] = useState<any[]>([]);
  const [consumables, setConsumables] = useState<any[]>([]);
  const [locations, setLocations] = useState<string[]>([
    "Warehouse", "Precinct 001", "Cove Library", "Shilshole School", "Office"
  ]);
  const [otherCostTypes, setOtherCostTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [formData, setFormData] = useState<LocalOrder | null>(null);

  // Load data
  useEffect(() => {
    if (open && order) {
      try {
        console.log("🔧 OrderEditDialog: Loading data for order:", order);

        // Validate order object
        if (!order || typeof order !== 'object') {
          throw new Error("Invalid order object provided");
        }

        const vendorData = getVendors();
        console.log("🔧 OrderEditDialog: Loaded vendors:", vendorData);
        setVendors(vendorData);

        // Load consumables from backend API
        const loadConsumables = async () => {
          try {
            console.log("🔧 OrderEditDialog: Loading consumables from API...");
            const response = await fetch("http://localhost:5000/api/transaction-orders/config/consumable-categories");

            if (!response.ok) {
              throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log("🔧 OrderEditDialog: API response:", data);

            if (data.success && Array.isArray(data.data)) {
              console.log("🔧 OrderEditDialog: Loaded", data.data.length, "consumables from API");
              setConsumables(data.data);
            } else {
              throw new Error("Invalid API response format");
            }
          } catch (apiError) {
            console.warn("⚠️ Failed to load consumables from API:", apiError);
            // Fallback to localStorage
            const consumableData = getConsumables();
            console.log("🔧 OrderEditDialog: Fallback - loaded", consumableData.length, "consumables from localStorage");
            setConsumables(consumableData);
          }
        };

        loadConsumables();

        // Load locations from backend API
        const loadLocations = async () => {
          try {
            const response = await fetch("http://localhost:5000/api/transaction-orders/config/packing-locations");
            const data = await response.json();
            if (data.success && Array.isArray(data.data)) {
              console.log("🔧 OrderEditDialog: Loaded locations from API:", data.data);
              setLocations(data.data);
            } else {
              throw new Error("Invalid API response");
            }
          } catch (apiError) {
            console.warn("⚠️ Failed to load locations from API, using defaults:", apiError);
            // Keep default locations
          }
        };

        loadLocations();

        const costTypes = getOtherCostTypes();
        console.log("🔧 OrderEditDialog: Loaded cost types:", costTypes);
        setOtherCostTypes(costTypes);

        // Parse date string to Date object
        try {
          console.log("🔧 OrderEditDialog: Original date string:", order.date);
          let parsedDate: Date;

          if (!order.date) {
            parsedDate = new Date();
          } else if (typeof order.date === 'string') {
            // Try different date formats
            const dateStr = order.date.trim();

            // Try ISO format first (YYYY-MM-DD)
            if (dateStr.match(/^\d{4}-\d{2}-\d{2}/)) {
              parsedDate = new Date(dateStr);
            }
            // Try dd-MMM-yyyy format
            else if (dateStr.match(/^\d{1,2}-[A-Za-z]{3}-\d{4}/)) {
              parsedDate = parse(dateStr, "dd-MMM-yyyy", new Date());
            }
            // Try MM/dd/yyyy format
            else if (dateStr.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
              parsedDate = parse(dateStr, "MM/dd/yyyy", new Date());
            }
            // Try dd/MM/yyyy format
            else if (dateStr.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
              parsedDate = parse(dateStr, "dd/MM/yyyy", new Date());
            }
            // Fallback: try native Date parsing
            else {
              parsedDate = new Date(dateStr);
            }
          } else {
            parsedDate = new Date();
          }

          // Check if the parsed date is valid
          if (isNaN(parsedDate.getTime())) {
            console.warn("❌ Invalid date parsed, using current date");
            parsedDate = new Date();
          }

          setDate(parsedDate);
          console.log("🔧 OrderEditDialog: Parsed date:", parsedDate);
        } catch (error) {
          console.error("❌ Error parsing date:", error, "Original date:", order.date);
          setDate(new Date());
        }

        // Convert API order to local order format
        const formDataToSet = convertApiOrderToLocal(order);

        console.log("🔧 OrderEditDialog: Setting form data:", formDataToSet);
        setFormData(formDataToSet);
      } catch (error) {
        console.error("❌ Error loading data in OrderEditDialog:", error);
        // Set a minimal form data to prevent crashes
        const fallbackData = convertApiOrderToLocal(order);
        fallbackData.items = [];
        fallbackData.otherCosts = [];
        setFormData(fallbackData);
      }
    }
  }, [open, order]);

  // Safety checks
  if (!open) {
    return null;
  }

  if (!order) {
    console.log("🔧 OrderEditDialog: No order provided");
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-red-600">No order data provided for editing.</p>
          </div>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  if (!formData) {
    console.log("🔧 OrderEditDialog: No form data, returning loading state");
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Loading...</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Loading order data...</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Add error boundary for rendering
  try {

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setDate(date);
      handleInputChange("date", format(date, "dd-MMM-yyyy"));
    }
  };

  // Handle adding an item
  const handleAddItem = () => {
    const newItem: OrderItem = {
      id: `item${formData.items.length + 1}`,
      itemId: "",
      itemName: "",
      quantity: 0,
      unitCost: 0,
      amount: 0
    };
    
    const updatedItems = [...formData.items, newItem];
    setFormData({
      ...formData,
      items: updatedItems
    });
  };

  // Handle removing an item
  const handleRemoveItem = (itemId: string) => {
    const updatedItems = formData.items.filter(item => item.id !== itemId);
    
    // Recalculate subtotal
    const subtotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
    const otherCostsTotal = formData.otherCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      items: updatedItems,
      subtotal: subtotal,
      total: subtotal + otherCostsTotal
    });
  };

  // Handle item change
  const handleItemChange = (itemId: string, field: string, value: any) => {
    const updatedItems = formData.items.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };
        
        // If quantity or unitCost changed, recalculate amount
        if (field === "quantity" || field === "unitCost") {
          updatedItem.quantity = field === "quantity" ? Number(value) : updatedItem.quantity;
          updatedItem.unitCost = field === "unitCost" ? Number(value) : updatedItem.unitCost;
          updatedItem.amount = updatedItem.quantity * updatedItem.unitCost;
        }
        
        // If item selection changed, update item details
        if (field === "itemId") {
          console.log("🔧 Item selection changed to:", value);
          console.log("🔧 Available consumables:", consumables);

          const selectedItem = consumables.find(c =>
            c.id === value ||
            c.name === value ||
            String(c.id) === String(value) ||
            String(c.name) === String(value)
          );

          console.log("🔧 Selected consumable:", selectedItem);

          if (selectedItem) {
            updatedItem.itemName = selectedItem.name || selectedItem.id;
            updatedItem.itemId = String(selectedItem.id || selectedItem.name);
            console.log("🔧 Updated item:", updatedItem);
          }
        }
        
        return updatedItem;
      }
      return item;
    });
    
    // Recalculate subtotal
    const subtotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
    const otherCostsTotal = formData.otherCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      items: updatedItems,
      subtotal: subtotal,
      total: subtotal + otherCostsTotal
    });
  };

  // Handle adding other cost
  const handleAddOtherCost = () => {
    const newCost: OtherCost = {
      id: `cost${formData.otherCosts.length + 1}`,
      type: "",
      amount: 0
    };
    
    const updatedCosts = [...formData.otherCosts, newCost];
    setFormData({
      ...formData,
      otherCosts: updatedCosts
    });
  };

  // Handle removing other cost
  const handleRemoveOtherCost = (costId: string) => {
    const updatedCosts = formData.otherCosts.filter(cost => cost.id !== costId);
    
    // Recalculate total
    const otherCostsTotal = updatedCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      otherCosts: updatedCosts,
      total: formData.subtotal + otherCostsTotal
    });
  };

  // Handle other cost change
  const handleOtherCostChange = (costId: string, field: string, value: any) => {
    const updatedCosts = formData.otherCosts.map(cost => {
      if (cost.id === costId) {
        const updatedCost = { ...cost, [field]: value };
        
        // If amount changed, convert to number
        if (field === "amount") {
          updatedCost.amount = Number(value);
        }
        
        return updatedCost;
      }
      return cost;
    });
    
    // Recalculate total
    const otherCostsTotal = updatedCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      otherCosts: updatedCosts,
      total: formData.subtotal + otherCostsTotal
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate form
      if (!formData.refNo || !formData.date || !formData.vendor || !formData.location) {
        setError("Please fill in all required fields");
        return;
      }

      if (!formData.status) {
        setError("Please select a status");
        return;
      }

      if (formData.items.length === 0) {
        setError("Please add at least one item");
        return;
      }

      // Validate items
      for (const item of formData.items) {
        if (!item.itemName || item.quantity <= 0 || item.unitCost <= 0) {
          setError("Please ensure all items have valid name, quantity, and unit cost");
          return;
        }
      }

      // Convert back to API format for saving
      const apiOrderData = {
        id: parseInt(formData.id),
        refNo: formData.refNo,
        date: formData.date,
        vendor: formData.vendor,
        location: formData.location,
        note: formData.note,
        status: formData.status,
        // For single item orders, use the first item's data
        consumable: formData.items[0]?.itemName || '',
        qty: formData.items[0]?.quantity || 0,
        unitCost: formData.items[0]?.unitCost || 0,
        total: formData.total,
        subtotal: formData.subtotal,
        items: formData.items.map(item => ({
          id: parseInt(item.id),
          name: item.itemName,
          quantity: item.quantity,
          unitCost: item.unitCost,
          amount: item.amount
        })),
        otherCosts: formData.otherCosts,
        createdBy: formData.createdBy,
        modifiedBy: "Super Admin"
      };

      console.log("💾 Saving order data:", apiOrderData);

      // Save order
      onSave(apiOrderData as any);
      onOpenChange(false);
    } catch (err) {
      console.error("❌ Error saving order:", err);
      setError(err instanceof Error ? err.message : "Failed to save order");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Order</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="refNo">Ref No. / Invoice No.</Label>
              <Input
                id="refNo"
                value={formData.refNo}
                onChange={(e) => handleInputChange("refNo", e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="date">Date *</Label>
              <div className="flex mt-1">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendor">Vendor *</Label>
              <Select 
                value={formData.vendor} 
                onValueChange={(value) => handleInputChange("vendor", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.name}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="location">Stock Location *</Label>
              <Select 
                value={formData.location} 
                onValueChange={(value) => handleInputChange("location", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status">Status *</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={formData.note || ""}
                onChange={(e) => handleInputChange("note", e.target.value)}
                className="mt-1"
                rows={3}
              />
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <Label>Item</Label>
              <div className="flex items-center">
                <Label className="mr-8">Qty</Label>
                <Label className="mr-8">Unit Cost</Label>
                <Label className="mr-8">Amount</Label>
                <div className="w-8"></div>
              </div>
            </div>
            
            {formData.items.map((item) => (
              <div key={item.id} className="flex items-center mb-2">
                <Select
                  value={item.itemId || item.itemName}
                  onValueChange={(value) => handleItemChange(item.id, "itemId", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select an item" />
                  </SelectTrigger>
                  <SelectContent>
                    {consumables.length === 0 ? (
                      <SelectItem value="no-items" disabled>No consumables available</SelectItem>
                    ) : (
                      consumables.map((consumable) => (
                        <SelectItem key={consumable.id || consumable.name} value={consumable.name || consumable.id}>
                          {consumable.name || consumable.id} {consumable.itemNo ? `- Item No.${consumable.itemNo}` : ''}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                
                <Input
                  type="number"
                  value={item.quantity}
                  onChange={(e) => handleItemChange(item.id, "quantity", e.target.value)}
                  className="w-20 mx-2"
                  min="0"
                />
                
                <Input
                  type="number"
                  value={item.unitCost}
                  onChange={(e) => handleItemChange(item.id, "unitCost", e.target.value)}
                  className="w-24 mx-2"
                  min="0"
                  step="0.01"
                />
                
                <div className="w-24 mx-2 text-right">
                  {item.amount.toFixed(1)}
                </div>
                
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveItem(item.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddItem}
              className="mt-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
          
          <div className="flex justify-end mt-4">
            <div className="w-64">
              <div className="flex justify-between py-2">
                <span>SUBTOTAL</span>
                <span>{formData.subtotal.toFixed(1)}</span>
              </div>
              
              {formData.otherCosts.map((cost) => (
                <div key={cost.id} className="flex items-center py-2">
                  <Select 
                    value={cost.type} 
                    onValueChange={(value) => handleOtherCostChange(cost.id, "type", value)}
                  >
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {otherCostTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <X 
                    className="h-4 w-4 mx-1 cursor-pointer" 
                    onClick={() => handleRemoveOtherCost(cost.id)}
                  />
                  
                  <Input
                    type="number"
                    value={cost.amount}
                    onChange={(e) => handleOtherCostChange(cost.id, "amount", e.target.value)}
                    className="w-20 ml-2"
                    min="0"
                    step="0.01"
                  />
                </div>
              ))}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddOtherCost}
                className="mt-1"
              >
                <Plus className="h-4 w-4 mr-1" />
                Other Cost Type
              </Button>
              
              <div className="flex justify-between py-2 mt-2 font-bold border-t">
                <span>TOTAL</span>
                <span>{formData.total.toFixed(1)}</span>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? "Updating..." : "Update Order"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
  } catch (renderError) {
    console.error("❌ Error rendering OrderEditDialog:", renderError);
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-red-600">
              There was an error loading the edit dialog. Please try again.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Error: {renderError instanceof Error ? renderError.message : 'Unknown error'}
            </p>
          </div>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
}

export default OrderEditDialog;
