import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { getVendors } from "@/utils/vendorStorage";
import { getConsumables } from "@/utils/ConsumablesLocalStorage";

// Safe string conversion function
const safeString = (value: any): string => {
  if (value === null || value === undefined) return '';
  if (typeof value === 'string') return value;
  if (typeof value === 'number') return value.toString();
  if (typeof value === 'boolean') return value.toString();
  return String(value);
};

// Safe number conversion function
const safeNumber = (value: any): number => {
  if (value === null || value === undefined) return 0;
  if (typeof value === 'number') return isNaN(value) ? 0 : value;
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

// API Order interface
interface ApiOrder {
  id?: any;
  refNo?: any;
  date?: any;
  vendor?: any;
  consumable?: any;
  qty?: any;
  unitCost?: any;
  total?: any;
  status?: any;
  locationName?: any;
  Location?: any;
  location?: any;
  note?: any;
  [key: string]: any; // Allow any additional properties
}

interface OrderEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order?: ApiOrder | null;
  onSave: (order: any) => void;
}

export function OrderEditDialog({
  open,
  onOpenChange,
  order,
  onSave
}: OrderEditDialogProps) {
  console.log("✏️ OrderEditDialog - open:", open, "order:", order);

  // State
  const [vendors, setVendors] = useState<any[]>([]);
  const [consumables, setConsumables] = useState<any[]>([]);
  const [locations, setLocations] = useState<string[]>([
    "Warehouse", "Precinct 001", "Cove Library", "Shilshole School", "Office"
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form data state
  const [formData, setFormData] = useState({
    id: '',
    refNo: '',
    date: '',
    vendor: '',
    location: '',
    consumable: '',
    qty: 0,
    unitCost: 0,
    total: 0,
    status: 'pending',
    note: ''
  });

  // Load data when dialog opens
  useEffect(() => {
    if (open) {
      console.log("🔧 Loading data for dialog");

      // Load vendors
      const vendorData = getVendors();
      console.log("📦 Loaded vendors:", vendorData);
      setVendors(vendorData || []);

      // Load consumables
      const loadConsumables = async () => {
        try {
          const response = await fetch("http://localhost:5000/api/transaction-orders/config/consumable-categories");
          if (response.ok) {
            const data = await response.json();
            console.log("📦 API consumables response:", data);
            if (data.success && Array.isArray(data.data)) {
              // Store the full objects since we need both id and name
              console.log("📦 Processed consumables:", data.data);
              setConsumables(data.data);
            } else {
              throw new Error("Invalid API response");
            }
          } else {
            throw new Error("API request failed");
          }
        } catch (error) {
          console.warn("Failed to load consumables from API, using fallback:", error);
          const consumableData = getConsumables();
          console.log("📦 Fallback consumables:", consumableData);
          setConsumables(consumableData || []);
        }
      };

      loadConsumables();

      // Load locations
      const loadLocations = async () => {
        try {
          const response = await fetch("http://localhost:5000/api/transaction-orders/config/packing-locations");
          if (response.ok) {
            const data = await response.json();
            console.log("📍 API locations response:", data);
            if (data.success && Array.isArray(data.data)) {
              // Extract location names from the API response
              const locationNames = data.data.map((location: any) => location.name || location);
              console.log("📍 Processed location names:", locationNames);
              setLocations(locationNames);
            } else {
              throw new Error("Invalid API response");
            }
          } else {
            throw new Error("API request failed");
          }
        } catch (error) {
          console.warn("Failed to load locations from API, using defaults:", error);
          // Keep default locations if API fails
          setLocations([
            "Warehouse", "Precinct 001", "Cove Library", "Shilshole School", "Office"
          ]);
        }
      };

      loadLocations();
    }
  }, [open]);

  // Load order data when order changes
  useEffect(() => {
    if (open && order) {
      console.log("🔧 Loading order data:", order);

      // Set form data from order - ensure all values are properly converted
      setFormData({
        id: safeString(order.id),
        refNo: safeString(order.refNo),
        date: safeString(order.date),
        vendor: safeString(order.vendor),
        location: safeString(order.location || order.Location || order.locationName),
        consumable: safeString(order.consumable),
        qty: safeNumber(order.qty),
        unitCost: safeNumber(order.unitCost),
        total: safeNumber(order.total),
        status: safeString(order.status) || 'pending',
        note: safeString(order.note)
      });
    }
  }, [order, open]);

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };

      // Recalculate total when qty or unitCost changes
      if (field === 'qty' || field === 'unitCost') {
        const qty = field === 'qty' ? safeNumber(value) : safeNumber(updated.qty);
        const unitCost = field === 'unitCost' ? safeNumber(value) : safeNumber(updated.unitCost);
        updated.total = qty * unitCost;
      }

      return updated;
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate required fields
      const refNo = safeString(formData.refNo).trim();
      const vendor = safeString(formData.vendor).trim();
      const location = safeString(formData.location).trim();
      const consumable = safeString(formData.consumable).trim();

      if (!refNo || !vendor || !location || !consumable) {
        setError("Please fill in all required fields");
        return;
      }

      const qty = safeNumber(formData.qty);
      const unitCost = safeNumber(formData.unitCost);

      if (qty <= 0 || unitCost <= 0) {
        setError("Quantity and unit cost must be greater than 0");
        return;
      }

      // Convert to API format and save
      const orderToSave = {
        ...formData,
        id: safeNumber(formData.id) || 0,
        qty: qty,
        unitCost: unitCost,
        total: qty * unitCost
      };

      console.log("💾 Saving order:", orderToSave);
      onSave(orderToSave);
      onOpenChange(false);
    } catch (err) {
      console.error("❌ Error saving order:", err);
      setError("Failed to save order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (!open || !order) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Order</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        <div className="grid gap-4 py-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="refNo">Reference No. *</Label>
              <Input
                id="refNo"
                value={safeString(formData.refNo)}
                onChange={(e) => handleInputChange("refNo", e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={safeString(formData.date)}
                onChange={(e) => handleInputChange("date", e.target.value)}
                className="mt-1"
              />
            </div>
          </div>

          {/* Vendor and Location */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendor">Vendor *</Label>
              <Select
                value={safeString(formData.vendor)}
                onValueChange={(value) => handleInputChange("vendor", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.length === 0 ? (
                    <SelectItem value="no-vendors" disabled>
                      No vendors available
                    </SelectItem>
                  ) : (
                    vendors.map((vendor, index) => {
                      // Debug log for vendors
                      if (index === 0) {
                        console.log("🏪 First vendor structure:", vendor);
                        console.log("🏪 All vendors:", vendors);
                      }

                      const vendorName = safeString(vendor?.name || vendor);
                      const vendorId = safeString(vendor?.id || index);
                      return (
                        <SelectItem key={vendorId} value={vendorName}>
                          {vendorName}
                        </SelectItem>
                      );
                    })
                  )}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="location">Location *</Label>
              <Select
                value={safeString(formData.location)}
                onValueChange={(value) => handleInputChange("location", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  {locations.length === 0 ? (
                    <SelectItem value="no-locations" disabled>
                      No locations available
                    </SelectItem>
                  ) : (
                    locations.map((location, index) => {
                      // Debug log for locations
                      if (index === 0) {
                        console.log("📍 First location structure:", location);
                        console.log("📍 All locations:", locations);
                      }

                      const locationName = safeString(location);
                      return (
                        <SelectItem key={index} value={locationName}>
                          {locationName}
                        </SelectItem>
                      );
                    })
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Item Details */}
          <div>
            <Label htmlFor="consumable">Item/Consumable *</Label>
            <Select
              value={safeString(formData.consumable)}
              onValueChange={(value) => handleInputChange("consumable", value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select item" />
              </SelectTrigger>
              <SelectContent>
                {consumables.length === 0 ? (
                  <SelectItem value="no-consumables" disabled>
                    No consumables available
                  </SelectItem>
                ) : (
                  consumables.map((consumable, index) => {
                    // Debug log to see the actual structure
                    if (index === 0) {
                      console.log("🔍 First consumable structure:", consumable);
                    }

                    // API returns { id: number, name: string } format
                    const consumableName = safeString(consumable?.name || consumable);
                    const consumableKey = safeString(consumable?.id || index);

                    return (
                      <SelectItem key={consumableKey} value={consumableName}>
                        {consumableName}
                      </SelectItem>
                    );
                  })
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Quantity, Unit Cost, Total */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="qty">Quantity *</Label>
              <Input
                id="qty"
                type="number"
                value={safeString(formData.qty)}
                onChange={(e) => handleInputChange("qty", e.target.value)}
                className="mt-1"
                min="0"
              />
            </div>

            <div>
              <Label htmlFor="unitCost">Unit Cost *</Label>
              <Input
                id="unitCost"
                type="number"
                value={safeString(formData.unitCost)}
                onChange={(e) => handleInputChange("unitCost", e.target.value)}
                className="mt-1"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <Label htmlFor="total">Total</Label>
              <Input
                id="total"
                type="number"
                value={safeString(formData.total)}
                readOnly
                className="mt-1 bg-gray-50"
              />
            </div>
          </div>

          {/* Status and Note */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status">Status *</Label>
              <Select
                value={safeString(formData.status)}
                onValueChange={(value) => handleInputChange("status", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={safeString(formData.note)}
                onChange={(e) => handleInputChange("note", e.target.value)}
                className="mt-1"
                rows={3}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? "Updating..." : "Update Order"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default OrderEditDialog;