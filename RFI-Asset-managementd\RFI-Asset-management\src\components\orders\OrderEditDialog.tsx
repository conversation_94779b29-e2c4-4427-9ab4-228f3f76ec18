import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { getVendors } from "@/utils/vendorStorage";
import { getConsumables } from "@/utils/ConsumablesLocalStorage";

// API Order interface
interface ApiOrder {
  id: number;
  refNo: string;
  date: string;
  vendor: string;
  consumable: string;
  qty: number;
  unitCost: number;
  total: number;
  status: string;
  locationName?: string;
  Location?: string;
  location?: string;
  note?: string;
}

interface OrderEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order?: ApiOrder;
  onSave: (order: any) => void;
}

export function OrderEditDialog({
  open,
  onOpenChange,
  order,
  onSave
}: OrderEditDialogProps) {
  console.log("✏️ OrderEditDialog - open:", open, "order:", order);

  // State
  const [vendors, setVendors] = useState<any[]>([]);
  const [consumables, setConsumables] = useState<any[]>([]);
  const [locations, setLocations] = useState<string[]>([
    "Warehouse", "Precinct 001", "Cove Library", "Shilshole School", "Office"
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form data state
  const [formData, setFormData] = useState({
    id: '',
    refNo: '',
    date: '',
    vendor: '',
    location: '',
    consumable: '',
    qty: 0,
    unitCost: 0,
    total: 0,
    status: 'pending',
    note: ''
  });

  // Load data when dialog opens
  useEffect(() => {
    if (open && order) {
      console.log("🔧 Loading data for order:", order);

      // Load vendors
      const vendorData = getVendors();
      setVendors(vendorData);

      // Load consumables
      const loadConsumables = async () => {
        try {
          const response = await fetch("http://localhost:5000/api/transaction-orders/config/consumable-categories");
          if (response.ok) {
            const data = await response.json();
            if (data.success && Array.isArray(data.data)) {
              setConsumables(data.data);
            }
          }
        } catch (error) {
          console.warn("Failed to load consumables from API, using fallback");
          const consumableData = getConsumables();
          setConsumables(consumableData);
        }
      };

      loadConsumables();

      // Load locations
      const loadLocations = async () => {
        try {
          const response = await fetch("http://localhost:5000/api/transaction-orders/config/packing-locations");
          if (response.ok) {
            const data = await response.json();
            if (data.success && Array.isArray(data.data)) {
              setLocations(data.data);
            }
          }
        } catch (error) {
          console.warn("Failed to load locations from API, using defaults");
        }
      };

      loadLocations();

      // Set form data from order
      setFormData({
        id: order.id?.toString() || '',
        refNo: order.refNo || '',
        date: order.date || '',
        vendor: order.vendor || '',
        location: order.location || order.Location || order.locationName || '',
        consumable: order.consumable || '',
        qty: order.qty || 0,
        unitCost: order.unitCost || 0,
        total: order.total || 0,
        status: order.status || 'pending',
        note: order.note || ''
      });
    }
  }, [open, order]);

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };

      // Recalculate total when qty or unitCost changes
      if (field === 'qty' || field === 'unitCost') {
        updated.total = updated.qty * updated.unitCost;
      }

      return updated;
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate required fields
      if (!formData.refNo || !formData.vendor || !formData.location || !formData.consumable) {
        setError("Please fill in all required fields");
        return;
      }

      if (formData.qty <= 0 || formData.unitCost <= 0) {
        setError("Quantity and unit cost must be greater than 0");
        return;
      }

      // Convert to API format and save
      const orderToSave = {
        ...formData,
        id: parseInt(formData.id) || 0,
        qty: Number(formData.qty),
        unitCost: Number(formData.unitCost),
        total: Number(formData.total)
      };

      console.log("💾 Saving order:", orderToSave);
      onSave(orderToSave);
      onOpenChange(false);
    } catch (err) {
      console.error("❌ Error saving order:", err);
      setError("Failed to save order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Order</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        <div className="grid gap-4 py-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="refNo">Reference No. *</Label>
              <Input
                id="refNo"
                value={formData.refNo}
                onChange={(e) => handleInputChange("refNo", e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange("date", e.target.value)}
                className="mt-1"
              />
            </div>
          </div>

          {/* Vendor and Location */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendor">Vendor *</Label>
              <Select
                value={formData.vendor}
                onValueChange={(value) => handleInputChange("vendor", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.name}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="location">Location *</Label>
              <Select
                value={formData.location}
                onValueChange={(value) => handleInputChange("location", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Item Details */}
          <div>
            <Label htmlFor="consumable">Item/Consumable *</Label>
            <Select
              value={formData.consumable}
              onValueChange={(value) => handleInputChange("consumable", value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select item" />
              </SelectTrigger>
              <SelectContent>
                {consumables.map((consumable) => (
                  <SelectItem key={consumable.id || consumable.name} value={consumable.name || consumable.id}>
                    {consumable.name || consumable.id}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Quantity, Unit Cost, Total */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="qty">Quantity *</Label>
              <Input
                id="qty"
                type="number"
                value={formData.qty}
                onChange={(e) => handleInputChange("qty", Number(e.target.value))}
                className="mt-1"
                min="0"
              />
            </div>

            <div>
              <Label htmlFor="unitCost">Unit Cost *</Label>
              <Input
                id="unitCost"
                type="number"
                value={formData.unitCost}
                onChange={(e) => handleInputChange("unitCost", Number(e.target.value))}
                className="mt-1"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <Label htmlFor="total">Total</Label>
              <Input
                id="total"
                type="number"
                value={formData.total}
                readOnly
                className="mt-1 bg-gray-50"
              />
            </div>
          </div>

          {/* Status and Note */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status">Status *</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={formData.note}
                onChange={(e) => handleInputChange("note", e.target.value)}
                className="mt-1"
                rows={3}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? "Updating..." : "Update Order"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default OrderEditDialog;