import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar as CalendarIcon, Plus, X } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, parse } from "date-fns";
import { cn } from "@/lib/utils";
import { Order, OrderItem, OtherCost, getOtherCostTypes } from "@/utils/OrdersLocalStorage";
import { getVendors } from "@/utils/vendorStorage";
import { getConsumables } from "@/utils/ConsumablesLocalStorage";

interface OrderEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order?: Order;
  onSave: (order: Order) => void;
}

export function OrderEditDialog({
  open,
  onOpenChange,
  order,
  onSave
}: OrderEditDialogProps) {
  const [vendors, setVendors] = useState<any[]>([]);
  const [consumables, setConsumables] = useState<any[]>([]);
  const [locations, setLocations] = useState<string[]>([
    "Warehouse", "Precinct 001", "Cove Library", "Shilshole School", "Office"
  ]);
  const [otherCostTypes, setOtherCostTypes] = useState<string[]>([]);
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [formData, setFormData] = useState<Order | null>(null);

  // Load data
  useEffect(() => {
    if (open && order) {
      try {
        const vendorData = getVendors();
        setVendors(vendorData);
        
        const consumableData = getConsumables();
        setConsumables(consumableData);
        
        const costTypes = getOtherCostTypes();
        setOtherCostTypes(costTypes);
        
        // Parse date string to Date object
        try {
          const parsedDate = parse(order.date, "dd-MMM-yyyy", new Date());
          setDate(parsedDate);
        } catch (error) {
          console.error("Error parsing date:", error);
          setDate(new Date());
        }
        
        // Defensive defaults for all required fields
        setFormData({
          ...order,
          items: Array.isArray(order.items) ? order.items : [],
          otherCosts: Array.isArray(order.otherCosts) ? order.otherCosts : [],
          subtotal: typeof order.subtotal === 'number' ? order.subtotal : 0,
          total: typeof order.total === 'number' ? order.total : 0,
          location: order.location || order.Location || order.locationName || '',
          note: order.note || '',
        });
      } catch (error) {
        console.error("Error loading data:", error);
      }
    }
  }, [open, order]);

  if (!formData) {
    return null;
  }

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setDate(date);
      handleInputChange("date", format(date, "dd-MMM-yyyy"));
    }
  };

  // Handle adding an item
  const handleAddItem = () => {
    const newItem: OrderItem = {
      id: `item${formData.items.length + 1}`,
      itemId: "",
      itemName: "",
      quantity: 0,
      unitCost: 0,
      amount: 0
    };
    
    const updatedItems = [...formData.items, newItem];
    setFormData({
      ...formData,
      items: updatedItems
    });
  };

  // Handle removing an item
  const handleRemoveItem = (itemId: string) => {
    const updatedItems = formData.items.filter(item => item.id !== itemId);
    
    // Recalculate subtotal
    const subtotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
    const otherCostsTotal = formData.otherCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      items: updatedItems,
      subtotal: subtotal,
      total: subtotal + otherCostsTotal
    });
  };

  // Handle item change
  const handleItemChange = (itemId: string, field: string, value: any) => {
    const updatedItems = formData.items.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };
        
        // If quantity or unitCost changed, recalculate amount
        if (field === "quantity" || field === "unitCost") {
          updatedItem.quantity = field === "quantity" ? Number(value) : updatedItem.quantity;
          updatedItem.unitCost = field === "unitCost" ? Number(value) : updatedItem.unitCost;
          updatedItem.amount = updatedItem.quantity * updatedItem.unitCost;
        }
        
        // If item selection changed, update item details
        if (field === "itemId") {
          const selectedItem = consumables.find(c => c.id === value);
          if (selectedItem) {
            updatedItem.itemName = selectedItem.name;
          }
        }
        
        return updatedItem;
      }
      return item;
    });
    
    // Recalculate subtotal
    const subtotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
    const otherCostsTotal = formData.otherCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      items: updatedItems,
      subtotal: subtotal,
      total: subtotal + otherCostsTotal
    });
  };

  // Handle adding other cost
  const handleAddOtherCost = () => {
    const newCost: OtherCost = {
      id: `cost${formData.otherCosts.length + 1}`,
      type: "",
      amount: 0
    };
    
    const updatedCosts = [...formData.otherCosts, newCost];
    setFormData({
      ...formData,
      otherCosts: updatedCosts
    });
  };

  // Handle removing other cost
  const handleRemoveOtherCost = (costId: string) => {
    const updatedCosts = formData.otherCosts.filter(cost => cost.id !== costId);
    
    // Recalculate total
    const otherCostsTotal = updatedCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      otherCosts: updatedCosts,
      total: formData.subtotal + otherCostsTotal
    });
  };

  // Handle other cost change
  const handleOtherCostChange = (costId: string, field: string, value: any) => {
    const updatedCosts = formData.otherCosts.map(cost => {
      if (cost.id === costId) {
        const updatedCost = { ...cost, [field]: value };
        
        // If amount changed, convert to number
        if (field === "amount") {
          updatedCost.amount = Number(value);
        }
        
        return updatedCost;
      }
      return cost;
    });
    
    // Recalculate total
    const otherCostsTotal = updatedCosts.reduce((sum, cost) => sum + cost.amount, 0);
    
    setFormData({
      ...formData,
      otherCosts: updatedCosts,
      total: formData.subtotal + otherCostsTotal
    });
  };

  // Handle form submission
  const handleSubmit = () => {
    // Validate form
    if (!formData.refNo || !formData.date || !formData.vendor || !formData.location) {
      alert("Please fill in all required fields");
      return;
    }
    
    if (formData.items.length === 0) {
      alert("Please add at least one item");
      return;
    }
    
    // Update modifiedBy
    const updatedOrder = {
      ...formData,
      modifiedBy: "Super Admin"
    };
    
    // Save order
    onSave(updatedOrder);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Order</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="refNo">Ref No. / Invoice No.</Label>
              <Input
                id="refNo"
                value={formData.refNo}
                onChange={(e) => handleInputChange("refNo", e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="date">Date *</Label>
              <div className="flex mt-1">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vendor">Vendor *</Label>
              <Select 
                value={formData.vendor} 
                onValueChange={(value) => handleInputChange("vendor", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.name}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="location">Stock Location *</Label>
              <Select 
                value={formData.location} 
                onValueChange={(value) => handleInputChange("location", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="note">Note</Label>
            <Textarea
              id="note"
              value={formData.note || ""}
              onChange={(e) => handleInputChange("note", e.target.value)}
              className="mt-1"
              rows={3}
            />
          </div>
          
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <Label>Item</Label>
              <div className="flex items-center">
                <Label className="mr-8">Qty</Label>
                <Label className="mr-8">Unit Cost</Label>
                <Label className="mr-8">Amount</Label>
                <div className="w-8"></div>
              </div>
            </div>
            
            {formData.items.map((item) => (
              <div key={item.id} className="flex items-center mb-2">
                <Select 
                  value={item.itemId} 
                  onValueChange={(value) => handleItemChange(item.id, "itemId", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select an item" />
                  </SelectTrigger>
                  <SelectContent>
                    {consumables.map((consumable) => (
                      <SelectItem key={consumable.id} value={consumable.id}>
                        {consumable.id} {consumable.name} - Item No.{consumable.itemNo}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Input
                  type="number"
                  value={item.quantity}
                  onChange={(e) => handleItemChange(item.id, "quantity", e.target.value)}
                  className="w-20 mx-2"
                  min="0"
                />
                
                <Input
                  type="number"
                  value={item.unitCost}
                  onChange={(e) => handleItemChange(item.id, "unitCost", e.target.value)}
                  className="w-24 mx-2"
                  min="0"
                  step="0.01"
                />
                
                <div className="w-24 mx-2 text-right">
                  {item.amount.toFixed(1)}
                </div>
                
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveItem(item.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddItem}
              className="mt-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
          
          <div className="flex justify-end mt-4">
            <div className="w-64">
              <div className="flex justify-between py-2">
                <span>SUBTOTAL</span>
                <span>{formData.subtotal.toFixed(1)}</span>
              </div>
              
              {formData.otherCosts.map((cost) => (
                <div key={cost.id} className="flex items-center py-2">
                  <Select 
                    value={cost.type} 
                    onValueChange={(value) => handleOtherCostChange(cost.id, "type", value)}
                  >
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {otherCostTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <X 
                    className="h-4 w-4 mx-1 cursor-pointer" 
                    onClick={() => handleRemoveOtherCost(cost.id)}
                  />
                  
                  <Input
                    type="number"
                    value={cost.amount}
                    onChange={(e) => handleOtherCostChange(cost.id, "amount", e.target.value)}
                    className="w-20 ml-2"
                    min="0"
                    step="0.01"
                  />
                </div>
              ))}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddOtherCost}
                className="mt-1"
              >
                <Plus className="h-4 w-4 mr-1" />
                Other Cost Type
              </Button>
              
              <div className="flex justify-between py-2 mt-2 font-bold border-t">
                <span>TOTAL</span>
                <span>{formData.total.toFixed(1)}</span>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Update Order</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
