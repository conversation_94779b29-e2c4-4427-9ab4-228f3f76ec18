import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Plus } from "lucide-react";
import { Link } from "react-router-dom";

// Simple Order interface
interface SimpleOrder {
  id: string;
  refNo: string;
  vendor: string;
  date: string;
  location: string;
  status: string;
  total: number;
}

// Simple API call
const apiCall = async (endpoint: string) => {
  try {
    const response = await fetch(`http://localhost:5000/api${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};

export default function OrdersPageSimple() {
  const [orders, setOrders] = useState<SimpleOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load orders
  const loadOrders = async () => {
    try {
      console.log("🚀 Loading orders...");
      setLoading(true);
      setError(null);
      
      const response = await apiCall('/transaction-orders');
      console.log("📡 Response:", response);
      
      if (response.success && response.data?.orders) {
        setOrders(response.data.orders);
      } else {
        setOrders([]);
      }
    } catch (err) {
      console.error('❌ Error:', err);
      setError(err instanceof Error ? err.message : 'Failed to load orders');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadOrders();
  }, []);

  if (loading) {
    return (
      <AppLayout>
        <div className="p-4">
          <h1>Loading Orders...</h1>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="p-4">
          <h1>Error: {error}</h1>
          <Button onClick={loadOrders}>Retry</Button>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-full p-4">
        <div className="mb-4">
          <Link to="/transactions/consumables">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Consumable Transactions
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Purchase Orders ({orders.length})
              <Button size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add Order
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {orders.length === 0 ? (
              <p>No orders found</p>
            ) : (
              <div className="space-y-2">
                {orders.map((order) => (
                  <div key={order.id} className="border p-3 rounded">
                    <div className="flex justify-between items-center">
                      <div>
                        <strong>{order.refNo}</strong> - {order.vendor}
                      </div>
                      <div>
                        {order.status} - ${order.total}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      {order.date} - {order.location}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="mt-4 p-2 bg-gray-100 rounded text-sm">
          Debug: {orders.length} orders loaded
        </div>
      </div>
    </AppLayout>
  );
}
