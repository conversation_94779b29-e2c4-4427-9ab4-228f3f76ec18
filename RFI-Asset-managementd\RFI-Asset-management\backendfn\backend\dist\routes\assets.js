"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Assets_1 = __importDefault(require("../models/Assets"));
const sequelize_1 = require("sequelize");
const router = (0, express_1.Router)();
// Helper function to generate unique asset ID
const generateAssetId = async (type) => {
    const typePrefix = type.toUpperCase().substring(0, 3);
    const count = await Assets_1.default.count({ where: { type } });
    const nextNumber = (count + 1).toString().padStart(3, '0');
    return `${typePrefix}-${nextNumber}`;
};
// GET /api/assets - Get all assets with optional filtering
router.get('/', async (req, res) => {
    try {
        const { page = 1, limit = 50, search, type, status, condition, location, county, precinct } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const whereClause = {};
        // Add filters
        if (search) {
            whereClause[sequelize_1.Op.or] = [
                { assetId: { [sequelize_1.Op.iLike]: `%${search}%` } },
                { type: { [sequelize_1.Op.iLike]: `%${search}%` } },
                { model: { [sequelize_1.Op.iLike]: `%${search}%` } },
                { serialNumber: { [sequelize_1.Op.iLike]: `%${search}%` } },
                { location: { [sequelize_1.Op.iLike]: `%${search}%` } }
            ];
        }
        if (type)
            whereClause.type = type;
        if (status)
            whereClause.status = status;
        if (condition)
            whereClause.condition = condition;
        if (location)
            whereClause.location = { [sequelize_1.Op.iLike]: `%${location}%` };
        if (county)
            whereClause.county = county;
        if (precinct)
            whereClause.precinct = precinct;
        const { count, rows } = await Assets_1.default.findAndCountAll({
            where: whereClause,
            limit: Number(limit),
            offset,
            order: [['createdAt', 'DESC']]
        });
        res.json({
            assets: rows,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: count,
                pages: Math.ceil(count / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Error fetching assets:', error);
        res.status(500).json({ error: 'Failed to fetch assets' });
    }
});
// GET /api/assets/stats - Get asset statistics
router.get('/stats', async (req, res) => {
    try {
        const totalAssets = await Assets_1.default.count();
        const availableAssets = await Assets_1.default.count({ where: { status: 'Available' } });
        const inTransitAssets = await Assets_1.default.count({ where: { status: 'In Transit' } });
        const deployedAssets = await Assets_1.default.count({ where: { status: 'Deployed' } });
        const damagedAssets = await Assets_1.default.count({ where: { status: 'Damaged' } });
        const underRepairAssets = await Assets_1.default.count({ where: { status: 'Under Repair' } });
        // Asset types breakdown
        const assetTypes = await Assets_1.default.findAll({
            attributes: [
                'type',
                [Assets_1.default.sequelize.fn('COUNT', Assets_1.default.sequelize.col('id')), 'count']
            ],
            group: ['type'],
            raw: true
        });
        // Condition breakdown
        const conditionStats = await Assets_1.default.findAll({
            attributes: [
                'condition',
                [Assets_1.default.sequelize.fn('COUNT', Assets_1.default.sequelize.col('id')), 'count']
            ],
            group: ['condition'],
            raw: true
        });
        res.json({
            total: totalAssets,
            available: availableAssets,
            inTransit: inTransitAssets,
            deployed: deployedAssets,
            damaged: damagedAssets,
            underRepair: underRepairAssets,
            assetTypes,
            conditionStats
        });
    }
    catch (error) {
        console.error('Error fetching asset stats:', error);
        res.status(500).json({ error: 'Failed to fetch asset statistics' });
    }
});
// GET /api/assets/:id - Get specific asset
router.get('/:id', async (req, res) => {
    try {
        const asset = await Assets_1.default.findByPk(req.params.id);
        if (!asset) {
            res.status(404).json({ error: 'Asset not found' });
            return;
        }
        res.json(asset);
    }
    catch (error) {
        console.error('Error fetching asset:', error);
        res.status(500).json({ error: 'Failed to fetch asset' });
    }
});
// GET /api/assets/by-asset-id/:assetId - Get asset by assetId
router.get('/by-asset-id/:assetId', async (req, res) => {
    try {
        const asset = await Assets_1.default.findOne({ where: { assetId: req.params.assetId } });
        if (!asset) {
            res.status(404).json({ error: 'Asset not found' });
            return;
        }
        res.json(asset);
    }
    catch (error) {
        console.error('Error fetching asset:', error);
        res.status(500).json({ error: 'Failed to fetch asset' });
    }
});
// POST /api/assets - Create new asset
router.post('/', async (req, res) => {
    try {
        const assetData = req.body;
        // Generate asset ID if not provided
        if (!assetData.assetId) {
            assetData.assetId = await generateAssetId(assetData.type);
        }
        // Check if asset ID already exists
        const existingAsset = await Assets_1.default.findOne({ where: { assetId: assetData.assetId } });
        if (existingAsset) {
            res.status(400).json({ error: 'Asset ID already exists' });
            return;
        }
        const asset = await Assets_1.default.create(assetData);
        res.status(201).json(asset);
    }
    catch (error) {
        console.error('Error creating asset:', error);
        res.status(500).json({ error: 'Failed to create asset' });
    }
});
// PUT /api/assets/:id - Update asset
router.put('/:id', async (req, res) => {
    try {
        const asset = await Assets_1.default.findByPk(req.params.id);
        if (!asset) {
            res.status(404).json({ error: 'Asset not found' });
            return;
        }
        // Check if assetId is being changed and if it already exists
        if (req.body.assetId && req.body.assetId !== asset.assetId) {
            const existingAsset = await Assets_1.default.findOne({ where: { assetId: req.body.assetId } });
            if (existingAsset) {
                res.status(400).json({ error: 'Asset ID already exists' });
                return;
            }
        }
        await asset.update(req.body);
        res.json(asset);
    }
    catch (error) {
        console.error('Error updating asset:', error);
        res.status(500).json({ error: 'Failed to update asset' });
    }
});
// DELETE /api/assets/:id - Delete asset
router.delete('/:id', async (req, res) => {
    try {
        const asset = await Assets_1.default.findByPk(req.params.id);
        if (!asset) {
            res.status(404).json({ error: 'Asset not found' });
            return;
        }
        await asset.destroy();
        res.json({ message: 'Asset deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting asset:', error);
        res.status(500).json({ error: 'Failed to delete asset' });
    }
});
// PUT /api/assets/:id/status - Update asset status
router.put('/:id/status', async (req, res) => {
    try {
        const { status, location, assignedTo, notes } = req.body;
        const asset = await Assets_1.default.findByPk(req.params.id);
        if (!asset) {
            res.status(404).json({ error: 'Asset not found' });
            return;
        }
        const updateData = { status, lastChecked: new Date() };
        if (location)
            updateData.location = location;
        if (assignedTo !== undefined)
            updateData.assignedTo = assignedTo;
        if (notes !== undefined)
            updateData.notes = notes;
        await asset.update(updateData);
        res.json(asset);
    }
    catch (error) {
        console.error('Error updating asset status:', error);
        res.status(500).json({ error: 'Failed to update asset status' });
    }
});
// PUT /api/assets/:id/condition - Update asset condition
router.put('/:id/condition', async (req, res) => {
    try {
        const { condition, notes } = req.body;
        const asset = await Assets_1.default.findByPk(req.params.id);
        if (!asset) {
            res.status(404).json({ error: 'Asset not found' });
            return;
        }
        await asset.update({
            condition,
            notes: notes || asset.notes,
            lastChecked: new Date()
        });
        res.json(asset);
    }
    catch (error) {
        console.error('Error updating asset condition:', error);
        res.status(500).json({ error: 'Failed to update asset condition' });
    }
});
// GET /api/assets/locations/list - Get unique locations
router.get('/locations/list', async (req, res) => {
    try {
        const locations = await Assets_1.default.findAll({
            attributes: ['location'],
            group: ['location'],
            order: [['location', 'ASC']],
            raw: true
        });
        res.json(locations.map(l => l.location));
    }
    catch (error) {
        console.error('Error fetching locations:', error);
        res.status(500).json({ error: 'Failed to fetch locations' });
    }
});
// GET /api/assets/types/list - Get unique asset types
router.get('/types/list', async (req, res) => {
    try {
        const types = await Assets_1.default.findAll({
            attributes: ['type'],
            group: ['type'],
            order: [['type', 'ASC']],
            raw: true
        });
        res.json(types.map(t => t.type));
    }
    catch (error) {
        console.error('Error fetching asset types:', error);
        res.status(500).json({ error: 'Failed to fetch asset types' });
    }
});
exports.default = router;
