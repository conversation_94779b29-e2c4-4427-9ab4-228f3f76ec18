/*!
 * jScrollPane - v2.0.23 - 2016-01-28
 * http://jscrollpane.kelvinluck.com/
 *
 * Copyright (c) 2014 Ke<PERSON>
 * Dual licensed under the MIT or GPL licenses.
 */
!(function (a) {
    "function" == typeof define && define.amd
        ? define(["jquery"], a)
        : "object" == typeof exports
        ? (module.exports = a(require("jquery")))
        : a(jQuery);
})(function (a) {
    (a.fn.jScrollPane = function (b) {
        function c(b, c) {
            function d(c) {
                var f,
                    h,
                    j,
                    k,
                    l,
                    o,
                    p = !1,
                    q = !1;
                if (((N = c), void 0 === O))
                    (l = b.scrollTop()),
                        (o = b.scrollLeft()),
                        b.css({ overflow: "hidden", padding: 0 }),
                        (P = b.innerWidth() + rb),
                        (Q = b.innerHeight()),
                        b.width(P),
                        (O = a('<div class="jspPane" />').css("padding", qb).append(b.children())),
                        (R = a('<div class="jspContainer" />')
                            .css({ width: P + "px", height: Q + "px" })
                            .append(O)
                            .appendTo(b));
                else {
                    if (
                        (b.css("width", ""),
                        (p = N.stickToBottom && A()),
                        (q = N.stickToRight && B()),
                        (k = b.innerWidth() + rb != P || b.outerHeight() != Q),
                        k &&
                            ((P = b.innerWidth() + rb),
                            (Q = b.innerHeight()),
                            R.css({ width: P + "px", height: Q + "px" })),
                        !k && sb == S && O.outerHeight() == T)
                    )
                        return void b.width(P);
                    (sb = S),
                        O.css("width", ""),
                        b.width(P),
                        R.find(">.jspVerticalBar,>.jspHorizontalBar").remove().end();
                }
                O.css("overflow", "auto"),
                    (S = c.contentWidth ? c.contentWidth : O[0].scrollWidth),
                    (T = O[0].scrollHeight),
                    O.css("overflow", ""),
                    (U = S / P),
                    (V = T / Q),
                    (W = V > 1),
                    (X = U > 1),
                    X || W
                        ? (b.addClass("jspScrollable"),
                          (f = N.maintainPosition && ($ || bb)),
                          f && ((h = y()), (j = z())),
                          e(),
                          g(),
                          i(),
                          f && (w(q ? S - P : h, !1), v(p ? T - Q : j, !1)),
                          F(),
                          C(),
                          L(),
                          N.enableKeyboardNavigation && H(),
                          N.clickOnTrack && m(),
                          J(),
                          N.hijackInternalLinks && K())
                        : (b.removeClass("jspScrollable"),
                          O.css({ top: 0, left: 0, width: R.width() - rb }),
                          D(),
                          G(),
                          I(),
                          n()),
                    N.autoReinitialise && !pb
                        ? (pb = setInterval(function () {
                              d(N);
                          }, N.autoReinitialiseDelay))
                        : !N.autoReinitialise && pb && clearInterval(pb),
                    l && b.scrollTop(0) && v(l, !1),
                    o && b.scrollLeft(0) && w(o, !1),
                    b.trigger("jsp-initialised", [X || W]);
            }
            function e() {
                W &&
                    (R.append(
                        a('<div class="jspVerticalBar" />').append(
                            a('<div class="jspCap jspCapTop" />'),
                            a('<div class="jspTrack" />').append(
                                a('<div class="jspDrag" />').append(
                                    a('<div class="jspDragTop" />'),
                                    a('<div class="jspDragBottom" />')
                                )
                            ),
                            a('<div class="jspCap jspCapBottom" />')
                        )
                    ),
                    (cb = R.find(">.jspVerticalBar")),
                    (db = cb.find(">.jspTrack")),
                    (Y = db.find(">.jspDrag")),
                    N.showArrows &&
                        ((hb = a('<a class="jspArrow jspArrowUp" />')
                            .bind("mousedown.jsp", k(0, -1))
                            .bind("click.jsp", E)),
                        (ib = a('<a class="jspArrow jspArrowDown" />')
                            .bind("mousedown.jsp", k(0, 1))
                            .bind("click.jsp", E)),
                        N.arrowScrollOnHover &&
                            (hb.bind("mouseover.jsp", k(0, -1, hb)),
                            ib.bind("mouseover.jsp", k(0, 1, ib))),
                        j(db, N.verticalArrowPositions, hb, ib)),
                    (fb = Q),
                    R.find(">.jspVerticalBar>.jspCap:visible,>.jspVerticalBar>.jspArrow").each(
                        function () {
                            fb -= a(this).outerHeight();
                        }
                    ),
                    Y.hover(
                        function () {
                            Y.addClass("jspHover");
                        },
                        function () {
                            Y.removeClass("jspHover");
                        }
                    ).bind("mousedown.jsp", function (b) {
                        a("html").bind("dragstart.jsp selectstart.jsp", E), Y.addClass("jspActive");
                        var c = b.pageY - Y.position().top;
                        return (
                            a("html")
                                .bind("mousemove.jsp", function (a) {
                                    p(a.pageY - c, !1);
                                })
                                .bind("mouseup.jsp mouseleave.jsp", o),
                            !1
                        );
                    }),
                    f());
            }
            function f() {
                db.height(fb + "px"),
                    ($ = 0),
                    (eb = N.verticalGutter + db.outerWidth()),
                    O.width(P - eb - rb);
                try {
                    0 === cb.position().left && O.css("margin-left", eb + "px");
                } catch (a) {}
            }
            function g() {
                X &&
                    (R.append(
                        a('<div class="jspHorizontalBar" />').append(
                            a('<div class="jspCap jspCapLeft" />'),
                            a('<div class="jspTrack" />').append(
                                a('<div class="jspDrag" />').append(
                                    a('<div class="jspDragLeft" />'),
                                    a('<div class="jspDragRight" />')
                                )
                            ),
                            a('<div class="jspCap jspCapRight" />')
                        )
                    ),
                    (jb = R.find(">.jspHorizontalBar")),
                    (kb = jb.find(">.jspTrack")),
                    (_ = kb.find(">.jspDrag")),
                    N.showArrows &&
                        ((nb = a('<a class="jspArrow jspArrowLeft" />')
                            .bind("mousedown.jsp", k(-1, 0))
                            .bind("click.jsp", E)),
                        (ob = a('<a class="jspArrow jspArrowRight" />')
                            .bind("mousedown.jsp", k(1, 0))
                            .bind("click.jsp", E)),
                        N.arrowScrollOnHover &&
                            (nb.bind("mouseover.jsp", k(-1, 0, nb)),
                            ob.bind("mouseover.jsp", k(1, 0, ob))),
                        j(kb, N.horizontalArrowPositions, nb, ob)),
                    _.hover(
                        function () {
                            _.addClass("jspHover");
                        },
                        function () {
                            _.removeClass("jspHover");
                        }
                    ).bind("mousedown.jsp", function (b) {
                        a("html").bind("dragstart.jsp selectstart.jsp", E), _.addClass("jspActive");
                        var c = b.pageX - _.position().left;
                        return (
                            a("html")
                                .bind("mousemove.jsp", function (a) {
                                    r(a.pageX - c, !1);
                                })
                                .bind("mouseup.jsp mouseleave.jsp", o),
                            !1
                        );
                    }),
                    (lb = R.innerWidth()),
                    h());
            }
            function h() {
                R.find(">.jspHorizontalBar>.jspCap:visible,>.jspHorizontalBar>.jspArrow").each(
                    function () {
                        lb -= a(this).outerWidth();
                    }
                ),
                    kb.width(lb + "px"),
                    (bb = 0);
            }
            function i() {
                if (X && W) {
                    var b = kb.outerHeight(),
                        c = db.outerWidth();
                    (fb -= b),
                        a(jb)
                            .find(">.jspCap:visible,>.jspArrow")
                            .each(function () {
                                lb += a(this).outerWidth();
                            }),
                        (lb -= c),
                        (Q -= c),
                        (P -= b),
                        kb.parent().append(a('<div class="jspCorner" />').css("width", b + "px")),
                        f(),
                        h();
                }
                X && O.width(R.outerWidth() - rb + "px"),
                    (T = O.outerHeight()),
                    (V = T / Q),
                    X &&
                        ((mb = Math.ceil((1 / U) * lb)),
                        mb > N.horizontalDragMaxWidth
                            ? (mb = N.horizontalDragMaxWidth)
                            : mb < N.horizontalDragMinWidth && (mb = N.horizontalDragMinWidth),
                        _.width(mb + "px"),
                        (ab = lb - mb),
                        s(bb)),
                    W &&
                        ((gb = Math.ceil((1 / V) * fb)),
                        gb > N.verticalDragMaxHeight
                            ? (gb = N.verticalDragMaxHeight)
                            : gb < N.verticalDragMinHeight && (gb = N.verticalDragMinHeight),
                        Y.height(gb + "px"),
                        (Z = fb - gb),
                        q($));
            }
            function j(a, b, c, d) {
                var e,
                    f = "before",
                    g = "after";
                "os" == b && (b = /Mac/.test(navigator.platform) ? "after" : "split"),
                    b == f ? (g = b) : b == g && ((f = b), (e = c), (c = d), (d = e)),
                    a[f](c)[g](d);
            }
            function k(a, b, c) {
                return function () {
                    return l(a, b, this, c), this.blur(), !1;
                };
            }
            function l(b, c, d, e) {
                d = a(d).addClass("jspActive");
                var f,
                    g,
                    h = !0,
                    i = function () {
                        0 !== b && tb.scrollByX(b * N.arrowButtonSpeed),
                            0 !== c && tb.scrollByY(c * N.arrowButtonSpeed),
                            (g = setTimeout(i, h ? N.initialDelay : N.arrowRepeatFreq)),
                            (h = !1);
                    };
                i(),
                    (f = e ? "mouseout.jsp" : "mouseup.jsp"),
                    (e = e || a("html")),
                    e.bind(f, function () {
                        d.removeClass("jspActive"), g && clearTimeout(g), (g = null), e.unbind(f);
                    });
            }
            function m() {
                n(),
                    W &&
                        db.bind("mousedown.jsp", function (b) {
                            if (
                                void 0 === b.originalTarget ||
                                b.originalTarget == b.currentTarget
                            ) {
                                var c,
                                    d = a(this),
                                    e = d.offset(),
                                    f = b.pageY - e.top - $,
                                    g = !0,
                                    h = function () {
                                        var a = d.offset(),
                                            e = b.pageY - a.top - gb / 2,
                                            j = Q * N.scrollPagePercent,
                                            k = (Z * j) / (T - Q);
                                        if (0 > f) $ - k > e ? tb.scrollByY(-j) : p(e);
                                        else {
                                            if (!(f > 0)) return void i();
                                            e > $ + k ? tb.scrollByY(j) : p(e);
                                        }
                                        (c = setTimeout(
                                            h,
                                            g ? N.initialDelay : N.trackClickRepeatFreq
                                        )),
                                            (g = !1);
                                    },
                                    i = function () {
                                        c && clearTimeout(c),
                                            (c = null),
                                            a(document).unbind("mouseup.jsp", i);
                                    };
                                return h(), a(document).bind("mouseup.jsp", i), !1;
                            }
                        }),
                    X &&
                        kb.bind("mousedown.jsp", function (b) {
                            if (
                                void 0 === b.originalTarget ||
                                b.originalTarget == b.currentTarget
                            ) {
                                var c,
                                    d = a(this),
                                    e = d.offset(),
                                    f = b.pageX - e.left - bb,
                                    g = !0,
                                    h = function () {
                                        var a = d.offset(),
                                            e = b.pageX - a.left - mb / 2,
                                            j = P * N.scrollPagePercent,
                                            k = (ab * j) / (S - P);
                                        if (0 > f) bb - k > e ? tb.scrollByX(-j) : r(e);
                                        else {
                                            if (!(f > 0)) return void i();
                                            e > bb + k ? tb.scrollByX(j) : r(e);
                                        }
                                        (c = setTimeout(
                                            h,
                                            g ? N.initialDelay : N.trackClickRepeatFreq
                                        )),
                                            (g = !1);
                                    },
                                    i = function () {
                                        c && clearTimeout(c),
                                            (c = null),
                                            a(document).unbind("mouseup.jsp", i);
                                    };
                                return h(), a(document).bind("mouseup.jsp", i), !1;
                            }
                        });
            }
            function n() {
                kb && kb.unbind("mousedown.jsp"), db && db.unbind("mousedown.jsp");
            }
            function o() {
                a("html").unbind(
                    "dragstart.jsp selectstart.jsp mousemove.jsp mouseup.jsp mouseleave.jsp"
                ),
                    Y && Y.removeClass("jspActive"),
                    _ && _.removeClass("jspActive");
            }
            function p(c, d) {
                if (W) {
                    0 > c ? (c = 0) : c > Z && (c = Z);
                    var e = new a.Event("jsp-will-scroll-y");
                    if ((b.trigger(e, [c]), !e.isDefaultPrevented())) {
                        var f = c || 0,
                            g = 0 === f,
                            h = f == Z,
                            i = c / Z,
                            j = -i * (T - Q);
                        void 0 === d && (d = N.animateScroll),
                            d
                                ? tb.animate(Y, "top", c, q, function () {
                                      b.trigger("jsp-user-scroll-y", [-j, g, h]);
                                  })
                                : (Y.css("top", c),
                                  q(c),
                                  b.trigger("jsp-user-scroll-y", [-j, g, h]));
                    }
                }
            }
            function q(a) {
                void 0 === a && (a = Y.position().top), R.scrollTop(0), ($ = a || 0);
                var c = 0 === $,
                    d = $ == Z,
                    e = a / Z,
                    f = -e * (T - Q);
                (ub != c || wb != d) &&
                    ((ub = c), (wb = d), b.trigger("jsp-arrow-change", [ub, wb, vb, xb])),
                    t(c, d),
                    O.css("top", f),
                    b.trigger("jsp-scroll-y", [-f, c, d]).trigger("scroll");
            }
            function r(c, d) {
                if (X) {
                    0 > c ? (c = 0) : c > ab && (c = ab);
                    var e = new a.Event("jsp-will-scroll-x");
                    if ((b.trigger(e, [c]), !e.isDefaultPrevented())) {
                        var f = c || 0,
                            g = 0 === f,
                            h = f == ab,
                            i = c / ab,
                            j = -i * (S - P);
                        void 0 === d && (d = N.animateScroll),
                            d
                                ? tb.animate(_, "left", c, s, function () {
                                      b.trigger("jsp-user-scroll-x", [-j, g, h]);
                                  })
                                : (_.css("left", c),
                                  s(c),
                                  b.trigger("jsp-user-scroll-x", [-j, g, h]));
                    }
                }
            }
            function s(a) {
                void 0 === a && (a = _.position().left), R.scrollTop(0), (bb = a || 0);
                var c = 0 === bb,
                    d = bb == ab,
                    e = a / ab,
                    f = -e * (S - P);
                (vb != c || xb != d) &&
                    ((vb = c), (xb = d), b.trigger("jsp-arrow-change", [ub, wb, vb, xb])),
                    u(c, d),
                    O.css("left", f),
                    b.trigger("jsp-scroll-x", [-f, c, d]).trigger("scroll");
            }
            function t(a, b) {
                N.showArrows &&
                    (hb[a ? "addClass" : "removeClass"]("jspDisabled"),
                    ib[b ? "addClass" : "removeClass"]("jspDisabled"));
            }
            function u(a, b) {
                N.showArrows &&
                    (nb[a ? "addClass" : "removeClass"]("jspDisabled"),
                    ob[b ? "addClass" : "removeClass"]("jspDisabled"));
            }
            function v(a, b) {
                var c = a / (T - Q);
                p(c * Z, b);
            }
            function w(a, b) {
                var c = a / (S - P);
                r(c * ab, b);
            }
            function x(b, c, d) {
                var e,
                    f,
                    g,
                    h,
                    i,
                    j,
                    k,
                    l,
                    m,
                    n = 0,
                    o = 0;
                try {
                    e = a(b);
                } catch (p) {
                    return;
                }
                for (
                    f = e.outerHeight(), g = e.outerWidth(), R.scrollTop(0), R.scrollLeft(0);
                    !e.is(".jspPane");

                )
                    if (
                        ((n += e.position().top),
                        (o += e.position().left),
                        (e = e.offsetParent()),
                        /^body|html$/i.test(e[0].nodeName))
                    )
                        return;
                (h = z()),
                    (j = h + Q),
                    h > n || c
                        ? (l = n - N.horizontalGutter)
                        : n + f > j && (l = n - Q + f + N.horizontalGutter),
                    isNaN(l) || v(l, d),
                    (i = y()),
                    (k = i + P),
                    i > o || c
                        ? (m = o - N.horizontalGutter)
                        : o + g > k && (m = o - P + g + N.horizontalGutter),
                    isNaN(m) || w(m, d);
            }
            function y() {
                return -O.position().left;
            }
            function z() {
                return -O.position().top;
            }
            function A() {
                var a = T - Q;
                return a > 20 && a - z() < 10;
            }
            function B() {
                var a = S - P;
                return a > 20 && a - y() < 10;
            }
            function C() {
                R.unbind(zb).bind(zb, function (a, b, c, d) {
                    bb || (bb = 0), $ || ($ = 0);
                    var e = bb,
                        f = $,
                        g = a.deltaFactor || N.mouseWheelSpeed;
                    return tb.scrollBy(c * g, -d * g, !1), e == bb && f == $;
                });
            }
            function D() {
                R.unbind(zb);
            }
            function E() {
                return !1;
            }
            function F() {
                O.find(":input,a")
                    .unbind("focus.jsp")
                    .bind("focus.jsp", function (a) {
                        x(a.target, !1);
                    });
            }
            function G() {
                O.find(":input,a").unbind("focus.jsp");
            }
            function H() {
                function c() {
                    var a = bb,
                        b = $;
                    switch (d) {
                        case 40:
                            tb.scrollByY(N.keyboardSpeed, !1);
                            break;
                        case 38:
                            tb.scrollByY(-N.keyboardSpeed, !1);
                            break;
                        case 34:
                        case 32:
                            tb.scrollByY(Q * N.scrollPagePercent, !1);
                            break;
                        case 33:
                            tb.scrollByY(-Q * N.scrollPagePercent, !1);
                            break;
                        case 39:
                            tb.scrollByX(N.keyboardSpeed, !1);
                            break;
                        case 37:
                            tb.scrollByX(-N.keyboardSpeed, !1);
                    }
                    return (e = a != bb || b != $);
                }
                var d,
                    e,
                    f = [];
                X && f.push(jb[0]),
                    W && f.push(cb[0]),
                    O.bind("focus.jsp", function () {
                        b.focus();
                    }),
                    b
                        .attr("tabindex", 0)
                        .unbind("keydown.jsp keypress.jsp")
                        .bind("keydown.jsp", function (b) {
                            if (b.target === this || (f.length && a(b.target).closest(f).length)) {
                                var g = bb,
                                    h = $;
                                switch (b.keyCode) {
                                    case 40:
                                    case 38:
                                    case 34:
                                    case 32:
                                    case 33:
                                    case 39:
                                    case 37:
                                        (d = b.keyCode), c();
                                        break;
                                    case 35:
                                        v(T - Q), (d = null);
                                        break;
                                    case 36:
                                        v(0), (d = null);
                                }
                                return (e = (b.keyCode == d && g != bb) || h != $), !e;
                            }
                        })
                        .bind("keypress.jsp", function (b) {
                            return (
                                b.keyCode == d && c(),
                                b.target === this || (f.length && a(b.target).closest(f).length)
                                    ? !e
                                    : void 0
                            );
                        }),
                    N.hideFocus
                        ? (b.css("outline", "none"), "hideFocus" in R[0] && b.attr("hideFocus", !0))
                        : (b.css("outline", ""), "hideFocus" in R[0] && b.attr("hideFocus", !1));
            }
            function I() {
                b.attr("tabindex", "-1").removeAttr("tabindex").unbind("keydown.jsp keypress.jsp"),
                    O.unbind(".jsp");
            }
            function J() {
                if (location.hash && location.hash.length > 1) {
                    var b,
                        c,
                        d = escape(location.hash.substr(1));
                    try {
                        b = a("#" + d + ', a[name="' + d + '"]');
                    } catch (e) {
                        return;
                    }
                    b.length &&
                        O.find(d) &&
                        (0 === R.scrollTop()
                            ? (c = setInterval(function () {
                                  R.scrollTop() > 0 &&
                                      (x(b, !0),
                                      a(document).scrollTop(R.position().top),
                                      clearInterval(c));
                              }, 50))
                            : (x(b, !0), a(document).scrollTop(R.position().top)));
                }
            }
            function K() {
                a(document.body).data("jspHijack") ||
                    (a(document.body).data("jspHijack", !0),
                    a(document.body).delegate('a[href*="#"]', "click", function (b) {
                        var c,
                            d,
                            e,
                            f,
                            g,
                            h,
                            i = this.href.substr(0, this.href.indexOf("#")),
                            j = location.href;
                        if (
                            (-1 !== location.href.indexOf("#") &&
                                (j = location.href.substr(0, location.href.indexOf("#"))),
                            i === j)
                        ) {
                            c = escape(this.href.substr(this.href.indexOf("#") + 1));
                            try {
                                d = a("#" + c + ', a[name="' + c + '"]');
                            } catch (k) {
                                return;
                            }
                            d.length &&
                                ((e = d.closest(".jspScrollable")),
                                (f = e.data("jsp")),
                                f.scrollToElement(d, !0),
                                e[0].scrollIntoView &&
                                    ((g = a(window).scrollTop()),
                                    (h = d.offset().top),
                                    (g > h || h > g + a(window).height()) && e[0].scrollIntoView()),
                                b.preventDefault());
                        }
                    }));
            }
            function L() {
                var a,
                    b,
                    c,
                    d,
                    e,
                    f = !1;
                R.unbind("touchstart.jsp touchmove.jsp touchend.jsp click.jsp-touchclick")
                    .bind("touchstart.jsp", function (g) {
                        var h = g.originalEvent.touches[0];
                        (a = y()), (b = z()), (c = h.pageX), (d = h.pageY), (e = !1), (f = !0);
                    })
                    .bind("touchmove.jsp", function (g) {
                        if (f) {
                            var h = g.originalEvent.touches[0],
                                i = bb,
                                j = $;
                            return (
                                tb.scrollTo(a + c - h.pageX, b + d - h.pageY),
                                (e = e || Math.abs(c - h.pageX) > 5 || Math.abs(d - h.pageY) > 5),
                                i == bb && j == $
                            );
                        }
                    })
                    .bind("touchend.jsp", function () {
                        f = !1;
                    })
                    .bind("click.jsp-touchclick", function () {
                        return e ? ((e = !1), !1) : void 0;
                    });
            }
            function M() {
                var a = z(),
                    c = y();
                b.removeClass("jspScrollable").unbind(".jsp"),
                    O.unbind(".jsp"),
                    b.replaceWith(yb.append(O.children())),
                    yb.scrollTop(a),
                    yb.scrollLeft(c),
                    pb && clearInterval(pb);
            }
            var N,
                O,
                P,
                Q,
                R,
                S,
                T,
                U,
                V,
                W,
                X,
                Y,
                Z,
                $,
                _,
                ab,
                bb,
                cb,
                db,
                eb,
                fb,
                gb,
                hb,
                ib,
                jb,
                kb,
                lb,
                mb,
                nb,
                ob,
                pb,
                qb,
                rb,
                sb,
                tb = this,
                ub = !0,
                vb = !0,
                wb = !1,
                xb = !1,
                yb = b.clone(!1, !1).empty(),
                zb = a.fn.mwheelIntent ? "mwheelIntent.jsp" : "mousewheel.jsp";
            "border-box" === b.css("box-sizing")
                ? ((qb = 0), (rb = 0))
                : ((qb =
                      b.css("paddingTop") +
                      " " +
                      b.css("paddingRight") +
                      " " +
                      b.css("paddingBottom") +
                      " " +
                      b.css("paddingLeft")),
                  (rb =
                      (parseInt(b.css("paddingLeft"), 10) || 0) +
                      (parseInt(b.css("paddingRight"), 10) || 0))),
                a.extend(tb, {
                    reinitialise: function (b) {
                        (b = a.extend({}, N, b)), d(b);
                    },
                    scrollToElement: function (a, b, c) {
                        x(a, b, c);
                    },
                    scrollTo: function (a, b, c) {
                        w(a, c), v(b, c);
                    },
                    scrollToX: function (a, b) {
                        w(a, b);
                    },
                    scrollToY: function (a, b) {
                        v(a, b);
                    },
                    scrollToPercentX: function (a, b) {
                        w(a * (S - P), b);
                    },
                    scrollToPercentY: function (a, b) {
                        v(a * (T - Q), b);
                    },
                    scrollBy: function (a, b, c) {
                        tb.scrollByX(a, c), tb.scrollByY(b, c);
                    },
                    scrollByX: function (a, b) {
                        var c = y() + Math[0 > a ? "floor" : "ceil"](a),
                            d = c / (S - P);
                        r(d * ab, b);
                    },
                    scrollByY: function (a, b) {
                        var c = z() + Math[0 > a ? "floor" : "ceil"](a),
                            d = c / (T - Q);
                        p(d * Z, b);
                    },
                    positionDragX: function (a, b) {
                        r(a, b);
                    },
                    positionDragY: function (a, b) {
                        p(a, b);
                    },
                    animate: function (a, b, c, d, e) {
                        var f = {};
                        (f[b] = c),
                            a.animate(f, {
                                duration: N.animateDuration,
                                easing: N.animateEase,
                                queue: !1,
                                step: d,
                                complete: e,
                            });
                    },
                    getContentPositionX: function () {
                        return y();
                    },
                    getContentPositionY: function () {
                        return z();
                    },
                    getContentWidth: function () {
                        return S;
                    },
                    getContentHeight: function () {
                        return T;
                    },
                    getPercentScrolledX: function () {
                        return y() / (S - P);
                    },
                    getPercentScrolledY: function () {
                        return z() / (T - Q);
                    },
                    getIsScrollableH: function () {
                        return X;
                    },
                    getIsScrollableV: function () {
                        return W;
                    },
                    getContentPane: function () {
                        return O;
                    },
                    scrollToBottom: function (a) {
                        p(Z, a);
                    },
                    hijackInternalLinks: a.noop,
                    destroy: function () {
                        M();
                    },
                }),
                d(c);
        }
        return (
            (b = a.extend({}, a.fn.jScrollPane.defaults, b)),
            a.each(["arrowButtonSpeed", "trackClickSpeed", "keyboardSpeed"], function () {
                b[this] = b[this] || b.speed;
            }),
            this.each(function () {
                var d = a(this),
                    e = d.data("jsp");
                e
                    ? e.reinitialise(b)
                    : (a("script", d).filter('[type="text/javascript"],:not([type])').remove(),
                      (e = new c(d, b)),
                      d.data("jsp", e));
            })
        );
    }),
        (a.fn.jScrollPane.defaults = {
            showArrows: !1,
            maintainPosition: !0,
            stickToBottom: !1,
            stickToRight: !1,
            clickOnTrack: !0,
            autoReinitialise: !1,
            autoReinitialiseDelay: 500,
            verticalDragMinHeight: 0,
            verticalDragMaxHeight: 99999,
            horizontalDragMinWidth: 0,
            horizontalDragMaxWidth: 99999,
            contentWidth: void 0,
            animateScroll: !1,
            animateDuration: 300,
            animateEase: "linear",
            hijackInternalLinks: !1,
            verticalGutter: 4,
            horizontalGutter: 4,
            mouseWheelSpeed: 3,
            arrowButtonSpeed: 0,
            arrowRepeatFreq: 50,
            arrowScrollOnHover: !1,
            trackClickSpeed: 0,
            trackClickRepeatFreq: 70,
            verticalArrowPositions: "split",
            horizontalArrowPositions: "split",
            enableKeyboardNavigation: !0,
            hideFocus: !1,
            keyboardSpeed: 0,
            initialDelay: 300,
            speed: 30,
            scrollPagePercent: 0.8,
        });
});
