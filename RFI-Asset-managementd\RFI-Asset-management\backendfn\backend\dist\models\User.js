"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
class User extends sequelize_1.Model {
    // Instance method to check password
    async comparePassword(candidatePassword) {
        return bcryptjs_1.default.compare(candidatePassword, this.password);
    }
    // Instance method to generate JWT token with enhanced data
    generateToken() {
        const jwt = require('jsonwebtoken');
        return jwt.sign({
            id: this.id,
            email: this.email,
            role: this.role,
            accessLevel: this.accessLevel,
            county: this.county,
            precinct: this.precinct, // Include precinct in token
            userGroup: this.userGroup,
            loginId: this.loginId,
            firstName: this.firstName,
            lastName: this.lastName
        }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
    }
    // Helper method to get full name
    getFullName() {
        return `${this.firstName} ${this.lastName}`;
    }
    // Helper method to check if user has admin privileges
    isAdmin() {
        return ['admin', 'Portal Admin'].includes(this.role);
    }
    // Helper method to check access level
    hasStateAccess() {
        return this.accessLevel === 'state' || this.isAdmin();
    }
    hasCountyAccess() {
        return ['state', 'county'].includes(this.accessLevel) || this.isAdmin();
    }
    hasPrecinctAccess() {
        return ['state', 'county', 'precinct'].includes(this.accessLevel) || this.isAdmin();
    }
}
User.init({
    id: {
        type: sequelize_1.DataTypes.STRING,
        primaryKey: true,
    },
    firstName: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        field: 'first_name'
    },
    lastName: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        field: 'last_name'
    },
    email: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
            isEmail: true,
        },
    },
    password: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    mobile: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    userGroup: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        field: 'user_group'
    },
    loginEnabled: {
        type: sequelize_1.DataTypes.BOOLEAN,
        defaultValue: true,
        field: 'login_enabled'
    },
    loginId: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        unique: true,
        field: 'login_id'
    },
    accessLevel: {
        type: sequelize_1.DataTypes.ENUM('county', 'state', 'precinct'), // Updated to include precinct
        allowNull: false,
        field: 'access_level'
    },
    county: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    precinct: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    status: {
        type: sequelize_1.DataTypes.BOOLEAN,
        defaultValue: true,
    },
    image: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    company: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    employeeNo: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
        field: 'employee_no'
    },
    manager: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    department: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    location: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    addressLine1: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
        field: 'address_line1'
    },
    addressLine2: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
        field: 'address_line2'
    },
    city: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    state: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    pincode: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    country: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    role: {
        type: sequelize_1.DataTypes.ENUM('admin', 'manager', 'user', 'Portal Admin'),
        allowNull: false,
        defaultValue: 'user',
    },
    username: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at'
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'updated_at'
    },
}, {
    sequelize: database_1.sequelize,
    tableName: 'users',
    underscored: true,
    hooks: {
        beforeCreate: async (user) => {
            if (user.password) {
                const salt = await bcryptjs_1.default.genSalt(12);
                user.password = await bcryptjs_1.default.hash(user.password, salt);
            }
        },
        beforeUpdate: async (user) => {
            if (user.changed('password')) {
                const salt = await bcryptjs_1.default.genSalt(12);
                user.password = await bcryptjs_1.default.hash(user.password, salt);
            }
        },
    },
});
exports.default = User;
