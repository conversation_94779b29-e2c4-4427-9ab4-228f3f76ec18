"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDatabase = createDatabase;
exports.grantDatabasePrivileges = grantDatabasePrivileges;
const database_1 = require("../config/database");
const User_1 = __importDefault(require("../models/User"));
async function createDatabase() {
    try {
        console.log('🔄 Creating database tables for assetmanagementool...');
        // Test connection first
        await database_1.sequelize.authenticate();
        console.log('✅ Connected to assetmanagementool database');
        // Create all tables (use force: false for production, true only for initial setup)
        await database_1.sequelize.sync({ force: true, alter: false });
        console.log('✅ Database tables created successfully in assetmanagementool');
        // Create default admin user
        await createDefaultUser();
        console.log('✅ Database setup completed for assetmanagementool');
    }
    catch (error) {
        console.error('❌ Error creating database:', error);
        // Provide specific error handling
        if (error instanceof Error) {
            if (error.message.includes('ECONNREFUSED')) {
                console.error('💡 MySQL server connection refused. Check if MySQL is running on **************:3306');
            }
            else if (error.message.includes('Access denied')) {
                console.error('💡 Access denied. Check username "sumanth" and password in .env file');
            }
            else if (error.message.includes('Unknown database')) {
                console.error('💡 Database "assetmanagementool" not found. Make sure it exists');
            }
        }
        throw error;
    }
}
async function createDefaultUser() {
    try {
        // Check if admin user already exists
        const adminExists = await User_1.default.findOne({ where: { email: '<EMAIL>' } });
        if (!adminExists) {
            await User_1.default.create({
                username: 'admin',
                email: '<EMAIL>',
                password: 'Admin@123', // Will be hashed by the model
                firstName: 'System',
                lastName: 'Administrator',
                mobile: '+91-9876543210',
                userGroup: 'administrators',
                loginEnabled: true,
                loginId: 'admin',
                accessLevel: 'state',
                status: true,
                role: 'admin',
                department: 'IT'
            });
            console.log('✅ Default admin user created:');
            console.log('   📧 Email: <EMAIL>');
            console.log('   🔐 Password: Admin@123');
            console.log('   👤 Username: admin');
        }
        else {
            console.log('ℹ️ Admin user already exists');
        }
        // Create a test user as well
        const testUserExists = await User_1.default.findOne({ where: { email: '<EMAIL>' } });
        if (!testUserExists) {
            await User_1.default.create({
                username: 'sumanth',
                email: '<EMAIL>',
                password: 'Sumanth@123',
                firstName: 'Sumanth',
                lastName: 'User',
                mobile: '+91-9876543211',
                userGroup: 'users',
                loginEnabled: true,
                loginId: 'sumanth',
                accessLevel: 'department',
                status: true,
                role: 'user',
                department: 'Operations'
            });
            console.log('✅ Test user created:');
            console.log('   📧 Email: <EMAIL>');
            console.log('   🔐 Password: Sumanth@123');
            console.log('   👤 Username: sumanth');
        }
    }
    catch (error) {
        console.error('❌ Error creating default users:', error);
        throw error;
    }
}
// Function to grant privileges (if needed)
async function grantDatabasePrivileges() {
    try {
        console.log('🔄 Checking database privileges...');
        // Grant privileges to the users (run this as root or admin)
        const grantQueries = [
            `GRANT ALL PRIVILEGES ON assetmanagementool.* TO 'sumanth'@'%'`,
            `GRANT ALL PRIVILEGES ON assetmanagementool.* TO 'sai'@'%'`,
            `FLUSH PRIVILEGES`
        ];
        for (const query of grantQueries) {
            await database_1.sequelize.query(query);
        }
        console.log('✅ Database privileges granted successfully');
    }
    catch (error) {
        console.error('❌ Error granting privileges (may need admin access):', error);
    }
}
// Run if called directly
if (require.main === module) {
    (async () => {
        try {
            console.log('🚀 Starting Asset Management Database Setup...');
            console.log('📍 Target Database: assetmanagementool');
            console.log('🔗 Host: **************:3306');
            console.log('👤 User: sumanth');
            await database_1.sequelize.authenticate();
            console.log('✅ Database connection established successfully');
            await createDatabase();
            console.log('🎉 Asset Management Database setup completed successfully!');
            console.log('📝 Database: assetmanagementool');
            console.log('🔧 Tables created and users setup complete');
            console.log('💡 You can now start your application server');
            console.log('');
            console.log('Default Login Credentials:');
            console.log('👨‍💼 Admin - Email: <EMAIL> | Password: Admin@123');
            console.log('👤 User - Email: <EMAIL> | Password: Sumanth@123');
            process.exit(0);
        }
        catch (error) {
            console.error('❌ Setup failed:', error);
            console.log('');
            console.log('🔧 Troubleshooting steps:');
            console.log('1. Ensure MySQL server is running on **************:3306');
            console.log('2. Verify database "assetmanagementool" exists');
            console.log('3. Check user "sumanth" has proper privileges');
            console.log('4. Verify .env file has correct credentials');
            process.exit(1);
        }
    })();
}
