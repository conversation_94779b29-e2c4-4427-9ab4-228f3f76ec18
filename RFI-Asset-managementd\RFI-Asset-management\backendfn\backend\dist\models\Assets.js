"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
// Asset model class
class Asset extends sequelize_1.Model {
    // Helper method to get specifications as object
    getSpecificationsData() {
        try {
            return this.specifications ? JSON.parse(this.specifications) : {};
        }
        catch (error) {
            console.error('Error parsing specifications:', error);
            return {};
        }
    }
    // Helper method to set specifications from object
    setSpecificationsData(specs) {
        this.specifications = JSON.stringify(specs);
    }
}
// Initialize the model
Asset.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    }, assetId: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        unique: true,
        field: 'asset_id', // Map to actual database column name
        validate: {
            notEmpty: true,
        },
    },
    type: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        validate: {
            notEmpty: true,
        },
    },
    model: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    }, serialNumber: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
        field: 'serial_number', // Map to actual database column name
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('Available', 'In Transit', 'Deployed', 'Damaged', 'Under Repair', 'Retired'),
        allowNull: false,
        defaultValue: 'Available',
    },
    condition: {
        type: sequelize_1.DataTypes.ENUM('Excellent', 'Good', 'Fair', 'Poor', 'Damaged'),
        allowNull: false,
        defaultValue: 'Good',
    },
    location: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        validate: {
            notEmpty: true,
        },
    }, assignedTo: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
        field: 'assigned_to', // Map to actual database column name
    },
    county: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    },
    precinct: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    }, purchaseDate: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'purchase_date', // Map to actual database column name
    },
    warrantyExpiry: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'warranty_expiry', // Map to actual database column name
    }, lastMaintenance: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'last_maintenance_date', // Map to actual database column name
    },
    nextMaintenance: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'next_maintenance_date', // Map to actual database column name
    }, lastChecked: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        field: 'last_checked', // Map to actual database column name
    },
    notes: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    specifications: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
}, {
    sequelize: database_1.sequelize,
    modelName: 'Asset',
    tableName: 'assets',
    timestamps: true,
});
exports.default = Asset;
