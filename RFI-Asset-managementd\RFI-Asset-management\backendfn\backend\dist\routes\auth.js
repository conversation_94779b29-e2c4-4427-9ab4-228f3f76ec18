"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const uuid_1 = require("uuid");
const User_1 = __importDefault(require("../models/User"));
const auth_1 = require("../middleware/auth");
const rateLimiter_1 = require("../middleware/rateLimiter");
const router = (0, express_1.Router)();
// Enhanced login route with role-based logic
router.post('/login', rateLimiter_1.authLimiter, async (req, res) => {
    try {
        const { email, password } = req.body;
        // Validate input
        if (!email || !password) {
            res.status(400).json({
                success: false,
                message: 'Email and password are required'
            });
            return;
        }
        console.log(`Login attempt for: ${email}`);
        // Find user by email, username, or loginId
        const user = await User_1.default.findOne({
            where: {
                [require('sequelize').Op.or]: [
                    { email: email.toLowerCase() },
                    { username: email },
                    { loginId: email }
                ]
            }
        });
        if (!user) {
            console.log(`User not found: ${email}`);
            res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
            return;
        }
        // Check if user is active
        if (!user.status) {
            console.log(`User account disabled: ${email}`);
            res.status(401).json({
                success: false,
                message: 'Account is disabled'
            });
            return;
        }
        // Check if login is enabled
        if (!user.loginEnabled) {
            console.log(`Login disabled for user: ${email}`);
            res.status(401).json({
                success: false,
                message: 'Login is disabled for this account'
            });
            return;
        }
        // Check password
        const isValidPassword = await user.comparePassword(password);
        if (!isValidPassword) {
            console.log(`Invalid password for: ${email}`);
            res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
            return;
        }
        console.log(`Login successful for: ${email}, Role: ${user.role}, Access: ${user.accessLevel}`);
        // Generate JWT token
        const token = user.generateToken();
        // Prepare user data based on role and access level
        const userData = {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: user.getFullName(),
            email: user.email,
            mobile: user.mobile,
            role: user.role,
            department: user.department,
            location: user.location,
            accessLevel: user.accessLevel,
            county: user.county,
            precinct: user.precinct, // Include precinct
            userGroup: user.userGroup,
            loginId: user.loginId,
            company: user.company,
            employeeNo: user.employeeNo,
            manager: user.manager,
            // Add permissions based on role
            permissions: {
                isAdmin: user.isAdmin(),
                hasStateAccess: user.hasStateAccess(),
                hasCountyAccess: user.hasCountyAccess(),
                hasPrecinctAccess: user.hasPrecinctAccess(),
                canManageUsers: ['admin', 'Portal Admin'].includes(user.role),
                canManageAssets: ['admin', 'Portal Admin', 'manager'].includes(user.role),
                canViewReports: true // All users can view reports, but content will be filtered
            },
            // Determine default dashboard route based on role
            defaultRoute: getDefaultRoute(user.role, user.accessLevel)
        };
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: userData,
                token: token,
                redirectTo: getDefaultRoute(user.role, user.accessLevel) // For frontend routing
            }
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Helper function to determine default route based on role and access level
function getDefaultRoute(role, accessLevel) {
    switch (role) {
        case 'Portal Admin':
        case 'admin':
            return '/admin/dashboard';
        case 'manager':
            return accessLevel === 'state' ? '/state/dashboard' : '/county/dashboard';
        case 'user':
            switch (accessLevel) {
                case 'state':
                    return '/state/dashboard';
                case 'county':
                    return '/county/dashboard';
                case 'precinct':
                    return '/precinct/dashboard';
                default:
                    return '/dashboard';
            }
        default:
            return '/dashboard';
    }
}
// Enhanced register route
router.post('/register', async (req, res) => {
    try {
        const { firstName, lastName, email, password, mobile, role = 'user', department, location, accessLevel = 'precinct', // Default to most restrictive
        county, precinct, // New field
        userGroup = 'default', loginId, company, employeeNo, manager } = req.body;
        // Validate required fields
        if (!firstName || !lastName || !email || !password) {
            res.status(400).json({
                success: false,
                message: 'First name, last name, email, and password are required'
            });
            return;
        }
        // Validate access level requirements
        if (accessLevel === 'county' && !county) {
            res.status(400).json({
                success: false,
                message: 'County is required for county-level access'
            });
            return;
        }
        if (accessLevel === 'precinct' && (!county || !precinct)) {
            res.status(400).json({
                success: false,
                message: 'County and precinct are required for precinct-level access'
            });
            return;
        }
        // Check if user already exists
        const existingUser = await User_1.default.findOne({
            where: {
                [require('sequelize').Op.or]: [
                    { email: email.toLowerCase() },
                    { loginId: loginId || email.toLowerCase() }
                ]
            }
        });
        if (existingUser) {
            res.status(409).json({
                success: false,
                message: 'User with this email or login ID already exists'
            });
            return;
        }
        // Create new user
        const user = await User_1.default.create({
            id: (0, uuid_1.v4)(),
            firstName,
            lastName,
            email: email.toLowerCase(),
            password,
            mobile: mobile || '',
            role,
            department,
            location,
            accessLevel,
            county,
            precinct, // Include precinct
            userGroup,
            loginId: loginId || email.toLowerCase(),
            loginEnabled: true,
            status: true,
            company,
            employeeNo,
            manager
        });
        console.log(`User created: ${email}, Role: ${role}, Access: ${accessLevel}`);
        // Generate JWT token
        const token = user.generateToken();
        // Return user data (excluding password)
        const userData = {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: user.getFullName(),
            email: user.email,
            role: user.role,
            department: user.department,
            location: user.location,
            accessLevel: user.accessLevel,
            county: user.county,
            precinct: user.precinct,
            userGroup: user.userGroup,
            loginId: user.loginId,
            permissions: {
                isAdmin: user.isAdmin(),
                hasStateAccess: user.hasStateAccess(),
                hasCountyAccess: user.hasCountyAccess(),
                hasPrecinctAccess: user.hasPrecinctAccess()
            }
        };
        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            data: {
                user: userData,
                token: token
            }
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get profile route - enhanced with permissions
router.get('/profile', auth_1.authenticateToken, async (req, res) => {
    try {
        const user = await User_1.default.findByPk(req.user.id, {
            attributes: { exclude: ['password'] }
        });
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        // Add computed fields
        const userWithPermissions = {
            ...user.toJSON(),
            fullName: user.getFullName(),
            permissions: {
                isAdmin: user.isAdmin(),
                hasStateAccess: user.hasStateAccess(),
                hasCountyAccess: user.hasCountyAccess(),
                hasPrecinctAccess: user.hasPrecinctAccess(),
                canManageUsers: ['admin', 'Portal Admin'].includes(user.role),
                canManageAssets: ['admin', 'Portal Admin', 'manager'].includes(user.role),
                canViewReports: true
            }
        };
        res.json({
            success: true,
            data: {
                user: userWithPermissions
            }
        });
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Update profile route
router.put('/profile', auth_1.authenticateToken, async (req, res) => {
    try {
        const { firstName, lastName, mobile, department, location, addressLine1, addressLine2, city, state, pincode, country } = req.body;
        const user = await User_1.default.findByPk(req.user.id);
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        // Update user fields
        await user.update({
            firstName: firstName || user.firstName,
            lastName: lastName || user.lastName,
            mobile: mobile || user.mobile,
            department: department || user.department,
            location: location || user.location,
            addressLine1: addressLine1 || user.addressLine1,
            addressLine2: addressLine2 || user.addressLine2,
            city: city || user.city,
            state: state || user.state,
            pincode: pincode || user.pincode,
            country: country || user.country
        });
        // Return updated user data (excluding password)
        const updatedUser = await User_1.default.findByPk(req.user.id, {
            attributes: { exclude: ['password'] }
        });
        res.json({
            success: true,
            message: 'Profile updated successfully',
            data: {
                user: updatedUser
            }
        });
    }
    catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get all users (admin only) - with enhanced filtering
router.get('/users', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const { accessLevel, county, precinct, role, status, page = 1, limit = 50 } = req.query;
        const whereClause = {};
        // Add filters
        if (accessLevel)
            whereClause.accessLevel = accessLevel;
        if (county)
            whereClause.county = county;
        if (precinct)
            whereClause.precinct = precinct;
        if (role)
            whereClause.role = role;
        if (status !== undefined)
            whereClause.status = status === 'true';
        const offset = (Number(page) - 1) * Number(limit);
        const { count, rows } = await User_1.default.findAndCountAll({
            where: whereClause,
            attributes: { exclude: ['password'] },
            limit: Number(limit),
            offset,
            order: [['createdAt', 'DESC']]
        });
        // Add computed fields to each user
        const usersWithPermissions = rows.map(user => ({
            ...user.toJSON(),
            fullName: user.getFullName(),
            permissions: {
                isAdmin: user.isAdmin(),
                hasStateAccess: user.hasStateAccess(),
                hasCountyAccess: user.hasCountyAccess(),
                hasPrecinctAccess: user.hasPrecinctAccess()
            }
        }));
        res.json({
            success: true,
            data: {
                users: usersWithPermissions,
                pagination: {
                    total: count,
                    page: Number(page),
                    limit: Number(limit),
                    totalPages: Math.ceil(count / Number(limit))
                }
            }
        });
    }
    catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Rest of the routes remain the same but should include precinct field handling...
// (getUserById, updateUser, deleteUser, changePassword - all need similar updates)
// Get user by ID
router.get('/users/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        const user = await User_1.default.findByPk(req.params.id, {
            attributes: { exclude: ['password'] }
        });
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        const userWithPermissions = {
            ...user.toJSON(),
            fullName: user.getFullName(),
            permissions: {
                isAdmin: user.isAdmin(),
                hasStateAccess: user.hasStateAccess(),
                hasCountyAccess: user.hasCountyAccess(),
                hasPrecinctAccess: user.hasPrecinctAccess()
            }
        };
        res.json({
            success: true,
            data: { user: userWithPermissions }
        });
    }
    catch (error) {
        console.error('Get user error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Update user (admin only)
router.put('/users/:id', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const user = await User_1.default.findByPk(req.params.id);
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        // Update user fields (exclude sensitive fields)
        const updateData = { ...req.body };
        delete updateData.id;
        delete updateData.password; // Handle password separately
        await user.update(updateData);
        const updatedUser = await User_1.default.findByPk(req.params.id, {
            attributes: { exclude: ['password'] }
        });
        res.json({
            success: true,
            message: 'User updated successfully',
            data: { user: updatedUser }
        });
    }
    catch (error) {
        console.error('Update user error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Delete user (admin only)
router.delete('/users/:id', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const user = await User_1.default.findByPk(req.params.id);
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        await user.destroy();
        res.json({
            success: true,
            message: 'User deleted successfully'
        });
    }
    catch (error) {
        console.error('Delete user error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Change password route
router.put('/change-password', auth_1.authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        if (!currentPassword || !newPassword) {
            res.status(400).json({
                success: false,
                message: 'Current password and new password are required'
            });
            return;
        }
        const user = await User_1.default.findByPk(req.user.id);
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        // Verify current password
        const isValidPassword = await user.comparePassword(currentPassword);
        if (!isValidPassword) {
            res.status(401).json({
                success: false,
                message: 'Current password is incorrect'
            });
            return;
        }
        // Update password
        await user.update({ password: newPassword });
        res.json({
            success: true,
            message: 'Password changed successfully'
        });
    }
    catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
