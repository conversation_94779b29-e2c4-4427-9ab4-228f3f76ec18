import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, FileDown, Plus, Search, AlertTriangle, Check, X, RefreshCw } from "lucide-react";
import { PageTitle } from "@/components/layout/PageTitle";
import { Link } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { exportData } from "@/utils/exportUtils";

// Stock Item interface
interface StockItem {
  id: string;
  name: string;
  category: string;
  location: string;
  quantity: number;
}

// Toast notification system
interface ToastData {
  id: string;
  type: 'success' | 'error';
  message: string;
}

let toastContainer: React.Dispatch<React.SetStateAction<ToastData[]>> | null = null;

const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  useEffect(() => {
    toastContainer = setToasts;
    return () => {
      toastContainer = null;
    };
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  useEffect(() => {
    toasts.forEach(toast => {
      const timer = setTimeout(() => {
        removeToast(toast.id);
      }, 5000);
      return () => clearTimeout(timer);
    });
  }, [toasts]);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map(toast => (
        <div
          key={toast.id}
          className={`flex items-center p-4 mb-3 rounded-lg shadow-lg border-l-4 transform transition-all duration-300 ease-in-out ${
            toast.type === 'success' 
              ? 'bg-green-50 border-green-500 text-green-800' 
              : 'bg-red-50 border-red-500 text-red-800'
          }`}
        >
          <span className="flex-1 text-sm font-medium">{toast.message}</span>
          <button
            onClick={() => removeToast(toast.id)}
            className={`ml-3 p-1 rounded-full text-lg font-bold ${
              toast.type === 'success' ? 'hover:bg-green-200' : 'hover:bg-red-200'
            }`}
          >
            ×
          </button>
        </div>
      ))}
    </div>
  );
};

const toast = {
  success: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'success', message }]);
    }
  },
  error: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'error', message }]);
    }
  }
};

// API service
const API_BASE_URL = 'http://localhost:5000/api';

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

export default function StockPage() {
  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<StockItem[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [locations, setLocations] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [locationFilter, setLocationFilter] = useState("all");
  const [selectedLocation, setSelectedLocation] = useState("all");
  const [selectedItem, setSelectedItem] = useState("all");

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [totalPages, setTotalPages] = useState(1);

  console.log("🔍 StockPage rendering, loading:", loading, "stock items:", stockItems.length);

  // Load stock data from backend
  const loadStockData = async () => {
    try {
      console.log("🚀 Starting Stock API call...");
      setLoading(true);
      setError(null);

      const response = await apiCall('/transaction-orders/stock-summary');
      console.log("📡 Stock API Response:", response);

      if (response.success && response.data) {
        // Transform the backend response to StockItem format
        const stockData: StockItem[] = [];

        // The backend returns: { "location - consumable": { orderQty: number, "item - consumable": {...} } }
        Object.entries(response.data).forEach(([locationConsumableKey, locationData]: [string, any]) => {
          console.log("🔍 Processing location-consumable:", locationConsumableKey, locationData);

          // Extract location and consumable from the key
          const [location, consumable] = locationConsumableKey.split(' - ');

          // Add order-level stock entry
          if (locationData.orderQty && locationData.orderQty > 0) {
            stockData.push({
              id: `ORDER-${locationConsumableKey}`,
              name: consumable,
              category: 'Order Level',
              location: location,
              quantity: locationData.orderQty
            });
          }

          // Process individual items within this location-consumable
          Object.entries(locationData).forEach(([itemKey, itemData]: [string, any]) => {
            if (itemKey !== 'orderQty' && typeof itemData === 'object' && itemData.totalQty) {
              console.log("🔍 Processing item:", itemKey, itemData);

              stockData.push({
                id: `ITEM-${itemKey}`,
                name: itemData.item || itemKey.split(' - ')[0],
                category: itemData.consumable || 'Unknown Category',
                location: itemData.location || location,
                quantity: itemData.totalQty
              });
            }
          });
        });

        console.log("📦 Transformed stock data:", stockData);

        setStockItems(stockData);
        setFilteredItems(stockData);

        // Extract unique categories and locations
        const uniqueCategories = [...new Set(stockData.map(item => item.category))].filter(Boolean);
        const uniqueLocations = [...new Set(stockData.map(item => item.location))].filter(Boolean);

        setCategories(uniqueCategories);
        setLocations(uniqueLocations);

        // Calculate total pages
        setTotalPages(Math.ceil(stockData.length / itemsPerPage));

        console.log("📊 Stock data loaded:", {
          items: stockData.length,
          categories: uniqueCategories.length,
          locations: uniqueLocations.length
        });

      } else {
        console.warn("⚠️ Invalid response or no data:", response);
        setError(response.message || 'No stock data available');
        setStockItems([]);
        setFilteredItems([]);
      }
    } catch (err) {
      console.error('❌ Error loading stock data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load stock data');
      setStockItems([]);
      setFilteredItems([]);
    } finally {
      setLoading(false);
      console.log("🏁 Stock loading finished");
    }
  };

  useEffect(() => {
    console.log("🎯 StockPage useEffect triggered");
    loadStockData();
  }, []);

  // Apply filters
  useEffect(() => {
    let result = stockItems;

    // Apply search filter
    if (searchQuery) {
      result = result.filter(item =>
        item.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.location.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    if (categoryFilter !== "all") {
      result = result.filter(item => item.category === categoryFilter);
    }

    // Apply location filter
    if (locationFilter !== "all") {
      result = result.filter(item => item.location === locationFilter);
    }

    // Apply selected location filter
    if (selectedLocation && selectedLocation !== "all") {
      result = result.filter(item => item.location === selectedLocation);
    }

    // Apply selected item filter
    if (selectedItem && selectedItem !== "all") {
      const itemId = selectedItem.split(' ')[0];
      result = result.filter(item => item.id === itemId);
    }

    setFilteredItems(result);
    setTotalPages(Math.ceil(result.length / itemsPerPage));
    setCurrentPage(1); // Reset to first page when filters change
  }, [stockItems, searchQuery, categoryFilter, locationFilter, selectedLocation, selectedItem, itemsPerPage]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle category filter change
  const handleCategoryChange = (value: string) => {
    setCategoryFilter(value);
  };

  // Handle location filter change
  const handleLocationChange = (value: string) => {
    setLocationFilter(value);
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = filteredItems.map(item => ({
      'ID': item.id,
      'Consumable': item.name,
      'Category': item.category,
      'Location': item.location,
      'In Stock': item.quantity,
    }));

    const headers = ['ID', 'Consumable', 'Category', 'Location', 'In Stock'];
    exportData(exportableData, format, 'stock', 'Stock Inventory', headers);
  };

  // Get current items for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredItems.slice(indexOfFirstItem, indexOfLastItem);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setCategoryFilter("all");
    setLocationFilter("all");
    setSelectedLocation("all");
    setSelectedItem("all");
  };

  console.log("🎨 About to render Stock page with state:", { 
    loading, 
    error, 
    stockItemsCount: stockItems.length,
    filteredItemsCount: filteredItems.length 
  });

  if (loading) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>
          
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading Stock Data...
              </div>
              <p className="mt-2 text-gray-600">Please wait while we fetch the inventory data.</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="mb-4">
            <Link to="/transactions/consumables">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Consumable Transactions
              </Button>
            </Link>
          </div>
          
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                <div className="flex items-center mb-4">
                  <AlertTriangle className="h-6 w-6 text-red-500 mr-2" />
                  <h3 className="text-lg font-semibold text-red-800">Error Loading Stock Data</h3>
                </div>
                <p className="text-red-700 mb-4">{error}</p>
                <Button onClick={loadStockData} className="bg-blue-500 hover:bg-blue-600">
                  <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Retry
                </Button>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <PageTitle
          title="Stock"
          description="View and manage inventory stock levels"
          breadcrumbs={
            <>
              <Link to="/transactions" className="hover:underline">Transactions</Link>
              <span className="mx-2">›</span>
              <Link to="/transactions/consumables" className="hover:underline">Consumables</Link>
              <span className="mx-2">›</span>
              <span>Stock</span>
            </>
          }
        />

        <Card>
          <CardHeader>
            <CardTitle>Stock Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div>
                <Label htmlFor="location-filter">Location</Label>
                <Select
                  value={selectedLocation}
                  onValueChange={setSelectedLocation}
                >
                  <SelectTrigger id="location-filter">
                    <SelectValue placeholder="Select location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    {locations.map(location => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="item-filter">Item</Label>
                <Select
                  value={selectedItem}
                  onValueChange={setSelectedItem}
                >
                  <SelectTrigger id="item-filter">
                    <SelectValue placeholder="Select item" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Items</SelectItem>
                    {stockItems.map(item => (
                      <SelectItem key={item.id} value={`${item.id} ${item.name.substring(0, 20)}`}>
                        {item.id} - {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="category-filter">Category</Label>
                <Select
                  value={categoryFilter}
                  onValueChange={handleCategoryChange}
                >
                  <SelectTrigger id="category-filter">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="w-full"
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Search</Label>
                <Input
                  id="search"
                  placeholder="Search by ID, name, category, or location..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>

              <div className="flex items-end gap-2">
                <Button
                  variant="outline"
                  onClick={loadStockData}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      <FileDown className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleExport('pdf')}>
                      Export as PDF
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('csv')}>
                      Export as CSV
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('excel')}>
                      Export as Excel
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExport('clipboard')}>
                      Copy to Clipboard
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Stock Inventory</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Consumable</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead className="text-right">In Stock</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.length > 0 ? (
                  currentItems.map((item, index) => (
                    <TableRow key={`${item.id}-${index}`}>
                      <TableCell className="font-mono text-sm">{item.id}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">{item.category}</Badge>
                      </TableCell>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>{item.location}</TableCell>
                      <TableCell className="text-right">
                        <Badge 
                          variant={item.quantity > 10 ? "default" : item.quantity > 0 ? "secondary" : "destructive"}
                        >
                          {item.quantity}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      {searchQuery || categoryFilter !== 'all' || locationFilter !== 'all' || 
                       selectedLocation !== 'all' || selectedItem !== 'all'
                        ? 'No matching stock items found' 
                        : 'No stock data available'}
                    </TableCell>
                  </TableRow>
                )}
                {currentItems.length > 0 && (
                  <TableRow className="bg-gray-50 font-bold">
                    <TableCell colSpan={4}>Total Items Displayed</TableCell>
                    <TableCell className="text-right">
                      {currentItems.reduce((sum, item) => sum + item.quantity, 0)}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {filteredItems.length > itemsPerPage && (
              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-gray-500">
                  Showing {currentItems.length > 0 ? indexOfFirstItem + 1 : 0}-{Math.min(indexOfLastItem, filteredItems.length)} of {filteredItems.length} entries
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => paginate(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-3 text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => paginate(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Summary Card */}
        <Card>
          <CardHeader>
            <CardTitle>Stock Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Total Items</p>
                <p className="text-2xl font-bold">{filteredItems.length}</p>
                <p className="text-xs text-gray-400">Unique Items</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Total Quantity</p>
                <p className="text-2xl font-bold text-blue-600">
                  {filteredItems.reduce((sum, item) => sum + item.quantity, 0)}
                </p>
                <p className="text-xs text-gray-400">Units in Stock</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Categories</p>
                <p className="text-2xl font-bold text-green-600">{categories.length}</p>
                <p className="text-xs text-gray-400">Item Categories</p>
              </div>
              <div className="border rounded-lg p-4 text-center">
                <p className="text-sm text-gray-500">Locations</p>
                <p className="text-2xl font-bold text-purple-600">{locations.length}</p>
                <p className="text-xs text-gray-400">Storage Locations</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div style={{ 
          marginTop: '20px', 
          padding: '10px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '4px',
          fontSize: '12px',
          color: '#666'
        }}>
          🛠️ Debug Info: {stockItems.length} stock items loaded from database! Filtered: {filteredItems.length}
        </div>

        {/* Toast Container */}
        <ToastContainer />
      </div>
    </AppLayout>
  );
}