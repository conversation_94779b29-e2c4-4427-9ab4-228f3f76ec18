"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const uuid_1 = require("uuid");
const CageManagement_1 = require("../models/CageManagement");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// Get all cages
router.get('/', auth_1.authenticateToken, async (req, res) => {
    try {
        const cages = await CageManagement_1.CageManagement.findAll({
            order: [['cage_name', 'ASC']]
        });
        res.json({
            success: true,
            data: {
                cages: cages,
                count: cages.length
            }
        });
    }
    catch (error) {
        console.error('Get cages error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get cage by ID
router.get('/:id', auth_1.authenticateToken, async (req, res) => {
    try {
        const cage = await CageManagement_1.CageManagement.findByPk(req.params.id);
        if (!cage) {
            res.status(404).json({
                success: false,
                message: 'Cage not found'
            });
            return;
        }
        res.json({
            success: true,
            data: { cage }
        });
    }
    catch (error) {
        console.error('Get cage error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Create new cage
router.post('/', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const { cage_name, current_location, status = 'Available', capacity = 0, current_item_count = 0, assigned_to, precinct, cage_type, qr_code, Item_id, notes, received_by, expected_return_date } = req.body;
        // Validate input
        if (!cage_name || !cage_name.trim()) {
            res.status(400).json({
                success: false,
                message: 'Cage name is required'
            });
            return;
        }
        if (!current_location || !current_location.trim()) {
            res.status(400).json({
                success: false,
                message: 'Current location is required'
            });
            return;
        }
        // Check if cage already exists
        const existingCage = await CageManagement_1.CageManagement.findOne({
            where: { cage_name: cage_name.trim() }
        });
        if (existingCage) {
            res.status(409).json({
                success: false,
                message: 'Cage with this name already exists'
            });
            return;
        }
        // Create new cage
        const cage = await CageManagement_1.CageManagement.create({
            cage_id: (0, uuid_1.v4)(),
            cage_name: cage_name.trim(),
            current_location: current_location.trim(),
            status,
            capacity: Number(capacity) || 0,
            current_item_count: Number(current_item_count) || 0,
            last_updated: new Date(),
            assigned_to: assigned_to?.trim() || undefined,
            precinct: precinct?.trim() || undefined,
            cage_type: cage_type?.trim() || undefined,
            qr_code: qr_code?.trim() || undefined,
            Item_id: Item_id?.trim() || undefined,
            notes: notes?.trim() || undefined,
            received_by: received_by?.trim() || undefined,
            expected_return_date: expected_return_date ? new Date(expected_return_date) : undefined
        });
        res.status(201).json({
            success: true,
            message: 'Cage created successfully',
            data: { cage }
        });
    }
    catch (error) {
        console.error('Create cage error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Update cage
router.put('/:id', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const { cage_name, current_location, status, capacity, current_item_count, assigned_to, precinct, cage_type, qr_code, Item_id, notes, received_by, expected_return_date } = req.body;
        const cage = await CageManagement_1.CageManagement.findByPk(req.params.id);
        if (!cage) {
            res.status(404).json({
                success: false,
                message: 'Cage not found'
            });
            return;
        }
        // Validate input
        if (!cage_name || !cage_name.trim()) {
            res.status(400).json({
                success: false,
                message: 'Cage name is required'
            });
            return;
        }
        if (!current_location || !current_location.trim()) {
            res.status(400).json({
                success: false,
                message: 'Current location is required'
            });
            return;
        }
        // Check if another cage with the same name exists (excluding current one)
        const existingCage = await CageManagement_1.CageManagement.findOne({
            where: {
                cage_name: cage_name.trim(),
                cage_id: { [require('sequelize').Op.ne]: req.params.id }
            }
        });
        if (existingCage) {
            res.status(409).json({
                success: false,
                message: 'Cage with this name already exists'
            });
            return;
        }
        // Update cage
        await cage.update({
            cage_name: cage_name.trim(),
            current_location: current_location.trim(),
            status,
            capacity: Number(capacity) || 0,
            current_item_count: Number(current_item_count) || 0,
            last_updated: new Date(),
            assigned_to: assigned_to?.trim() || undefined,
            precinct: precinct?.trim() || undefined,
            cage_type: cage_type?.trim() || undefined,
            qr_code: qr_code?.trim() || undefined,
            Item_id: Item_id?.trim() || undefined,
            notes: notes?.trim() || undefined,
            received_by: received_by?.trim() || undefined,
            expected_return_date: expected_return_date ? new Date(expected_return_date) : undefined
        });
        res.json({
            success: true,
            message: 'Cage updated successfully',
            data: { cage }
        });
    }
    catch (error) {
        console.error('Update cage error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Check out cage
router.put('/:id/checkout', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const { precinct, assigned_to, expected_return_date } = req.body;
        const cage = await CageManagement_1.CageManagement.findByPk(req.params.id);
        if (!cage) {
            res.status(404).json({
                success: false,
                message: 'Cage not found'
            });
            return;
        }
        await cage.update({
            status: 'Checked Out',
            precinct: precinct?.trim() || undefined,
            assigned_to: assigned_to?.trim() || undefined,
            expected_return_date: expected_return_date ? new Date(expected_return_date) : undefined,
            received_by: undefined, // Clear received_by on checkout
            last_updated: new Date()
        });
        res.json({
            success: true,
            message: `Cage ${cage.cage_id} checked out successfully`,
            data: { cage }
        });
    }
    catch (error) {
        console.error('Check out cage error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Check in cage
router.put('/:id/checkin', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const { returned_by, condition, notes } = req.body;
        const cage = await CageManagement_1.CageManagement.findByPk(req.params.id);
        if (!cage) {
            res.status(404).json({
                success: false,
                message: 'Cage not found'
            });
            return;
        }
        await cage.update({
            status: 'Available', // Set status to Available on check-in
            received_by: returned_by?.trim() || undefined,
            notes: notes?.trim() || undefined,
            expected_return_date: undefined, // Clear expected return date on check-in
            assigned_to: undefined, // Clear assigned_to on check-in
            precinct: undefined, // Clear precinct on check-in
            last_updated: new Date()
        });
        res.json({
            success: true,
            message: `Cage ${cage.cage_id} checked in successfully`,
            data: { cage }
        });
    }
    catch (error) {
        console.error('Check in cage error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Delete cage
router.delete('/:id', auth_1.authenticateToken, (0, auth_1.authorizeRoles)('admin', 'Portal Admin'), async (req, res) => {
    try {
        const cage = await CageManagement_1.CageManagement.findByPk(req.params.id);
        if (!cage) {
            res.status(404).json({
                success: false,
                message: 'Cage not found'
            });
            return;
        }
        await cage.destroy();
        res.json({
            success: true,
            message: 'Cage deleted successfully'
        });
    }
    catch (error) {
        console.error('Delete cage error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
