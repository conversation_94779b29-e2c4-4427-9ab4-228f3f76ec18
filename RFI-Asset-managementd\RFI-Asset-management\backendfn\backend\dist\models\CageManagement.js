"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CageManagement = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
class CageManagement extends sequelize_1.Model {
}
exports.CageManagement = CageManagement;
CageManagement.init({
    cage_id: {
        type: sequelize_1.DataTypes.STRING(50),
        primaryKey: true,
    },
    cage_name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    current_location: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('Available', 'Checked Out', 'In Transit', 'Maintenance'),
        defaultValue: 'Available',
    },
    capacity: {
        type: sequelize_1.DataTypes.INTEGER,
        defaultValue: 0,
    },
    current_item_count: {
        type: sequelize_1.DataTypes.INTEGER,
        defaultValue: 0,
    },
    last_updated: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW,
    },
    assigned_to: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    precinct: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    cage_type: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: true,
    },
    qr_code: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        unique: true,
    },
    Item_id: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        unique: true,
    },
    notes: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    received_by: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
    },
    expected_return_date: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
    },
    createdAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'created_at',
    },
    updatedAt: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
        field: 'updated_at',
    },
}, {
    sequelize: database_1.sequelize,
    tableName: 'cage_management',
    underscored: true,
    timestamps: true,
});
