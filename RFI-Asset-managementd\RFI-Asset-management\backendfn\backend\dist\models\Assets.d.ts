import { Model, Optional } from 'sequelize';
export interface AssetAttributes {
    id: number;
    assetId: string;
    type: string;
    model?: string;
    serialNumber?: string;
    status: 'Available' | 'In Transit' | 'Deployed' | 'Damaged' | 'Under Repair' | 'Retired';
    condition: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
    location: string;
    assignedTo?: string;
    county?: string;
    precinct?: string;
    purchaseDate?: Date;
    warrantyExpiry?: Date;
    lastMaintenance?: Date;
    nextMaintenance?: Date;
    lastChecked?: Date;
    notes?: string;
    specifications?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface AssetCreationAttributes extends Optional<AssetAttributes, 'id' | 'model' | 'serialNumber' | 'assignedTo' | 'county' | 'precinct' | 'purchaseDate' | 'warrantyExpiry' | 'lastMaintenance' | 'nextMaintenance' | 'lastChecked' | 'notes' | 'specifications' | 'createdAt' | 'updatedAt'> {
}
declare class Asset extends Model<AssetAttributes, AssetCreationAttributes> implements AssetAttributes {
    id: number;
    assetId: string;
    type: string;
    model?: string;
    serialNumber?: string;
    status: 'Available' | 'In Transit' | 'Deployed' | 'Damaged' | 'Under Repair' | 'Retired';
    condition: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
    location: string;
    assignedTo?: string;
    county?: string;
    precinct?: string;
    purchaseDate?: Date;
    warrantyExpiry?: Date;
    lastMaintenance?: Date;
    nextMaintenance?: Date;
    lastChecked?: Date;
    notes?: string;
    specifications?: string;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    getSpecificationsData(): any;
    setSpecificationsData(specs: any): void;
}
export default Asset;
