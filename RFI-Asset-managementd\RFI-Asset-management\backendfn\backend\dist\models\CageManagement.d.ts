import { Model, Optional } from 'sequelize';
interface CageManagementAttributes {
    cage_id: string;
    cage_name: string;
    current_location: string;
    status: 'Available' | 'Checked Out' | 'In Transit' | 'Maintenance';
    capacity: number;
    current_item_count: number;
    last_updated: Date;
    assigned_to?: string | null;
    precinct?: string | null;
    cage_type?: string | null;
    qr_code?: string | null;
    Item_id?: string | null;
    notes?: string | null;
    received_by?: string | null;
    expected_return_date?: Date | null;
    createdAt: Date;
    updatedAt: Date;
}
interface CageManagementCreationAttributes extends Optional<CageManagementAttributes, 'createdAt' | 'updatedAt'> {
}
declare class CageManagement extends Model<CageManagementAttributes, CageManagementCreationAttributes> implements CageManagementAttributes {
    cage_id: string;
    cage_name: string;
    current_location: string;
    status: 'Available' | 'Checked Out' | 'In Transit' | 'Maintenance';
    capacity: number;
    current_item_count: number;
    last_updated: Date;
    assigned_to?: string | null;
    precinct?: string | null;
    cage_type?: string | null;
    qr_code?: string | null;
    Item_id?: string | null;
    notes?: string | null;
    received_by?: string | null;
    expected_return_date?: Date | null;
    readonly createdAt: Date;
    readonly updatedAt: Date;
}
export { CageManagement, CageManagementAttributes, CageManagementCreationAttributes };
