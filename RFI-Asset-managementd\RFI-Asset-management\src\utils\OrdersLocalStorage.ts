// Types for Orders
export interface OrderItem {
  id: string;
  itemId: string;
  itemName: string;
  quantity: number;
  unitCost: number;
  amount: number;
}

export interface OtherCost {
  id: string;
  type: string;
  amount: number;
}

export interface Order {
  id: string;
  refNo: string;
  date: string;
  vendor: string;
  vendorContact?: string;
  vendorEmail?: string;
  vendorPhone?: string;
  location: string;
  Location?: string; // Backend uses capital L
  locationName?: string; // Human-readable location name from backend
  consumable?: string; // Added for backend compatibility
  qty?: number; // Added for backend compatibility
  note?: string;
  items: OrderItem[];
  otherCosts: OtherCost[];
  subtotal: number;
  total: number;
  status: 'Pending' | 'Processing' | 'In Transit' | 'Delivered' | 'Cancelled' | 'pending' | 'approved' | 'completed' | 'cancelled';
  createdBy: string;
  modifiedBy?: string;
}

// Local Storage Keys
const ORDERS_KEY = 'app_orders';

// Get all orders from local storage
export function getOrders(): Order[] {
  const ordersJson = localStorage.getItem(ORDERS_KEY);
  if (!ordersJson) {
    return [];
  }
  return JSON.parse(ordersJson);
}

// Get a single order by ID
export function getOrder(id: string): Order | undefined {
  const orders = getOrders();
  return orders.find(order => order.id === id);
}

// Add a new order
export function addOrder(orderData: Omit<Order, 'id'>): Order {
  const orders = getOrders();
  
  // Generate a new ID (simple incremental ID for demo purposes)
  const lastOrder = orders.length > 0 ? orders[0] : null;
  const newId = lastOrder ? 
    String(parseInt(lastOrder.id.replace(/\D/g, '')) + 1).padStart(3, '0') : 
    '001';
  
  const newOrder: Order = {
    ...orderData,
    id: newId
  };
  
  // Add to beginning of array to show newest first
  orders.unshift(newOrder);
  localStorage.setItem(ORDERS_KEY, JSON.stringify(orders));
  
  return newOrder;
}

// Update an existing order
export function updateOrder(updatedOrder: Order): Order {
  const orders = getOrders();
  const index = orders.findIndex(o => o.id === updatedOrder.id);
  
  if (index !== -1) {
    orders[index] = updatedOrder;
    localStorage.setItem(ORDERS_KEY, JSON.stringify(orders));
  }
  
  return updatedOrder;
}

// Delete an order
export function deleteOrder(id: string): boolean {
  const orders = getOrders();
  const filteredOrders = orders.filter(o => o.id !== id);
  
  if (filteredOrders.length < orders.length) {
    localStorage.setItem(ORDERS_KEY, JSON.stringify(filteredOrders));
    return true;
  }
  
  return false;
}

// Search orders
export function searchOrders(query: string): Order[] {
  if (!query) return getOrders();
  
  const orders = getOrders();
  const lowerQuery = query.toLowerCase();
  
  return orders.filter(order => 
    order.id.toLowerCase().includes(lowerQuery) ||
    order.refNo.toLowerCase().includes(lowerQuery) ||
    order.vendor.toLowerCase().includes(lowerQuery) ||
    order.location.toLowerCase().includes(lowerQuery)
  );
}

// Filter orders by status
export function filterOrdersByStatus(status: string): Order[] {
  if (status === 'all') return getOrders();
  
  const orders = getOrders();
  return orders.filter(order => order.status.toLowerCase() === status.toLowerCase());
}

// Get order statistics
export function getOrderStats() {
  const orders = getOrders();
  
  // Get current month
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  
  // Filter orders for current month
  const thisMonthOrders = orders.filter(order => {
    const orderDate = new Date(order.date);
    return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear;
  });
  
  // Count by status
  const pendingOrders = orders.filter(order => order.status === 'Pending').length;
  const processingOrders = orders.filter(order => order.status === 'Processing').length;
  const inTransitOrders = orders.filter(order => order.status === 'In Transit').length;
  const deliveredOrders = orders.filter(order => order.status === 'Delivered').length;
  
  return {
    total: orders.length,
    thisMonth: thisMonthOrders.length,
    pending: pendingOrders,
    processing: processingOrders,
    inTransit: inTransitOrders,
    delivered: deliveredOrders
  };
}

// Prepare orders for export
export function prepareOrdersForExport(orders: Order[]) {
  return orders.map(order => ({
    'Order ID': order.id,
    'Reference No': order.refNo,
    'Date': order.date,
    'Vendor': order.vendor,
    'Location': order.location,
    'Items': order.items.length,
    'Total': `$${order.total.toFixed(2)}`,
    'Status': order.status,
    'Created By': order.createdBy
  }));
}

// Get other cost types
export function getOtherCostTypes(): string[] {
  return [
    'Shipping & Handling',
    'Tax',
    'Insurance',
    'Discount',
    'Other'
  ];
}
