"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Vendors_1 = __importDefault(require("../models/Vendors"));
const router = (0, express_1.Router)();
// Get all vendors with optional search
router.get('/', async (req, res) => {
    try {
        const { search, status, page = 1, limit = 50 } = req.query;
        const whereClause = {};
        // Add search filter
        if (search) {
            const { Op } = require('sequelize');
            whereClause[Op.or] = [
                { companyName: { [Op.like]: `%${search}%` } },
                { firstName: { [Op.like]: `%${search}%` } },
                { lastName: { [Op.like]: `%${search}%` } },
                { email: { [Op.like]: `%${search}%` } },
                { phone: { [Op.like]: `%${search}%` } },
            ];
        }
        // Add status filter
        if (status !== undefined) {
            whereClause.status = status === 'true';
        }
        const offset = (Number(page) - 1) * Number(limit);
        const { count, rows } = await Vendors_1.default.findAndCountAll({
            where: whereClause,
            limit: Number(limit),
            offset,
            order: [['companyName', 'ASC']],
        });
        // Parse JSON fields for response
        const vendorsWithParsedData = rows.map((vendor) => {
            const vendorData = vendor.toJSON();
            return {
                ...vendorData,
                secondaryContacts: vendor.getSecondaryContactsData(),
                addresses: vendor.getAddressesData(),
            };
        });
        res.json({
            success: true,
            data: {
                vendors: vendorsWithParsedData,
                pagination: {
                    total: count,
                    page: Number(page),
                    limit: Number(limit),
                    totalPages: Math.ceil(count / Number(limit)),
                },
            },
        });
    }
    catch (error) {
        console.error('Error fetching vendors:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch vendors',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Get vendor by ID
router.get('/:id', async (req, res) => {
    try {
        const vendor = await Vendors_1.default.findByPk(req.params.id);
        if (!vendor) {
            res.status(404).json({
                success: false,
                message: 'Vendor not found',
            });
            return;
        }
        const vendorData = vendor.toJSON();
        const responseData = {
            ...vendorData,
            secondaryContacts: vendor.getSecondaryContactsData(),
            addresses: vendor.getAddressesData(),
        };
        res.json({
            success: true,
            data: responseData,
        });
    }
    catch (error) {
        console.error('Error fetching vendor:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch vendor',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Create new vendor
router.post('/', async (req, res) => {
    try {
        const { companyName, title, firstName, lastName, phone, email, status = true, secondaryContacts = [], addresses = [] } = req.body;
        // Validate required fields
        if (!companyName || !firstName || !lastName || !phone || !email) {
            res.status(400).json({
                success: false,
                message: 'Missing required fields: companyName, firstName, lastName, phone, email',
            });
            return;
        }
        // Check if email already exists
        const existingVendor = await Vendors_1.default.findOne({ where: { email } });
        if (existingVendor) {
            res.status(409).json({
                success: false,
                message: 'Vendor with this email already exists',
            });
            return;
        }
        const vendor = await Vendors_1.default.create({
            companyName,
            title,
            firstName,
            lastName,
            phone,
            email,
            status,
        });
        // Set secondary contacts and addresses if provided
        if (secondaryContacts.length > 0) {
            vendor.setSecondaryContactsData(secondaryContacts);
        }
        if (addresses.length > 0) {
            vendor.setAddressesData(addresses);
        }
        if (secondaryContacts.length > 0 || addresses.length > 0) {
            await vendor.save();
        }
        const responseData = {
            ...vendor.toJSON(),
            secondaryContacts: vendor.getSecondaryContactsData(),
            addresses: vendor.getAddressesData(),
        };
        res.status(201).json({
            success: true,
            data: responseData,
            message: 'Vendor created successfully',
        });
    }
    catch (error) {
        console.error('Error creating vendor:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create vendor',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Update vendor
router.put('/:id', async (req, res) => {
    try {
        const { companyName, title, firstName, lastName, phone, email, status, secondaryContacts, addresses } = req.body;
        const vendor = await Vendors_1.default.findByPk(req.params.id);
        if (!vendor) {
            res.status(404).json({
                success: false,
                message: 'Vendor not found',
            });
            return;
        }
        // Check if email already exists for another vendor
        if (email && email !== vendor.email) {
            const { Op } = require('sequelize');
            const existingVendor = await Vendors_1.default.findOne({
                where: {
                    email,
                    id: { [Op.ne]: req.params.id },
                },
            });
            if (existingVendor) {
                res.status(409).json({
                    success: false,
                    message: 'Vendor with this email already exists',
                });
                return;
            }
        }
        // Update basic vendor fields
        await vendor.update({
            companyName: companyName || vendor.companyName,
            title: title !== undefined ? title : vendor.title,
            firstName: firstName || vendor.firstName,
            lastName: lastName || vendor.lastName,
            phone: phone || vendor.phone,
            email: email || vendor.email,
            status: status !== undefined ? status : vendor.status,
        });
        // Update secondary contacts if provided
        if (secondaryContacts !== undefined) {
            vendor.setSecondaryContactsData(secondaryContacts);
        }
        // Update addresses if provided
        if (addresses !== undefined) {
            vendor.setAddressesData(addresses);
        }
        // Save if JSON fields were updated
        if (secondaryContacts !== undefined || addresses !== undefined) {
            await vendor.save();
        }
        const responseData = {
            ...vendor.toJSON(),
            secondaryContacts: vendor.getSecondaryContactsData(),
            addresses: vendor.getAddressesData(),
        };
        res.json({
            success: true,
            data: responseData,
            message: 'Vendor updated successfully',
        });
    }
    catch (error) {
        console.error('Error updating vendor:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update vendor',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Delete vendor
router.delete('/:id', async (req, res) => {
    try {
        const vendor = await Vendors_1.default.findByPk(req.params.id);
        if (!vendor) {
            res.status(404).json({
                success: false,
                message: 'Vendor not found',
            });
            return;
        }
        await vendor.destroy();
        res.json({
            success: true,
            message: 'Vendor deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting vendor:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete vendor',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Update secondary contacts
router.put('/:id/secondary-contacts', async (req, res) => {
    try {
        const { secondaryContacts } = req.body;
        const vendor = await Vendors_1.default.findByPk(req.params.id);
        if (!vendor) {
            res.status(404).json({
                success: false,
                message: 'Vendor not found',
            });
            return;
        }
        vendor.setSecondaryContactsData(secondaryContacts || []);
        await vendor.save();
        res.json({
            success: true,
            data: vendor.getSecondaryContactsData(),
            message: 'Secondary contacts updated successfully',
        });
    }
    catch (error) {
        console.error('Error updating secondary contacts:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update secondary contacts',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Update addresses
router.put('/:id/addresses', async (req, res) => {
    try {
        const { addresses } = req.body;
        const vendor = await Vendors_1.default.findByPk(req.params.id);
        if (!vendor) {
            res.status(404).json({
                success: false,
                message: 'Vendor not found',
            });
            return;
        }
        vendor.setAddressesData(addresses || []);
        await vendor.save();
        res.json({
            success: true,
            data: vendor.getAddressesData(),
            message: 'Addresses updated successfully',
        });
    }
    catch (error) {
        console.error('Error updating addresses:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update addresses',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Get secondary contacts
router.get('/:id/secondary-contacts', async (req, res) => {
    try {
        const vendor = await Vendors_1.default.findByPk(req.params.id);
        if (!vendor) {
            res.status(404).json({
                success: false,
                message: 'Vendor not found',
            });
            return;
        }
        res.json({
            success: true,
            data: vendor.getSecondaryContactsData(),
        });
    }
    catch (error) {
        console.error('Error fetching secondary contacts:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch secondary contacts',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Get addresses
router.get('/:id/addresses', async (req, res) => {
    try {
        const vendor = await Vendors_1.default.findByPk(req.params.id);
        if (!vendor) {
            res.status(404).json({
                success: false,
                message: 'Vendor not found',
            });
            return;
        }
        res.json({
            success: true,
            data: vendor.getAddressesData(),
        });
    }
    catch (error) {
        console.error('Error fetching addresses:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch addresses',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
exports.default = router;
